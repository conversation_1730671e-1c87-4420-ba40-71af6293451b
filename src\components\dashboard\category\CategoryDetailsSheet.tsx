import React, { useState, useCallback, useEffect } from "react";
import { She<PERSON>, SheetContent } from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Edit, Trash2, Info, Loader2, Search } from "lucide-react";
import { AddPatientsSheet } from "./AddPatientsSheet";
import { type Category } from "@/components/ui-components/CategoryListCard";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import {
	useCategoryDetail,
	useCategoryClients,
	useAddClientsToCategory,
	useDetachClientsFromCategory,
} from "@/hooks/useCategories";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";

interface Client {
	id: string;
	name: string;
	email: string;
	phone: string;
	status: "Active" | "Inactive";
	initials: string;
	profile_picture_url?: string;
}

interface CategoryDetailsSheetProps {
	open: boolean;
	onClose: () => void;
	category?: Category | null;
}

export const CategoryDetailsSheet: React.FC<CategoryDetailsSheetProps> = ({
	open,
	onClose,
	category,
}) => {
	const [activeTab, setActiveTab] = useState("information");
	const [searchQuery, setSearchQuery] = useState("");
	const [showAddPatientsSheet, setShowAddPatientsSheet] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [clientToDelete, setClientToDelete] = useState<Client | null>(null);

	const {
		data: categoryDetailData,
		isLoading: isLoadingCategoryDetail,
		error: categoryDetailError,
	} = useCategoryDetail(category?.id || "", {
		enabled: !!category?.id && open,
	});

	const [currentPage, setCurrentPage] = useState(1);
	const clientsPerPage = 7;

	const {
		data: categoryClientsData,
		isLoading: isLoadingClients,
		error: clientsError,
		refetch: refetchClients,
	} = useCategoryClients(
		category?.id || "",
		{
			page: currentPage,
			limit: clientsPerPage,
		},
		{
			enabled: !!category?.id && open && activeTab === "clients",
		}
	);

	const addClientsToCategory = useAddClientsToCategory({
		onSuccess: () => {
			setShowAddPatientsSheet(false);
			refetchClients(); 
		},
	});

	const detachClientsFromCategory = useDetachClientsFromCategory({
		onSuccess: () => {
			setIsDeleteDialogOpen(false);
			setClientToDelete(null);
			refetchClients(); 
		},
	});

	const transformCategoryClientToClient = (apiClient: any): Client => {
		const initials =
			`${apiClient.first_name?.[0] || ""}${apiClient.last_name?.[0] || ""}`.toUpperCase();
		return {
			id: apiClient.id.toString(),
			name:
				apiClient.full_name ||
				`${apiClient.first_name} ${apiClient.last_name}`,
			email: apiClient.email,
			phone: apiClient.phone_number,
			status: apiClient.is_active ? "Active" : "Inactive",
			initials,
			profile_picture_url: apiClient.profile_picture_url,
		};
	};

	const clients: Client[] = categoryClientsData?.data
		? categoryClientsData.data.map(transformCategoryClientToClient)
		: [];

	const filteredClients = clients.filter((client) => {
		if (!searchQuery.trim()) return true;
		const query = searchQuery.toLowerCase();
		return (
			client.name.toLowerCase().includes(query) ||
			client.email.toLowerCase().includes(query) ||
			client.phone.toLowerCase().includes(query)
		);
	});

	const totalPages =
		categoryClientsData?.meta?.total_pages ||
		Math.ceil(filteredClients.length / clientsPerPage);
	const startIndex = (currentPage - 1) * clientsPerPage;
	const endIndex = startIndex + clientsPerPage;
	const currentClients = categoryClientsData?.data
		? filteredClients
		: filteredClients.slice(startIndex, endIndex);

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		if (currentPage < totalPages) {
			handlePageChange(currentPage + 1);
		}
	}, [currentPage, totalPages, handlePageChange]);

	useEffect(() => {
		setCurrentPage(1);
	}, [activeTab, searchQuery]);

	useEffect(() => {
		if (activeTab !== "clients") {
			setSearchQuery("");
		}
	}, [activeTab]);

	useEffect(() => {
		if (categoryClientsData?.meta && activeTab === "clients") {
			const totalItems = categoryClientsData.meta.total || 0;
			const itemsPerPage = clientsPerPage;
			const maxPage = Math.ceil(totalItems / itemsPerPage);

			if (currentPage > maxPage && maxPage > 0) {
				console.log(
					`CategoryDetailsSheet: Navigating from page ${currentPage} to page ${maxPage}`
				);
				setCurrentPage(maxPage);
			}
		}
	}, [
		categoryClientsData?.meta?.total,
		currentPage,
		clientsPerPage,
		activeTab,
	]);

	if (!category) return null;

	const handleEdit = () => {
		console.log("Edit category:", category.id);
	};

	const handleAddPatients = () => {
		setShowAddPatientsSheet(true);
	};

	const handleAddPatientsToCategory = (patientIds: string[]) => {
		const clientIds = patientIds.map((id) => parseInt(id, 10));

		addClientsToCategory.mutate({
			categoryId: category.id,
			clientIds,
		});
	};

	const handleDeleteClient = (client: Client) => {
		setClientToDelete(client);
		setIsDeleteDialogOpen(true);
	};

	const handleConfirmDelete = () => {
		if (clientToDelete && category) {
			detachClientsFromCategory.mutate({
				categoryId: category.id,
				clientIds: [parseInt(clientToDelete.id, 10)],
			});
		}
	};

	const handleCancelDelete = () => {
		setIsDeleteDialogOpen(false);
		setClientToDelete(null);
	};

	if (isLoadingCategoryDetail) {
		return (
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
					<div className="flex h-full items-center justify-center">
						<div className="flex flex-col items-center gap-3">
							<Loader2 className="h-8 w-8 animate-spin text-gray-500" />
							<p className="text-sm text-gray-500">
								Loading category details...
							</p>
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}

	if (categoryDetailError) {
		return (
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
					<div className="flex h-full items-center justify-center">
						<div className="flex flex-col items-center gap-3">
							<p className="text-sm text-red-500">
								Failed to load category details
							</p>
							<Button variant="outline" onClick={onClose}>
								Close
							</Button>
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}

	const detailedCategory = categoryDetailData?.data;
	const locationData = (detailedCategory as any)?.applied_to || [];

	const tabItems = [
		{ value: "information", label: "Information" },
		{ value: "clients", label: "Clients" },
	];

	return (
		<>
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
					<div className="flex h-full flex-col gap-6">
						<div className="flex items-start justify-between gap-2.5">
							<div className="flex flex-1 flex-col items-start justify-start gap-2.5">
								<div className="flex items-center justify-start gap-2.5">
									<div className="text-base leading-7 font-semibold text-gray-900">
										{(detailedCategory as any)?.name ||
											category.name}
									</div>
									<div
										className="h-3.5 w-3.5 rounded-full"
										style={{
											backgroundColor:
												(detailedCategory as any)
													?.color || category.color,
										}}
									/>
								</div>
								<div className="text-xs leading-none font-normal text-gray-500">
									{(detailedCategory as any)?.description ||
										"No description available"}
								</div>
							</div>
							<div className="flex items-start justify-start gap-2.5">
								<Button
									variant="ghost"
									size="icon"
									onClick={onClose}
									className="h-9 w-9 rounded-md"
								>
									<X className="h-4 w-4" />
								</Button>
							</div>
						</div>

						<div className="flex h-10 items-center justify-start rounded-lg bg-gray-100 p-1">
							{tabItems.map((tab) => (
								<button
									key={tab.value}
									onClick={() => setActiveTab(tab.value)}
									className={`flex h-8 flex-1 items-center justify-center gap-2 rounded-md px-3 py-1 ${
										activeTab === tab.value
											? "bg-white text-gray-900 shadow-sm"
											: "text-gray-600"
									}`}
								>
									<div
										className={`text-center text-xs leading-none ${
											activeTab === tab.value
												? "font-semibold"
												: "font-medium"
										}`}
									>
										{tab.label}
									</div>
								</button>
							))}
						</div>

						<div className="flex flex-1 flex-col gap-6 overflow-y-auto">
							{activeTab === "information" && (
								<>
									<div className="flex flex-col items-start justify-start gap-2">
										<div className="text-sm leading-tight font-semibold text-gray-900">
											Condition Info
										</div>
										<div className="flex w-full items-start justify-start">
											<div className="flex flex-1 flex-col items-start justify-start">
												<div className="flex w-full flex-col items-start justify-center gap-1 border-t border-gray-200 py-3">
													<div className="text-[10px] leading-3 font-normal text-gray-500">
														Category Type
													</div>
													<div className="text-sm leading-tight font-normal text-gray-900">
														{(
															detailedCategory as any
														)?.type ===
														"conditional"
															? "Custom Conditions"
															: "Manual"}
													</div>
												</div>
											</div>
										</div>
									</div>

									<div className="flex flex-col items-start justify-start gap-2">
										<div className="text-sm leading-tight font-semibold text-gray-900">
											Applied To
										</div>
										<div className="flex w-full items-start justify-start">
											<div className="flex flex-1 flex-col items-start justify-start">
												{locationData.length > 0 ? (
													locationData.map(
														(
															location: any,
															index: number
														) => (
															<div
																key={index}
																className="flex w-full flex-col items-start justify-center gap-1 border-t border-gray-200 py-3"
															>
																<div className="text-sm leading-tight font-normal text-gray-900">
																	{
																		location.location
																	}
																</div>
																<div className="flex w-full flex-wrap items-start justify-start gap-2">
																	{location.stations.map(
																		(
																			station: string,
																			stationIndex: number
																		) => (
																			<div
																				key={
																					stationIndex
																				}
																				className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1"
																			>
																				<div className="text-[10px] leading-3 font-medium text-gray-900">
																					{
																						station
																					}
																				</div>
																			</div>
																		)
																	)}
																</div>
															</div>
														)
													)
												) : (
													<div className="flex w-full flex-col items-start justify-center gap-1 border-t border-gray-200 py-3">
														<div className="text-sm leading-tight font-normal text-gray-500">
															No locations
															assigned
														</div>
													</div>
												)}
											</div>
										</div>
									</div>
								</>
							)}

							{activeTab === "clients" && (
								<div className="flex flex-1 flex-col gap-4 overflow-hidden">
									<div className="relative">
										<Search className="absolute top-1/2 left-3 h-3 w-3 -translate-y-1/2 text-gray-400" />
										<Input
											placeholder="Select All Patients"
											value={searchQuery}
											onChange={(e) =>
												setSearchQuery(e.target.value)
											}
											className="h-9 pl-9 text-xs"
										/>
									</div>

									{isLoadingClients ? (
										<div className="flex h-full items-center justify-center">
											<div className="flex flex-col items-center gap-3">
												<Loader2 className="h-8 w-8 animate-spin text-gray-500" />
												<p className="text-sm text-gray-500">
													Loading clients...
												</p>
											</div>
										</div>
									) : clientsError ? (
										<div className="flex h-full items-center justify-center">
											<div className="flex flex-col items-center gap-3">
												<p className="text-sm text-red-500">
													Failed to load clients
												</p>
												<Button
													variant="outline"
													onClick={() =>
														refetchClients()
													}
													size="sm"
												>
													Retry
												</Button>
											</div>
										</div>
									) : currentClients.length === 0 ? (
										<div className="flex h-full items-center justify-center">
											<div className="flex flex-col items-center gap-3">
												<p className="text-sm text-gray-500">
													{searchQuery.trim()
														? `No clients found matching "${searchQuery}"`
														: "No clients found in this category"}
												</p>
											</div>
										</div>
									) : (
										<>
											<div className="flex-1 overflow-y-auto">
												<div className="flex flex-col items-start justify-start gap-1 self-stretch rounded-xl border border-gray-200">
													{currentClients.map(
														(client, index) => (
															<div
																key={client.id}
																className={`bg-Foreground-Base inline-flex items-center justify-start self-stretch ${
																	index > 0
																		? "border-t border-gray-200"
																		: ""
																}`}
															>
																<div className="flex w-72 min-w-20 items-start justify-start gap-3 self-stretch px-3 pt-3.5 pb-3">
																	<div className="relative h-9 w-9 overflow-hidden rounded-full bg-gray-200">
																		{client.profile_picture_url ? (
																			<img
																				src={
																					client.profile_picture_url
																				}
																				alt={
																					client.name
																				}
																				className="h-full w-full object-cover"
																			/>
																		) : (
																			<div className="absolute top-[8px] left-[8px] h-5 w-5 justify-center text-center text-xs leading-none font-medium text-gray-600">
																				{
																					client.initials
																				}
																			</div>
																		)}
																	</div>
																	<div className="inline-flex w-56 flex-col items-start justify-start gap-1">
																		<div className="inline-flex items-center justify-start gap-1 self-stretch">
																			<div className="justify-center text-sm leading-tight font-medium text-gray-900">
																				{
																					client.name
																				}
																			</div>
																			<div className="h-1.5 w-1.5 rounded-full bg-red-500" />
																		</div>
																		<div className="justify-center text-xs leading-none font-normal text-gray-500">
																			{
																				client.email
																			}
																		</div>
																		<div className="justify-center text-xs leading-none font-normal text-gray-500">
																			{
																				client.phone
																			}
																		</div>
																	</div>
																</div>

																<div className="flex min-w-20 flex-1 items-start justify-start gap-3 self-stretch px-3 pt-3.5 pb-3">
																	<div
																		className={`flex items-center justify-center gap-2.5 rounded-md px-2 py-1 ${
																			client.status ===
																			"Active"
																				? "bg-green-100"
																				: "bg-gray-200"
																		}`}
																	>
																		<div className="justify-start text-[10px] leading-3 font-medium text-gray-900">
																			{
																				client.status
																			}
																		</div>
																	</div>
																</div>
																<div className="flex min-w-16 items-start justify-end gap-1.5 self-stretch px-3 pt-3.5 pb-3">
																	{(
																		detailedCategory as any
																	)?.type !==
																		"conditional" && (
																		<button
																			className="flex h-6 w-6 items-center justify-center gap-2 rounded-md bg-gray-100 p-2 transition-colors hover:bg-gray-200"
																			onClick={() =>
																				handleDeleteClient(
																					client
																				)
																			}
																		>
																			<Trash2 className="h-3 w-3 text-gray-500" />
																		</button>
																	)}
																	<button className="flex h-6 w-6 items-center justify-center gap-2 rounded-md bg-gray-100 p-2 transition-colors hover:bg-gray-200">
																		<Info className="h-3 w-3 text-gray-500" />
																	</button>
																</div>
															</div>
														)
													)}
												</div>
											</div>

											{totalPages > 1 && (
												<div className="flex justify-end pt-4">
													<div>
														<Pagination>
															<PaginationContent>
																<PaginationItem>
																	<PaginationPrevious
																		onClick={
																			handlePreviousPage
																		}
																		className={
																			currentPage ===
																			1
																				? "pointer-events-none opacity-50"
																				: "cursor-pointer"
																		}
																	/>
																</PaginationItem>
																{Array.from(
																	{
																		length: totalPages,
																	},
																	(_, i) =>
																		i + 1
																).map(
																	(page) => (
																		<PaginationItem
																			key={
																				page
																			}
																		>
																			<PaginationLink
																				onClick={() =>
																					handlePageChange(
																						page
																					)
																				}
																				isActive={
																					currentPage ===
																					page
																				}
																				className="cursor-pointer"
																			>
																				{
																					page
																				}
																			</PaginationLink>
																		</PaginationItem>
																	)
																)}

																{totalPages >
																	5 &&
																	currentPage <
																		totalPages -
																			2 && (
																		<PaginationItem>
																			<PaginationEllipsis />
																		</PaginationItem>
																	)}

																<PaginationItem>
																	<PaginationNext
																		onClick={
																			handleNextPage
																		}
																		className={
																			currentPage ===
																			totalPages
																				? "pointer-events-none opacity-50"
																				: "cursor-pointer"
																		}
																	/>
																</PaginationItem>
															</PaginationContent>
														</Pagination>
													</div>
												</div>
											)}
										</>
									)}
								</div>
							)}
						</div>

						<div className="flex items-center justify-end gap-3">
							<Button
								variant="outline"
								onClick={onClose}
								className="h-9"
							>
								Close
							</Button>
							{activeTab === "clients" ? (
								<Button
									onClick={handleAddPatients}
									className="h-9 bg-[#005893] hover:bg-[#004a7a]"
								>
									Add Patients
								</Button>
							) : (
								<Button onClick={handleEdit} className="h-9">
									<Edit className="mr-2 h-4 w-4" />
									Edit
								</Button>
							)}
						</div>
					</div>
				</SheetContent>
			</Sheet>

			<AddPatientsSheet
				open={showAddPatientsSheet}
				onClose={() => setShowAddPatientsSheet(false)}
				categoryName={category.name}
				categoryId={category.id}
				onAddPatients={handleAddPatientsToCategory}
				isAddingPatients={addClientsToCategory.isPending}
			/>

			<DeleteConfirmationDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
				title="Remove Patient from Category"
				description={`Are you sure you want to remove ${clientToDelete?.name || "this patient"} from the category? This action cannot be undone.`}
				onConfirm={handleConfirmDelete}
				onCancel={handleCancelDelete}
				confirmText="Remove Patient"
				isLoading={detachClientsFromCategory.isPending}
			/>
		</>
	);
};
