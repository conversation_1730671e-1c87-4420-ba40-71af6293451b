import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { useState } from "react";
import { User, Video, Volume2, Heart, Star } from "lucide-react";
import { ToggleButton } from "./ToggleButton";

const meta: Meta<typeof ToggleButton> = {
	title: "Common/ToggleButton",
	component: ToggleButton,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		onClick: { action: "clicked" },
		icon: {
			control: false,
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

// Interactive template to demonstrate state changes
const InteractiveTemplate = (args: any) => {
	const [isSelected, setIsSelected] = useState(args.isSelected || false);

	return (
		<ToggleButton
			{...args}
			isSelected={isSelected}
			onClick={() => setIsSelected(!isSelected)}
		/>
	);
};

export const Default: Story = {
	render: InteractiveTemplate,
	args: {
		label: "Toggle Me",
		isSelected: false,
	},
};

export const WithIcon: Story = {
	render: InteractiveTemplate,
	args: {
		label: "In Person",
		icon: User,
		isSelected: false,
	},
};

export const WithoutCheckIndicator: Story = {
	render: InteractiveTemplate,
	args: {
		label: "Simple Toggle",
		icon: Heart,
		showCheckIndicator: false,
		isSelected: false,
	},
};

export const IconOnly: Story = {
	render: InteractiveTemplate,
	args: {
		label: "Video Call",
		icon: Video,
		showCheckIndicator: false,
		isSelected: false,
	},
};

export const Selected: Story = {
	render: InteractiveTemplate,
	args: {
		label: "Audio Call",
		icon: Volume2,
		isSelected: true,
	},
};

export const Disabled: Story = {
	args: {
		label: "Disabled Button",
		icon: Star,
		isSelected: false,
		disabled: true,
		onClick: () => {},
	},
};

// Group example showing multiple buttons
export const Group: Story = {
	render: () => {
		const [selectedTypes, setSelectedTypes] = useState<string[]>(["video"]);

		const meetingTypes = [
			{ id: "in-person", label: "In Person", icon: User },
			{ id: "video", label: "Video", icon: Video },
			{ id: "audio", label: "Audio", icon: Volume2 },
		];

		const toggleSelection = (typeId: string) => {
			setSelectedTypes((prev) =>
				prev.includes(typeId)
					? prev.filter((id) => id !== typeId)
					: [...prev, typeId]
			);
		};

		return (
			<div className="flex w-[500px] gap-2">
				{meetingTypes.map((type) => (
					<ToggleButton
						key={type.id}
						label={type.label}
						icon={type.icon}
						isSelected={selectedTypes.includes(type.id)}
						onClick={() => toggleSelection(type.id)}
					/>
				))}
			</div>
		);
	},
	parameters: {
		docs: {
			description: {
				story: "Example showing multiple toggle buttons working together in a group.",
			},
		},
	},
};
