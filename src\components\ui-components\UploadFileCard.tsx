import * as React from "react";
import { cn } from "@/lib/utils";
import { FileText, Trash2 } from "lucide-react";

export interface UploadedFileCardProps
	extends React.HTMLAttributes<HTMLDivElement> {
	fileName: string;
	fileType?: string;
	fileSize: string;
	onRemove?: () => void;
	variant?: "default" | "compact";
	icon?: React.ReactNode;
}

const UploadedFileCard = React.forwardRef<
	HTMLDivElement,
	UploadedFileCardProps
>(
	(
		{
			className,
			fileName,
			fileType,
			fileSize,
			onRemove,
			variant = "default",
			icon,
			...props
		},
		ref
	) => {
		const displayName = fileType ? `${fileName}.${fileType}` : fileName;

		return (
			<div
				ref={ref}
				className={cn(
					"flex items-start justify-between gap-4 overflow-hidden rounded-lg bg-white p-3 outline outline-1 outline-offset-[-1px] outline-[#E4E4E7]",
					variant === "compact" && "w-full max-w-[400px] p-2",
					className
				)}
				{...props}
			>
				<div className="flex gap-4">
					<div className="rounded-md bg-[#F4F4F5] p-2.5">
						<FileText className="h-3.5 w-3.5 text-gray-700" />
					</div>

					<div className="">
						<div className="text-sm leading-tight font-semibold text-gray-900">
							{displayName}
						</div>
						<div className="text-[10px] leading-3 font-normal text-gray-500">
							File size: {fileSize}
						</div>
					</div>
				</div>
				<button
					onClick={onRemove}
					aria-label="Remove file"
					type="button"
				>
					<Trash2 className="h-3.5 w-3.5 text-red-600" />
				</button>
			</div>
		);
	}
);

UploadedFileCard.displayName = "UploadedFileCard";

export { UploadedFileCard };
