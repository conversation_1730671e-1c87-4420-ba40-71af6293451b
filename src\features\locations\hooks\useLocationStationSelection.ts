import { useState } from "react";

interface LocationStationSelectionReturn {
	selectedLocationIds: Set<string>;
	selectedStationIds: Set<string>;
	handleLocationSelection: (locationId: string, checked: boolean) => void;
	handleStationSelection: (stationId: string, checked: boolean) => void;
	clearSelections: () => void;
	getSelectedLocations: () => string[];
	getSelectedStations: () => string[];
}

export function useLocationStationSelection(): LocationStationSelectionReturn {
	const [selectedLocationIds, setSelectedLocationIds] = useState<Set<string>>(
		new Set()
	);
	const [selectedStationIds, setSelectedStationIds] = useState<Set<string>>(
		new Set()
	);

	const handleLocationSelection = (locationId: string, checked: boolean) => {
		setSelectedLocationIds((prev) => {
			const newSet = new Set(prev);
			if (checked) {
				newSet.add(locationId);
			} else {
				newSet.delete(locationId);
			}
			return newSet;
		});
	};

	const handleStationSelection = (stationId: string, checked: boolean) => {
		setSelectedStationIds((prev) => {
			const newSet = new Set(prev);
			if (checked) {
				newSet.add(stationId);
			} else {
				newSet.delete(stationId);
			}
			return newSet;
		});
	};

	const clearSelections = () => {
		setSelectedLocationIds(new Set());
		setSelectedStationIds(new Set());
	};

	const getSelectedLocations = () => Array.from(selectedLocationIds);
	const getSelectedStations = () => Array.from(selectedStationIds);

	return {
		selectedLocationIds,
		selectedStationIds,
		handleLocationSelection,
		handleStationSelection,
		clearSelections,
		getSelectedLocations,
		getSelectedStations,
	};
}
