import {
    Sheet,
    Sheet<PERSON>ontent,
    Sheet<PERSON><PERSON><PERSON>,
    Sheet<PERSON>eader,
    Sheet<PERSON>itle,
} from "@/components/ui/sheet"
import { useEffect, useState } from "react";
import AddNewPatient from "./add-new-patient";
import SearchPatient from "./search-patient";
import { But<PERSON> } from "@/components/ui/button";
import FirstAvailable from "./mode/first-available/first-available";
import SelectDateTime from "./mode/select-date-time/select-date-time";
import AutoAssign from "./mode/auto-assign/auto-assign";
import clsx from "clsx";
import { IoIosArrowBack } from "react-icons/io";
import UniversityForm from "./form";
import AppointmentScheduled from "./appointment-scheduled";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { useUIStore } from "@/stores/uiStore";

type Props = {
    open: boolean;
    onOpenChange: (open: boolean) => void
}

export default function BookAppointment({
    open,
    onOpenChange,
}: Props) {
    const [selectedPatient, setSelectedPatient] = useState<number | null>(null);
    const [addNewPatientOpen, setAddNewPatientOpen] = useState<boolean>(false);
    const [mode, setMode] = useState<"first-available" | "date-time" | "auto-assign" | "form" | "scheduled" | null>(null);

    const tabs = [
        { value: "first-available", label: "First Available Option" },
        { value: "date-time", label: "Select Date & Time" },
        { value: "auto-assign", label: "Auto Assign" },
    ];

    return (
        <Sheet open={open} onOpenChange={() => {
            onOpenChange(!open)
            setAddNewPatientOpen(false)
            setSelectedPatient(null)
            setMode(null)
        }}>
            <SheetContent className="z-[1003] py-5 px-2 sm:max-w-[780px] overflow-scroll scrollbar-hide">

                <SheetHeader className="border-b border-[#E4E4E7]">
                    <SheetTitle className="text-2xl mb-1">
                        Schedule an Appointment
                    </SheetTitle>
                    <SheetDescription>
                        {selectedPatient === null ? "Select Patient, Provider and Service to Book an Appointment" : "Add the customer's details to schedule an appointment."}
                    </SheetDescription>
                </SheetHeader>

                {mode === null && ( 
                    <div className="flex items-center justify-between mx-5">
                        <h1 className="font-medium text-xl">Select Patient</h1>
                        <AddNewPatient
                            open={addNewPatientOpen}
                            onOpenChange={setAddNewPatientOpen}
                        >
                            <Button
                                type="button"
                                className="cursor-pointer py-5 font-normal"
                            >
                                Add New Patient
                            </Button>
                        </AddNewPatient>
                    </div>
                )}

                {(selectedPatient !== null && mode !== null) ? ( // if a patient is selected and a mode is set(which will always be if the patient button is double clicked), render this
                    <div className="px-3">
                        {mode !== "scheduled" && (
                            <div className="flex items-start gap-x-2 mb-6">
                                <Button
                                    variant="outline"
                                    className="cursor-pointer size-8"
                                    onClick={() => {
                                        setSelectedPatient(null);
                                        setMode(null);
                                    }}
                                >
                                    <IoIosArrowBack />
                                </Button>

                                <div
                                    className={"w-full border-[1px] py-3.5 px-4 rounded-[10px] flex items-start justify-between transition-all duration-300"}
                                    tabIndex={0}
                                >
                                    <div className="flex items-center justify-center gap-x-3">
                                        <div className="size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                            AB
                                        </div>
                                        <div>
                                            <h1 className="text-[#27272A] text-base">Thomas Edison</h1>
                                            <p className="text-[#71717A] text-sm font-light"><EMAIL></p>
                                        </div>
                                    </div>
                                    <h1 className="text-[#27272A] font-medium text-xl">#UID</h1>
                                </div>
                            </div>
                        )}
                        {mode === "form" ? ( // if users has gotten to the university form step render this
                            <UniversityForm
                                back={() => { setSelectedPatient(null); setMode(null); }}
                                next={() => { setMode("scheduled") }}
                            />
                        ) : mode === "scheduled" ? ( // if the appointment is successfully created render this
                            <AppointmentScheduled

                            />
                        ) : ( // else render other tabs on the user interface
                            <Tabs
                                items={tabs}
                                useRouting={true}
                                searchParamKey="book-appointment-tab"
                                defaultTab="first-available">
                                <TabsContent value="first-available">
                                    <FirstAvailable
                                        back={() => { setSelectedPatient(null); setMode(null); }}
                                        next={() => setMode("form")}
                                    />
                                </TabsContent>
                                <TabsContent value="date-time">
                                    <SelectDateTime
                                        back={() => { setSelectedPatient(null); setMode(null) }}
                                        next={() => setMode("form")}
                                    />
                                </TabsContent>
                                <TabsContent value="auto-assign">
                                    <AutoAssign
                                        back={() => { setSelectedPatient(null); setMode(null); }}
                                        next={() => setMode("form")}
                                    />
                                </TabsContent>
                            </Tabs>
                        )}
                    </div>
                ) // else render the search patient ui
                    : <SearchPatient
                        selectedPatient={selectedPatient}
                        setSelectedPatient={(e) => {
                            setSelectedPatient?.(e)
                        }}
                        onDoubleClick={() => setMode("first-available")}
                    />
                }

            </SheetContent>
        </Sheet>
    )
}