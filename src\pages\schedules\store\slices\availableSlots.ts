import { useQuery } from "@tanstack/react-query";
import * as HttpQuery from "../../http";
import * as AvailableSlotsTypes from "../../types/AvailableSlots";

/**
 * Get available slots
 * @param params - Query parameters
 * @returns Available slots
 */

export function useGetAvailableSlots(
    params: AvailableSlotsTypes.AvailableSlotsQueryParams
) {
    return useQuery({
        queryKey: ['available-slots', params],
        queryFn: () => HttpQuery.APIVersion3GetAvailableSlots(params),
    })
}