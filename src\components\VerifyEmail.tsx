import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { LuX } from "react-icons/lu";
import { Button } from "./ui/button";
import useCustomToast from "./CustomToast";
import { ResendVerificationEmailSlice } from "@/stores/slices/authSlice";
import MailIcon  from "@/assets/icons/mail.svg";


const VerifyEmail: React.FC<{
	email: string;
	showVerifyEmailModal: boolean;
	setShowVerifyEmailModal: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ email, showVerifyEmailModal, setShowVerifyEmailModal }) => {
	const customToast = useCustomToast();
	const resendVerificationEmailMutation = ResendVerificationEmailSlice();

	const onSubmitForm = () => {
		resendVerificationEmailMutation.mutate(
			{
				email,
			},
			{
				onSuccess: () => {
					customToast("Verification email sent successfully 🎉", {
						id: "resend-verification-email",
					});
				},

				onError: () => {
					customToast("Verification email could not be sent 🤕", {
						id: "resend-verification-email",
						type: "error",
					});
				},
			}
		);
	};

	return (
		<Dialog
			open={showVerifyEmailModal}
			onOpenChange={setShowVerifyEmailModal}
		>
			<DialogContent className="max-w-[500px] w-full rounded-lg p-2 py-4">
				<div className="flex justify-between  space-x-2">
					{/* <i className="mgc_invite_line py-1 text-[20px] before:!text-primary" /> */}
					
					<form className="flex flex-col w-full justify-center items-center" onSubmit={onSubmitForm}>
						<div className="flex justify-center items-center">
							<img src={MailIcon} alt="email" />
						</div>
						<DialogTitle className="text-3xl text-[#27272A] mt-8 font-semibold capitalize leading-[30px] -tracking-[1%] ">
							Verify Your Email!
						</DialogTitle>
						<p className="tracking-[-0.1px] font-medium mt-4 text-[14px] leading-[20px] text-[#6D748D]">
							You must verify your email before proceeding to sign in.
						</p>
						<p className="mt-6">Didn't receive an email? Please check your spam folder.</p>
						<div className="flex justify-end w-full">
						<Button
							className="mt-4 self-end text-white"
							type="button"
							// loaderSize={20}
							loading={resendVerificationEmailMutation.isPending}
							disabled={resendVerificationEmailMutation.isPending}
							onClick={onSubmitForm}
						>
							Resend Email
						</Button>
						</div>
						
					</form>
					<button
						className="flex items-start"
						onClick={(e) => {
							e.preventDefault();
							setShowVerifyEmailModal(false);
						}}
					>
						<LuX
							color="#858C95"
							className="cursor-pointer"
							width="20px"
							height="20px"
						/>
					</button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default VerifyEmail;
