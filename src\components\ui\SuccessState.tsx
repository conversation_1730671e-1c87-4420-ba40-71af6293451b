import React from "react";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface SuccessStateProps {
	title: string;
	description: string;
	primaryAction?: {
		label: string;
		onClick: () => void;
	};
	secondaryAction?: {
		label: string;
		onClick: () => void;
	};
	icon?: React.ReactNode;
}

export function SuccessState({
	title,
	description,
	primaryAction,
	secondaryAction,
	icon,
}: SuccessStateProps) {
	return (
		<div className="flex flex-1 flex-col items-center justify-center gap-8">
			<div className="flex flex-col items-center justify-start gap-11">
				<div className="inline-flex items-center justify-start gap-2.5 overflow-hidden rounded-[500px] bg-zinc-500/5 p-8">
					{icon || (
						<div className="relative h-12 w-12 overflow-hidden">
							<Check className="h-8 w-8 absolute left-2 top-3 text-green-600" strokeWidth={4} />
						</div>
					)}
				</div>

				<div className="flex w-full max-w-72 flex-col items-center justify-start gap-3">
					<div className="text-center text-xl font-semibold leading-loose text-foreground">
						{title}
					</div>
					<div className="text-center text-sm font-normal leading-tight text-muted-foreground">
						{description}
					</div>
				</div>
			</div>

			{(primaryAction || secondaryAction) && (
				<div className="inline-flex items-start justify-center gap-3">
					{secondaryAction && (
						<Button
							variant="secondary"
							onClick={secondaryAction.onClick}
							className="h-9 px-4 py-2 text-xs font-medium"
						>
							{secondaryAction.label}
						</Button>
					)}
					{primaryAction && (
						<Button
							onClick={primaryAction.onClick}
							className="h-9 w-20 px-4 py-2 bg-sky-800 text-xs font-medium text-white hover:bg-sky-900"
						>
							{primaryAction.label}
						</Button>
					)}
				</div>
			)}
		</div>
	);
}
