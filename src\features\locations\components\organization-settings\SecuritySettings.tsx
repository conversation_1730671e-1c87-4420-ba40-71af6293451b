import React, { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

const validationFields = ["Field 1", "Field 2", "Field 3"];

const SecuritySettings: React.FC = () => {
	const [patientValidation, setPatientValidation] = useState(true);
	const [patientVerification, setPatientVerification] = useState(false);
	const [validationField1, setValidationField1] = useState<string | string[]>("");
	const [validationField2, setValidationField2] = useState<string | string[]>("");

	return (
		<div className="max-w-2xl">
			<h2 className="mb-4 text-xl font-semibold">Security</h2>
			<Separator />
			<div className="my-6">
				<h3 className="mb-2 font-medium">Patient Validation</h3>
				<div className="mb-2 flex items-center justify-between gap-2">
					<span>Enable Patients Validation</span>
					<div>
						<Switch
							checked={patientValidation}
							onCheckedChange={setPatientValidation}
							className="ml-2"
						/>
						<span className="ml-2 font-semibold text-blue-600">
							{patientValidation ? "On" : "Off"}
						</span>
					</div>
				</div>
				<p className="mb-2 text-xs text-gray-500">
					This will enable the validation process. Patients who are
					not validated will not be allowed to continue.
				</p>
				<div className="my-5 flex flex-col gap-2">
					<div className="flex items-center justify-between gap-2">
						<span>Select Titles for Validation</span>
					</div>
					<p className="mb-1 text-xs text-gray-500">
						Select two fields from below to validate your Patients.
					</p>
					<div className="grid w-full gap-3 md:grid-cols-2">
						<div>
							<Label
								htmlFor="validation-field-1"
								className="mb-1 block text-xs"
							>
								Validation Field 1
							</Label>
							<Select
								value={validationField1}
								onValueChange={setValidationField1} 
							>
								<SelectTrigger
									className="w-full"
									id="validation-field-1"
								>
									<SelectValue placeholder="Select Field" />
								</SelectTrigger>
								<SelectContent>
									{validationFields.map((field) => (
										<SelectItem key={field} value={field}>
											{field}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div>
							<Label
								htmlFor="validation-field-2"
								className="mb-1 block text-xs"
							>
								Validation Field 2
							</Label>
							<Select
								value={validationField2}
								onValueChange={setValidationField2}
							>
								<SelectTrigger
									className="w-full"
									id="validation-field-2"
								>
									<SelectValue placeholder="Select Field" />
								</SelectTrigger>
								<SelectContent>
									{validationFields.map((field) => (
										<SelectItem key={field} value={field}>
											{field}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>
				</div>
			</div>
			<div>
				<h3 className="mb-2 font-medium">Patient Verification</h3>
				<div className="mb-2 flex items-center justify-between gap-2">
					<span>Enable Patients Verifications</span>
					<div>
						<Switch
							checked={patientValidation}
							onCheckedChange={setPatientValidation}
							className="ml-2"
						/>
						<span className="ml-2 font-semibold text-blue-600">
							{patientValidation ? "On" : "Off"}
						</span>
					</div>
				</div>
				<p className="text-xs text-gray-500">
					This will enable the verification step to identify new or
					existing Patients. Users who are not registered will be
					redirected to the link/form pasted in the response section
					based on selection.
				</p>
			</div>
		</div>
	);
};

export default SecuritySettings;
