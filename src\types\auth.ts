export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  createdAt: string
  updatedAt: string
}

export type UserRole = 'admin' | 'doctor' | 'nurse' | 'receptionist'

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface SignInCredentials {
  email: string
  password: string
}

export interface SignUpData {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
}

export interface AuthResponse {
  user: User
  token: string
}

export interface RefreshTokenResponse {
  token: string
} 

export interface SSOVerificationResponse {
	data: {
		id: number;
		name: string;
		role: string;
		products: string[];
		logo_url: string | null;
		domain: string;
		domain_changed: number;
		sso_enable: boolean;
		sso_login_url: string;
	};
	status: boolean;
	message: string;
}
