import React from 'react';
import { ChevronRight, Home, MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { BreadcrumbProps, BreadcrumbItemData } from './types';

const BreadcrumbRoot = React.forwardRef<
  HTMLElement,
  React.ComponentPropsWithoutRef<"nav">
>(({ className, ...props }, ref) => (
  <nav
    ref={ref}
    aria-label="breadcrumb"
    className={cn("", className)}
    {...props}
  />
));
BreadcrumbRoot.displayName = "Breadcrumb";

const BreadcrumbList = React.forwardRef<
  HTMLOListElement,
  React.ComponentPropsWithoutRef<"ol">
>(({ className, ...props }, ref) => (
  <ol
    ref={ref}
    className={cn(
      "flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",
      className
    )}
    {...props}
  />
));
BreadcrumbList.displayName = "BreadcrumbList";

const BreadcrumbItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentPropsWithoutRef<"li">
>(({ className, ...props }, ref) => (
  <li
    ref={ref}
    className={cn("inline-flex items-center gap-1.5", className)}
    {...props}
  />
));
BreadcrumbItem.displayName = "BreadcrumbItem";

const BreadcrumbLink = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentPropsWithoutRef<"a"> & {
    asChild?: boolean;
    children: React.ReactNode;
  }
>(({ className, asChild, children, ...props }, ref) => {
  const Comp = asChild ? React.Fragment : "a";
  return (
    <Comp
      ref={ref}
      className={cn("transition-colors hover:text-foreground", className)}
      {...props}
    >
      {children}
    </Comp>
  );
});
BreadcrumbLink.displayName = "BreadcrumbLink";

const BreadcrumbPage = React.forwardRef<
  HTMLSpanElement,
  React.ComponentPropsWithoutRef<"span">
>(({ className, ...props }, ref) => (
  <span
    ref={ref}
    role="link"
    aria-disabled="true"
    aria-current="page"
    className={cn("font-normal text-foreground", className)}
    {...props}
  />
));
BreadcrumbPage.displayName = "BreadcrumbPage";

const BreadcrumbSeparator = ({ 
  children, 
  className, 
  ...props 
}: React.ComponentProps<"li">) => (
  <li
    role="presentation"
    aria-hidden="true"
    className={cn("[&>svg]:size-3.5", className)}
    {...props}
  >
    {children ?? <ChevronRight />}
  </li>
);
BreadcrumbSeparator.displayName = "BreadcrumbSeparator";

const BreadcrumbEllipsis = ({ 
  className, 
  ...props 
}: React.ComponentProps<"span">) => (
  <span
    role="presentation"
    aria-hidden="true"
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More</span>
  </span>
);
BreadcrumbEllipsis.displayName = "BreadcrumbEllipsis";

/**
 * Breadcrumb component for navigation hierarchy display
 * 
 * @example
 * ```tsx
 * <Breadcrumb
 *   items={[
 *     { label: 'Home', href: '/' },
 *     { label: 'Products', href: '/products' },
 *     { label: 'Item', isCurrentPage: true }
 *   ]}
 *   variant="default"
 *   onItemClick={(item) => router.push(item.href)}
 * />
 * ```
 */
const Breadcrumb = React.forwardRef<HTMLElement, BreadcrumbProps>(
  ({
    items,
    variant = 'default',
    size = 'md',
    separator,
    maxItems,
    showHome = false,
    className,
    onItemClick,
    ...props
  }, ref) => {
    const sizeClasses = {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base'
    };

    const variantClasses = {
      default: '',
      compact: 'gap-0.5',
      'with-icons': 'gap-2',
      separated: 'divide-x divide-border'
    };

   
    const processedItems = React.useMemo(() => {
      let finalItems = [...items];
      
      if (showHome && finalItems.length > 0 && !finalItems[0].icon) {
        finalItems[0] = { ...finalItems[0], icon: <Home className="h-4 w-4" /> };
      }
      
      return finalItems;
    }, [items, showHome]);

  
    const displayItems = React.useMemo(() => {
      if (!maxItems || processedItems.length <= maxItems) {
        return processedItems;
      }

      const firstItem = processedItems[0];
      const lastItems = processedItems.slice(-(maxItems - 2));
      
      return [
        firstItem, 
        { 
          label: '...', 
          isEllipsis: true 
        } as BreadcrumbItemData & { isEllipsis: boolean }, 
        ...lastItems
      ];
    }, [processedItems, maxItems]);

    const handleItemClick = (item: BreadcrumbItemData, index: number) => {
      if (onItemClick && !item.isCurrentPage) {
        onItemClick(item, index);
      }
    };

    const renderSeparator = () => {
      if (variant === 'separated') return null;
      return separator || <ChevronRight className="h-4 w-4" />;
    };

    return (
      <BreadcrumbRoot
        ref={ref}
        className={cn(
          "flex items-center",
          sizeClasses[size],
          variantClasses[variant],
          className
        )}
        {...props}
      >
        <BreadcrumbList>
          {displayItems.map((item, index) => (
            <React.Fragment key={`${item.label}-${index}`}>
              <BreadcrumbItem className={variant === 'separated' ? 'px-2 first:pl-0' : ''}>
                {(item as any).isEllipsis ? (
                  <BreadcrumbEllipsis />
                ) : item.isCurrentPage ? (
                  <BreadcrumbPage className="flex items-center gap-1.5">
                    <span className="flex items-center gap-1.5">
                      {variant === 'with-icons' && item.icon}
                      {item.label}
                    </span>
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink
                    href={item.href}
                    onClick={(e) => {
                      if (onItemClick) {
                        e.preventDefault();
                        handleItemClick(item, index);
                      }
                    }}
                    className="flex items-center gap-1.5 cursor-pointer"
                  >
                    <span className="flex items-center gap-1.5">
                      {variant === 'with-icons' && item.icon}
                      {item.label}
                    </span>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {index < displayItems.length - 1 && variant !== 'separated' && (
                <BreadcrumbSeparator>
                  {renderSeparator()}
                </BreadcrumbSeparator>
              )}
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </BreadcrumbRoot>
    );
  }
);

Breadcrumb.displayName = "Breadcrumb";

export { Breadcrumb };

export {
  BreadcrumbRoot,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis
};
