import {
	Select,
	SelectItem,
	SelectLabel,
	SelectContent,
	SelectGroup,
	SelectTrigger,
} from "@/components/ui/select";
import { X } from "lucide-react";

function CustomSelectValue({ value, onValueChange, placeholder }: any) {
	const selectedValues = Array.isArray(value) ? value : [];

	if (selectedValues.length === 0) {
		return (
			<span className="text-muted-foreground">
				{placeholder || "Select options..."}
			</span>
		);
	}

	const handleRemove = (selectedValue: string, event: React.MouseEvent) => {
		// Prevent all event bubbling and default behavior
		event.preventDefault();
		event.stopPropagation();
		event.nativeEvent.stopImmediatePropagation();

		// Use setTimeout to delay the state update and prevent dropdown from opening
		setTimeout(() => {
			if (onValueChange) {
				const updatedValues = selectedValues.filter(
					(v) => v !== selectedValue
				);
				onValueChange(updatedValues);
			}
		}, 0);
	};

	return (
		<div className="flex flex-wrap gap-1">
			{selectedValues.map((selectedValue: string) => (
				<div
					key={selectedValue}
					className="font-regular flex items-center justify-between gap-x-3 rounded-xl border border-[#005893] bg-[#0058931A] px-4 py-2 text-[#27272A] opacity-100"
				>
					<p className="flex-1">{selectedValue}</p>
					<div
						onClick={(e) => {
							e.preventDefault();
							e.stopPropagation();
							e.nativeEvent.stopImmediatePropagation();
						}}
						onMouseDown={(e) => {
							e.preventDefault();
							e.stopPropagation();
						}}
						onPointerDown={(e) => {
							e.preventDefault();
							e.stopPropagation();
						}}
					>
						<button
							type="button"
							onClick={(e) => handleRemove(selectedValue, e)}
							className="cursor-pointer"
						>
							<X color="#000000" />
						</button>
					</div>
				</div>
			))}
		</div>
	);
}

export function RefactorMultiSelect({
	value,
	setValue,
	placeholder,
	label,
	id,
	options,
}: {
	value: string | string[];
	setValue: (value: string | string[]) => void;
	placeholder?: string;
	label?: string;
	id?: string;
	options: string[];
}) {
	return (
		<Select
			multiSelect
			value={value}
			onValueChange={(value) => {
				setValue(value);
			}}
			aria-label={label}
		>
			<SelectTrigger
				className="scrollbar-hide w-full items-start overflow-scroll py-3 shadow-none"
				style={{
					height: "max-content",
					maxHeight: "10rem",
					display: "flex",
					alignItems: "flex-start",
					position: "relative",
				}}
				id={id}
				aria-label={label}
			>
				<CustomSelectValue
					value={value}
					onValueChange={setValue}
					placeholder={placeholder}
					className="w-[40px] px-0 text-black"
				/>
				<style>
					{`
                    #countryCodeSelect > svg {
                    align-self: flex-start !important;
                    width: 22px !important;
                    height: 22px !important;
                    color: #000000 !important;
                } 
        `}
				</style>
			</SelectTrigger>
			<SelectContent className="z-9999!" aria-label="select-country-code">
				<SelectGroup>
					<SelectLabel className="px-1">Country Codes</SelectLabel>

					{options.map((option) => {
						return (
							<SelectItem
								key={option}
								value={option}
								className="px-8 text-black"
							>
								{option}
							</SelectItem>
						);
					})}
				</SelectGroup>
			</SelectContent>
		</Select>
	);
}
