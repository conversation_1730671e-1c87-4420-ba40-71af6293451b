import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { QRCode } from "./QRCode";

const meta = {
	title: "Common/QRCode",
	component: QRCode,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"A customizable QR Code component built on top of qrcode.react",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		value: {
			control: "text",
			description: "The value to encode in the QR code",
		},
		size: {
			control: { type: "select" },
			options: ["sm", "md", "lg", "xl", 200],
			description: "Predefined size or custom number",
		},
		bgColor: {
			control: "color",
			description: "Background color of the QR code",
		},
		fgColor: {
			control: "color",
			description: "Foreground color of the QR code",
		},
		level: {
			control: { type: "select" },
			options: ["L", "M", "Q", "H"],
			description: "Error correction level",
		},
		title: {
			control: "text",
			description: "Title attribute for accessibility",
		},
		includeMargin: {
			control: "boolean",
			description: "Whether to include margin around the QR code",
		},
	},
} satisfies Meta<typeof QRCode>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		value: "https://app.migranium.com/schedule/university-of-waterloo-17-hellothiswaterloo",
		title: "Migranium Schedule QR Code",
	},
};

export const Small: Story = {
	args: {
		value: "https://example.com",
		size: "sm",
		title: "Small QR Code",
	},
};

export const Large: Story = {
	args: {
		value: "https://example.com",
		size: "lg",
		title: "Large QR Code",
	},
};

export const CustomColors: Story = {
	args: {
		value: "https://example.com",
		bgColor: "#1a1a1a",
		fgColor: "#ffffff",
		title: "Dark QR Code",
	},
};

export const HighErrorCorrection: Story = {
	args: {
		value: "https://example.com/very-long-url-with-lots-of-parameters?param1=value1&param2=value2",
		level: "H",
		title: "High Error Correction QR Code",
	},
};

export const CustomSize: Story = {
	args: {
		value: "https://example.com",
		size: 300,
		title: "Custom Size QR Code",
	},
};

export const WithoutMargin: Story = {
	args: {
		value: "https://example.com",
		includeMargin: false,
		title: "QR Code without margin",
	},
};

export const ColoredQR: Story = {
	args: {
		value: "https://example.com",
		bgColor: "#e0f2fe",
		fgColor: "#0369a1",
		title: "Blue themed QR Code",
	},
};
