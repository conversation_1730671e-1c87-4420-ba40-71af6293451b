import { useState } from "react";
import {
	ChevronLeft,
	Clock,
	Settings,
	Users,
	Calendar,
	UserCheck,
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

interface ImportSettingsStepProps {
	onBack?: () => void;
	onImport?: (selectedSettings: ImportSetting[]) => void;
	providerName?: string;
	locationName?: string;
}

interface ImportSetting {
	id: string;
	name: string;
	icon: React.ReactNode;
	description?: string;
}

const availableSettings: ImportSetting[] = [
	{
		id: "operating-hours",
		name: "Operating Hours",
		icon: <Clock className="h-5 w-5 text-gray-500" />,
	},
	{
		id: "services",
		name: "Services",
		icon: <Settings className="h-5 w-5 text-gray-500" />,
	},
	{
		id: "members",
		name: "Members",
		icon: <Users className="h-5 w-5 text-gray-500" />,
	},
	{
		id: "schedule-settings",
		name: "Schedule Settings",
		icon: <Calendar className="h-5 w-5 text-gray-500" />,
	},
	{
		id: "walk-in-settings",
		name: "Walk-in Settings",
		icon: <UserCheck className="h-5 w-5 text-gray-500" />,
	},
];

export function ImportSettingsStep({
	onBack,
	onImport,
	providerName = "Service Provider Station Name",
	locationName = "Location Name",
}: ImportSettingsStepProps) {
	const [selectedSettings, setSelectedSettings] = useState<string[]>([]);

	const handleSettingSelection = (settingId: string, checked: boolean) => {
		if (checked) {
			setSelectedSettings((prev) => [...prev, settingId]);
		} else {
			setSelectedSettings((prev) =>
				prev.filter((id) => id !== settingId)
			);
		}
	};

	const handleImport = () => {
		const selectedSettingData = availableSettings.filter((setting) =>
			selectedSettings.includes(setting.id)
		);
		onImport?.(selectedSettingData);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center gap-3">
				<button
					onClick={onBack}
					className="flex h-8 min-w-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
				>
					<ChevronLeft className="h-5 w-5 text-gray-600" />
				</button>
				<div>
					<h3 className="text-lg font-bold text-gray-900">
						[{providerName}] at [{locationName}]
					</h3>
					<p className="text-sm text-gray-500">
						Select all the settings you'd like to import over to
						this new station at [{locationName}]
					</p>
				</div>
			</div>

			{/* Settings List */}
			<div className="space-y-3">
				{availableSettings.map((setting) => (
					<label
						htmlFor={setting.id}
						key={setting.id}
						className="border-primary flex cursor-pointer items-center gap-3 rounded-lg border px-3 py-2 hover:bg-gray-50"
					>
						<div className="flex flex-1 items-center gap-3">
							<div className="flex h-8 w-8 items-center justify-center">
								{setting.icon}
							</div>
							<div className="flex-1">
								<div className="font-medium text-gray-900">
									{setting.name}
								</div>
								{setting.description && (
									<div className="text-sm text-gray-500">
										{setting.description}
									</div>
								)}
							</div>
						</div>
						<Checkbox
							checked={selectedSettings.includes(setting.id)}
							onCheckedChange={(checked) =>
								handleSettingSelection(
									setting.id,
									checked as boolean
								)
							}
							id={setting.id}
						/>
					</label>
				))}
			</div>
		</div>
	);
}
