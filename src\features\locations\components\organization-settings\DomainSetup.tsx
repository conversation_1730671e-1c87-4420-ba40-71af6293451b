import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const DomainSetup: React.FC = () => {
	const [subdomain] = useState("qpc-hospital-236");

	return (
		<div className="max-w-lg">
			<h2 className="mb-4 text-xl font-semibold">Domain Setup</h2>
			<div className="mb-4">
				<label htmlFor="subdomain">Sub Domain</label>
				<p className="mt-1 text-xs text-gray-500">
					Editable only once and it must be a unique subdomain.
				</p>
				<div className="relative w-full">
					<input
						id="subdomain"
						type="text"
						value={subdomain}
						disabled
						className={cn(
							"peer border-input focus:border-primary focus:ring-primary block w-full rounded-md border bg-transparent px-3 py-2 text-base placeholder-transparent transition-all focus:ring-1 disabled:bg-gray-100",
							"pr-36" // space for the trailing domain
						)}
						placeholder="Sub Domain"
					/>
					<span className="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 select-none">
						.migranium.com
					</span>
				</div>
			</div>
			<Button className="rounded bg-blue-600 px-4 py-2 text-white">
				Edit
			</Button>
		</div>
	);
};

export default DomainSetup;
