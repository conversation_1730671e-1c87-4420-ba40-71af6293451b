import React, { useState } from "react";
import OrganizationInformation from "./organization-settings/OrganizationInformation";
import SecuritySettings from "./organization-settings/SecuritySettings";
import DomainSetup from "./organization-settings/DomainSetup";
import SamlSsoSettings from "./organization-settings/SamlSsoSettings";
import ThemeSettings from "./organization-settings/ThemeSettings";
import IntegrationsPlugins from "./organization-settings/IntegrationsPlugins";
// import IntegrationsPlugins from "./organization-settings/IntegrationsPlugins";

const sidebarItems = [
	{ key: "org-info", label: "Organization Information" },
	{ key: "security", label: "Security" },
	{ key: "domain", label: "Domain Setup" },
	{ key: "sso", label: "SAML single sign-on (SSO)" },
	{ key: "theme", label: "Theme" },
	{ key: "integrations", label: "Integrations & Plugins" },
];

const OrganizationSettingsTab: React.FC = () => {
	const [selected, setSelected] = useState("org-info");

	let ContentComponent;
	switch (selected) {
		case "org-info":
			ContentComponent = <OrganizationInformation />;
			break;
		case "security":
			ContentComponent = <SecuritySettings />;
			break;
		case "domain":
			ContentComponent = <DomainSetup />;
			break;
		case "sso":
			ContentComponent = <SamlSsoSettings />;
			break;
		case "theme":
			ContentComponent = <ThemeSettings />;
			break;
		case "integrations":
			ContentComponent = <IntegrationsPlugins />;
			break;
		default:
			ContentComponent = null;
	}

	return (
		<div className="flex min-h-[600px] rounded-lg bg-white p-4 shadow">
			{/* Sidebar */}
			<div className="w-64 border-r pr-4">
				<nav className="flex flex-col gap-2">
					{sidebarItems.map((item) => (
						<button
							key={item.key}
							className={`cursor-pointer rounded px-3 py-2 text-left font-medium transition ${selected === item.key ? "bg-[#F4F4F5] text-[#27272A]" : "text-[#27272A] hover:bg-gray-50"}`}
							onClick={() => setSelected(item.key)}
						>
							{item.label}
						</button>
					))}
				</nav>
			</div>
			{/* Main Content */}
			<div className="flex-1 pl-8">{ContentComponent}</div>
		</div>
	);
};

export default OrganizationSettingsTab;
