import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
	ChevronLeft,
	CheckCircle,
	Circle,
	Dot,
	ChevronDown,
	ChevronUp,
	ShieldCheck,
	AlertCircle,
} from "lucide-react";

// Placeholder icons (replace with real SVGs or images as needed)
const OscarProIcon = () => (
	<span className="flex inline-block h-10 w-10 items-center justify-center rounded bg-[#2EC4B6] font-bold text-white">
		O
	</span>
);
const AccuroEmrIcon = () => (
	<span className="flex inline-block h-10 w-10 items-center justify-center rounded bg-[#F76C6C] font-bold text-white">
		A
	</span>
);

const integrations = [
	{
		id: "oscar-pro",
		name: "OSCAR Pro",
		group: "EMR Solutions",
		icon: <OscarProIcon />,
		steps: [
			{
				title: "Step 1",
				content: (
					<div className="rounded-lg bg-white p-6 shadow">
						<div className="mb-2 font-semibold">
							Sign in to your EMR
						</div>
						<div className="mb-4 text-sm text-gray-600">
							Enter your EMR URL, admin credentials, and sign in
							to get your access!
						</div>
						<div className="mb-4 flex flex-col gap-3">
							<Input placeholder="EMR URL" />
							<Input placeholder="Email" type="email" />
							<Input placeholder="Password" type="password" />
						</div>
						<Button>Continue</Button>
					</div>
				),
			},
			{
				title: "Step 2",
				content: (
					<div className="rounded-lg bg-white p-6 shadow">
						<div className="mb-2 font-semibold">
							Navigate to REST Clients
						</div>
						<ol className="mb-4 list-inside list-decimal text-sm text-gray-700">
							<li>
								Go to the{" "}
								<span className="font-medium">
									Administrative Panel
								</span>{" "}
								(top right of the navigation panel)
							</li>
							<li>
								Click on{" "}
								<span className="font-medium">
									Integrations
								</span>{" "}
								(left hand panel)
							</li>
							<li>
								Then, click on{" "}
								<span className="font-medium">
									REST Clients
								</span>{" "}
								(right below Integrations)
							</li>
						</ol>
						<img
							src="https://via.placeholder.com/400x120?text=REST+Clients+Screenshot"
							alt="REST Clients Screenshot"
							className="my-2 rounded border"
						/>
					</div>
				),
			},
			{
				title: "Step 3",
				content: (
					<div className="rounded-lg bg-white p-6 shadow">
						<div className="mb-2 font-semibold">
							Once at REST Clients, click “Add New” client to your
							EMR
						</div>
						<div className="mb-2 text-sm text-gray-700">
							Click on “Add New” under Manage Clients
						</div>
						<img
							src="https://via.placeholder.com/400x120?text=Add+New+Client+Screenshot"
							alt="Add New Client Screenshot"
							className="my-2 rounded border"
						/>
						<div className="mt-4 mb-2 font-semibold">
							Fill in this information to complete adding a new
							client
						</div>
						<div className="mb-4 flex flex-col gap-3">
							<Input placeholder="Client Name" />
							<Input placeholder="Client Secret" />
							<Input placeholder="EMR Base URL" />
						</div>
						<Button>Create Client</Button>
					</div>
				),
			},
			{
				title: "Step 4",
				content: (
					<div className="rounded-lg bg-white p-6 shadow">
						<div className="mb-2 font-semibold">
							Add details for EMR to connect the system
						</div>
						<div className="mb-2 text-sm text-gray-700">
							Add Client Name, Client Secret, and EMR Base URL to
							connect your system.
						</div>
						<img
							src="https://via.placeholder.com/400x120?text=Connection+Details+Screenshot"
							alt="Connection Details Screenshot"
							className="my-2 rounded border"
						/>
						<div className="mt-4 mb-4 flex flex-col gap-3">
							<Input placeholder="Client ID" />
							<Input placeholder="Client Secret" />
							<Input placeholder="EMR Base URL" />
						</div>
						<Button>Connect</Button>
					</div>
				),
			},
		],
	},
	{
		id: "accuro-emr",
		name: "Accuro EMR",
		group: "EMR Solutions",
		icon: <AccuroEmrIcon />,
		steps: [
			{
				title: "Step 1",
				content: <div>Accuro EMR Step 1 instructions...</div>,
			},
		],
	},
	{
		id: "oscar-pro-2",
		name: "OSCAR Pro",
		group: "Other Tools",
		icon: <OscarProIcon />,
		steps: [
			{
				title: "Step 1",
				content: <div>Other Tool Step 1 instructions...</div>,
			},
		],
	},
	{
		id: "oscar-pro-3",
		name: "OSCAR Pro",
		group: "Other Tools",
		icon: <OscarProIcon />,
		steps: [
			{
				title: "Step 1",
				content: <div>Other Tool Step 1 instructions...</div>,
			},
		],
	},
];

const groups = ["EMR Solutions", "Other Tools"];

const Stepper = ({
	steps,
	current,
}: {
	steps: { title: string }[];
	current: number;
}) => (
	<div className="mb-8 flex w-full items-center justify-between px-2">
		{steps.map((step, idx) => (
			<React.Fragment key={step.title}>
				<div className="flex flex-col items-center">
					<div
						className={
							"z-10 flex h-7 w-7 items-center justify-center rounded-full border-2 bg-white text-sm font-semibold " +
							(idx === current
								? "border-blue-600 bg-blue-50 text-blue-600"
								: "border-gray-300 bg-white text-gray-400")
						}
					>
						{idx + 1}
					</div>
				</div>
				{idx < steps.length - 1 && (
					<div className="mx-2 h-0.5 flex-1 bg-gray-200" />
				)}
			</React.Fragment>
		))}
	</div>
);

const ConnectedPanel = ({
	integration,
	onDisconnect,
}: {
	integration: any;
	onDisconnect: () => void;
}) => {
	const [showInfo, setShowInfo] = useState(false);
	return (
		<div className="mx-auto max-w-2xl">
			<div className="mb-6 flex items-center gap-3">
				{integration.icon}
				<span className="text-lg font-semibold">
					{integration.name}
				</span>
			</div>
			<div className="mb-4 flex items-center gap-2">
				<CheckCircle className="h-5 w-5 text-green-500" />
				<span className="font-medium text-green-700">
					You are connected
				</span>
				<span className="text-sm text-gray-500">
					Connected since: 06 MM YYYY
				</span>
			</div>
			<div className="mb-4">
				<button
					className="flex items-center gap-2 text-base font-medium text-gray-700 focus:outline-none"
					onClick={() => setShowInfo((v) => !v)}
				>
					EMR Integration Information
					{showInfo ? (
						<ChevronUp className="h-4 w-4" />
					) : (
						<ChevronDown className="h-4 w-4" />
					)}
				</button>
				{showInfo && (
					<div className="mt-2 rounded border bg-gray-50 p-4 text-sm text-gray-700">
						<div>
							Client ID:{" "}
							<span className="font-mono">oscar-123</span>
						</div>
						<div>
							Client Secret:{" "}
							<span className="font-mono">••••••••</span>
						</div>
						<div>
							EMR Base URL:{" "}
							<span className="font-mono">
								https://oscarpro.example.com
							</span>
						</div>
					</div>
				)}
			</div>
			<div className="mb-4 rounded border bg-red-50 p-4">
				<div className="mb-2 font-medium text-red-700">Disconnect</div>
				<div className="mb-2 text-xs text-gray-600">
					Disconnecting this EMR will limit certain functions. You may
					disconnect here or contact support.
				</div>
				<Button variant="destructive" onClick={onDisconnect}>
					Disconnect
				</Button>
			</div>
		</div>
	);
};

const NotConnectedPanel = ({
	integration,
	onConnect,
}: {
	integration: any;
	onConnect: () => void;
}) => (
	<div className="mx-auto max-w-2xl">
		<div className="mb-6 flex items-center gap-3">
			{integration.icon}
			<span className="text-lg font-semibold">{integration.name}</span>
		</div>
		<div className="mb-4 flex items-center gap-2">
			<AlertCircle className="h-5 w-5 text-yellow-500" />
			<span className="font-medium text-yellow-700">Not connected</span>
		</div>
		<div className="mb-6 text-sm text-gray-700">
			Connect your EMR to enable seamless data exchange and unlock all
			features.
		</div>
		<Button onClick={onConnect}>Connect</Button>
	</div>
);

const IntegrationsPlugins: React.FC = () => {
	const [search, setSearch] = useState("");
	const [selected, setSelected] = useState<string | null>(null);
	const [step, setStep] = useState(0);
	const [connected, setConnected] = useState(false);
	const [started, setStarted] = useState(false);

	const filtered = integrations.filter((i) =>
		i.name.toLowerCase().includes(search.toLowerCase())
	);

	const selectedIntegration = integrations.find((i) => i.id === selected);

	// Show connected panel after last step for OSCAR Pro
	if (
		selectedIntegration &&
		selectedIntegration.id === "oscar-pro" &&
		connected
	) {
		return (
			<div className="max-w-4xl">
				<div className="mb-6 flex items-center gap-2">
					<Button
						variant="ghost"
						size="icon"
						onClick={() => {
							setSelected(null);
							setStep(0);
							setConnected(false);
							setStarted(false);
						}}
					>
						<ChevronLeft className="h-5 w-5" />
					</Button>
					<span className="text-lg font-semibold">
						Integrate {selectedIntegration.name}
					</span>
				</div>
				<ConnectedPanel
					integration={selectedIntegration}
					onDisconnect={() => {
						setConnected(false);
						setStarted(false);
					}}
				/>
			</div>
		);
	}

	// Show not connected panel before starting integration
	if (selectedIntegration && !started) {
		return (
			<div className="max-w-4xl">
				<div className="mb-6 flex items-center gap-2">
					<Button
						variant="ghost"
						size="icon"
						onClick={() => {
							setSelected(null);
							setStep(0);
							setConnected(false);
							setStarted(false);
						}}
					>
						<ChevronLeft className="h-5 w-5" />
					</Button>
					<span className="text-lg font-semibold">
						Integrate {selectedIntegration.name}
					</span>
				</div>
				<NotConnectedPanel
					integration={selectedIntegration}
					onConnect={() => setStarted(true)}
				/>
			</div>
		);
	}

	if (selectedIntegration) {
		const steps = selectedIntegration.steps || [];
		return (
			<div className="max-w-4xl">
				<div className="mb-6 flex items-center gap-2">
					<Button
						variant="ghost"
						size="icon"
						onClick={() => {
							setSelected(null);
							setStep(0);
							setConnected(false);
							setStarted(false);
						}}
					>
						<ChevronLeft className="h-5 w-5" />
					</Button>
					<span className="text-lg font-semibold">
						Integrate {selectedIntegration.name}
					</span>
				</div>
				<div className="flex flex-col gap-8">
					<Stepper steps={steps} current={step} />
					<div className="flex-1">
						<div className="mb-6 flex items-center gap-3">
							{selectedIntegration.icon}
							<span className="text-base font-semibold">
								{selectedIntegration.name}
							</span>
						</div>
						<div className="mb-4">{steps[step]?.content}</div>
						<div className="mt-4 flex gap-2">
							{step > 0 && (
								<Button
									variant="outline"
									onClick={() => setStep(step - 1)}
								>
									Previous
								</Button>
							)}
							{step < steps.length - 1 && (
								<Button onClick={() => setStep(step + 1)}>
									Next
								</Button>
							)}
							{/* Show Finish/Connect button on last step for OSCAR Pro */}
							{selectedIntegration.id === "oscar-pro" &&
								step === steps.length - 1 && (
									<Button onClick={() => setConnected(true)}>
										Finish
									</Button>
								)}
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="max-w-4xl">
			<h2 className="mb-4 text-xl font-semibold">
				Integrations & Plugins
			</h2>
			<Input
				placeholder="Search"
				value={search}
				onChange={(e) => setSearch(e.target.value)}
				className="mb-4 max-w-md"
			/>
			{groups.map((group) => {
				const groupItems = filtered.filter((i) => i.group === group);
				if (groupItems.length === 0) return null;
				return (
					<div key={group} className="mb-8">
						<h3 className="mb-2 text-base font-semibold text-gray-700">
							{group}
						</h3>
						<div className="flex flex-wrap gap-8">
							{groupItems.map((integration) => (
								<button
									key={integration.id}
									className="flex min-w-[120px] cursor-pointer flex-col items-center gap-2 rounded-lg border border-gray-200 bg-white px-6 py-4 shadow-sm transition hover:shadow-md"
									onClick={() => {
										setSelected(integration.id);
										setStep(0);
										setConnected(false);
										setStarted(false);
									}}
									type="button"
								>
									{integration.icon}
									<span className="mt-1 text-sm font-medium text-gray-800">
										{integration.name}
									</span>
								</button>
							))}
						</div>
					</div>
				);
			})}
		</div>
	);
};

export default IntegrationsPlugins;
