import {
	MultiSelectAccordion,
	type SelectableGroup,
} from "@/components/common/MultiSelectAccordion";

// Define types for backward compatibility
export interface Station {
	id: string;
	name: string;
}

export interface Location {
	id: string;
	name: string;
	stations: Station[];
}

export type ApplyServiceOption = "all-locations" | "selected-locations";

export interface LocationStationAccordionProps {
	locations: Location[];
	selectedLocationIds: Set<string>;
	selectedStationIds: Set<string>;
	onLocationSelectionChange: (locationId: string, checked: boolean) => void;
	onStationSelectionChange: (stationId: string, checked: boolean) => void;
	onBack?: () => void;

	// Text customization
	allLocationsTitle?: string;
	allLocationsSubtitle?: string;
	selectedLocationsTitle?: string;
	selectedLocationsSubtitle?: string;
	searchPlaceholder?: string;

	// Display options
	showBackButton?: boolean;
}

export function LocationStationAccordion({
	locations,
	selectedLocationIds,
	selectedStationIds,
	onLocationSelectionChange,
	onStationSelectionChange,
	onBack,
	allLocationsTitle = "All Locations and Stations",
	allLocationsSubtitle = "This will add this service to all locations & stations in this location.",
	selectedLocationsTitle = "Selected Locations and Stations only",
	selectedLocationsSubtitle = "This will add this service to all locations & stations in this location.",
	searchPlaceholder = "Select All Locations and Stations",
	showBackButton = true,
}: LocationStationAccordionProps) {
	// Convert locations to SelectableGroup format
	const groups: SelectableGroup[] = locations.map((location) => ({
		id: location.id,
		name: location.name,
		items: location.stations.map((station) => ({
			id: station.id,
			name: station.name,
		})),
	}));

	return (
		<MultiSelectAccordion
			groups={groups}
			selectedGroupIds={selectedLocationIds}
			selectedItemIds={selectedStationIds}
			onGroupSelectionChange={onLocationSelectionChange}
			onItemSelectionChange={onStationSelectionChange}
			onBack={onBack}
			allItemsTitle={allLocationsTitle}
			allItemsSubtitle={allLocationsSubtitle}
			allItemsDescription="This option will apply the service to all locations and stations automatically."
			selectedItemsTitle={selectedLocationsTitle}
			selectedItemsSubtitle={selectedLocationsSubtitle}
			searchPlaceholder={searchPlaceholder}
			showBackButton={showBackButton}
			showAllItemsOption={true}
			showSearch={true}
			context="locations"
		/>
	);
}
