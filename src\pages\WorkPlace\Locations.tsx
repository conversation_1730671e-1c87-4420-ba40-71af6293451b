import { useEffect, useState, type FC } from "react";
import {
	LocationOrganizationCard,
	LocationsList,
	OrganizationDetailsSheet,
	type Organization,
} from "@/features/locations";
import { useUIStore } from "@/stores/uiStore";
import { useOrganizationContext } from "@/features/organizations/context";

const Locations: FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const [openOrganizationDetailsSheet, setOpenOrganizationDetailsSheet] =
		useState(false);
	
		const setCurrentPageTitle = useUIStore((state) => state.setCurrentPageTitle);
		

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Workplace",
				href: "/workplace",
			},
			{
				label: "Locations",
				isCurrentPage: true,
			},
		]);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	const handleView = (org: Organization) => {
		console.log("View:", org.name);
		setOpenOrganizationDetailsSheet(true);
	};

	const organization = useOrganizationContext();
	return (
		<div className="flex flex-col gap-4 py-6">
			<LocationOrganizationCard
				organization={organization.organization!}
				onView={handleView}
				onEdit={(org) => console.log("Edit:", org.name)}
				onDelete={(org) => console.log("Delete:", org.name)}
			/>
			<LocationsList />
			<OrganizationDetailsSheet
				open={openOrganizationDetailsSheet}
				onClose={() => setOpenOrganizationDetailsSheet(false)}
			/>
		</div>
	);
};

export default Locations;
