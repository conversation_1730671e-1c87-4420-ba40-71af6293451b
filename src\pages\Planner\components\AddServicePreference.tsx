// components/planner/AddServicePreference.tsx
import React, { useState } from 'react';
import { ChevronLeft, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Import shared components
import { CustomParameterSidebar } from './CustomParameterSidebar';
import { DayScheduleTable } from './DayScheduleTable';
import { OccurrenceSelector } from './OccurenceSelector';
import { RuleDurationSelector } from './RuleDurationSelector';
import { FrequencyApplicationSheet } from './FrequencyApplicationSheet';
import { navigationHelpers } from '../utils/navigation';
import { useNavigate } from 'react-router';
import { useCurrentContext } from '../hooks/useCurrentContext';
import { InputText } from '@/components/common/InputText';

interface CustomParameter {
  id: string;
  label: string;
  enabled: boolean;
}

interface TimeValue {
  hour: string;
  minute: string;
  period: 'AM' | 'PM';
}

interface TimeSlot {
  id: string;
  startTime: TimeValue;
  endTime: TimeValue;
}

interface DaySchedule {
  day: string;
  enabled: boolean;
  timeSlots: TimeSlot[];
  maxFrequency: string;
}

interface Service {
  id: string;
  name: string;
}

interface AddServicePreferenceProps {
  onBack: () => void;
  onSave: (preferenceData: any) => void;
  editingId?: string | null;
  initialData?: any;
}

export const AddServicePreference: React.FC<AddServicePreferenceProps> = ({ 
  onBack, 
  onSave,
  editingId,
  initialData
}) => {
  const navigate = useNavigate();
  const context = useCurrentContext();
  
  // Form state
  const [selectedService, setSelectedService] = useState(initialData?.serviceId || '');
  const [title, setTitle] = useState(initialData?.title || '');
  
  const [preferenceType, setPreferenceType] = useState<'availability' | 'restriction'>(initialData?.preferenceType || 'availability');
  const [totalMaxFrequency, setTotalMaxFrequency] = useState(initialData?.totalMaxFrequency || '25');
  const [frequencyPeriod, setFrequencyPeriod] = useState(initialData?.frequencyPeriod || 'Monthly');
  const [occurrence, setOccurrence] = useState<'Daily' | 'Weekly' | 'Monthly' | 'Yearly'>(initialData?.occurrence || 'Daily');
  const [timePeriodType, setTimePeriodType] = useState<'rolling' | 'specific'>(initialData?.timePeriodType || 'rolling');
  const [duration, setDuration] = useState(initialData?.duration || '20');
  const [durationUnit, setDurationUnit] = useState(initialData?.durationUnit || 'Days');
  const [startDate, setStartDate] = useState(initialData?.startDate || '');
  const [endDate, setEndDate] = useState(initialData?.endDate || '');
  const [showFrequencySheet, setShowFrequencySheet] = useState(false);

  // Context-aware service filtering
  const getLocationSpecificServices = (): Service[] => {
    const allServices: Service[] = [
      { id: '1', name: 'General Medical Test' },
      { id: '2', name: 'Blood Work' },
      { id: '3', name: 'Immunization' },
      { id: '4', name: 'Report Reading' },
      { id: '5', name: 'Video Consultation' },
      { id: '6', name: 'General Consultation' },
      { id: '7', name: 'X-Ray' },
      { id: '8', name: 'MRI Scan' },
      { id: '9', name: 'Physical Therapy' },
      { id: '10', name: 'Dental Cleaning' },
    ];

     // For now, return all services - replace with actual API filtering later
    return allServices;
  };

  const services = getLocationSpecificServices();

  // Context-aware title and labels
  const getContextTitle = () => {
    switch (context.level) {
      case 'location':
        return editingId ? 'Edit Location Service Preference' : 'Add Location Service Preference';
      case 'provider':
        return editingId ? 'Edit Provider Service Preference' : 'Add Provider Service Preference';
      default:
        return editingId ? 'Edit Service Preference' : 'Add Service Preference';
    }
  };

  const getContextDescription = () => {
    switch (context.level) {
      case 'location':
        return `Set service-specific preferences for ${context.entityName}`;
      case 'provider':
        return `Set service-specific preferences for ${context.entityName}`;
      default:
        return 'Set service-specific preferences across the organization';
    }
  };

  // Custom parameters state
  const [customParameters, setCustomParameters] = useState<CustomParameter[]>(
    initialData?.customParameters || [
      { id: 'selectDays', label: 'Select Days', enabled: true },
      { id: 'setTimes', label: 'Set Times', enabled: true },
      { id: 'setFrequency', label: 'Set Frequency', enabled: true },
      { id: 'setOccurrence', label: 'Set Occurrence', enabled: false },
      { id: 'setTimePeriod', label: 'Set Time Period', enabled: true },
    ]
  );

  // Days schedule state
  const [daysSchedule, setDaysSchedule] = useState<DaySchedule[]>(
    initialData?.daysSchedule || [
      { 
        day: 'Monday', 
        enabled: true, 
        timeSlots: [{ 
          id: 'mon-1', 
          startTime: { hour: '08', minute: '00', period: 'AM' }, 
          endTime: { hour: '05', minute: '00', period: 'PM' } 
        }], 
        maxFrequency: '' 
      },
      { 
        day: 'Tuesday', 
        enabled: false, 
        timeSlots: [{ 
          id: 'tue-1', 
          startTime: { hour: '08', minute: '00', period: 'AM' }, 
          endTime: { hour: '05', minute: '00', period: 'PM' } 
        }], 
        maxFrequency: '' 
      },
      { 
        day: 'Wednesday', 
        enabled: false, 
        timeSlots: [{ 
          id: 'wed-1', 
          startTime: { hour: '08', minute: '00', period: 'AM' }, 
          endTime: { hour: '05', minute: '00', period: 'PM' } 
        }], 
        maxFrequency: '' 
      },
      { 
        day: 'Thursday', 
        enabled: false, 
        timeSlots: [{ 
          id: 'thu-1', 
          startTime: { hour: '08', minute: '00', period: 'AM' }, 
          endTime: { hour: '05', minute: '00', period: 'PM' } 
        }], 
        maxFrequency: '' 
      },
      { 
        day: 'Friday', 
        enabled: false, 
        timeSlots: [{ 
          id: 'fri-1', 
          startTime: { hour: '08', minute: '00', period: 'AM' }, 
          endTime: { hour: '05', minute: '00', period: 'PM' } 
        }], 
        maxFrequency: '' 
      },
      { 
        day: 'Saturday', 
        enabled: false, 
        timeSlots: [{ 
          id: 'sat-1', 
          startTime: { hour: '08', minute: '00', period: 'AM' }, 
          endTime: { hour: '05', minute: '00', period: 'PM' } 
        }], 
        maxFrequency: '' 
      },
      { 
        day: 'Sunday', 
        enabled: false, 
        timeSlots: [{ 
          id: 'sun-1', 
          startTime: { hour: '08', minute: '00', period: 'AM' }, 
          endTime: { hour: '05', minute: '00', period: 'PM' } 
        }], 
        maxFrequency: '' 
      },
    ]
  );

  // Occurrence selections
  const [selectedOccurrenceItems, setSelectedOccurrenceItems] = useState<string[]>(initialData?.selectedOccurrenceItems || []);

  // Helper functions
  const toggleParameter = (id: string) => {
    setCustomParameters(prev =>
      prev.map(param =>
        param.id === id ? { ...param, enabled: !param.enabled } : param
      )
    );
  };

  const toggleDay = (index: number) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === index ? { ...day, enabled: !day.enabled } : day
      )
    );
  };

  const updateDayTime = (dayIndex: number, slotIndex: number, field: 'startTime' | 'endTime', time: TimeValue) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === dayIndex
          ? {
              ...day,
              timeSlots: day.timeSlots.map((slot, j) =>
                j === slotIndex ? { ...slot, [field]: time } : slot
              )
            }
          : day
      )
    );
  };

  const addTimeSlot = (dayIndex: number) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === dayIndex
          ? {
              ...day,
              timeSlots: [
                ...day.timeSlots,
                {
                  id: `${day.day.toLowerCase()}-${day.timeSlots.length + 1}`,
                  startTime: { hour: '08', minute: '00', period: 'AM' },
                  endTime: { hour: '05', minute: '00', period: 'PM' }
                }
              ]
            }
          : day
      )
    );
  };

  const removeTimeSlot = (dayIndex: number, slotIndex: number) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === dayIndex
          ? {
              ...day,
              timeSlots: day.timeSlots.filter((_, j) => j !== slotIndex)
            }
          : day
      )
    );
  };

  const updateDayFrequency = (index: number, frequency: string) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === index ? { ...day, maxFrequency: frequency } : day
      )
    );
  };

  const getEnabledParameter = (id: string) => {
    return customParameters.find(param => param.id === id)?.enabled || false;
  };

  const getCharacterCount = () => title.length;

  const toggleOccurrenceItem = (item: string) => {
    setSelectedOccurrenceItems(prev =>
      prev.includes(item)
        ? prev.filter(i => i !== item)
        : [...prev, item]
    );
  };

  // Frequency application functions
  const shouldShowFrequencySheet = () => {
    return getEnabledParameter('setFrequency') && totalMaxFrequency && frequencyPeriod;
  };

  const handleAdd = () => {
    if (shouldShowFrequencySheet()) {
      setShowFrequencySheet(true);
    } else {
      handleSave();
    }
  };

  const handleFrequencyComplete = (frequencyData: any) => {
    // Handle frequency application data
    console.log('Frequency application data:', frequencyData);
    handleSave(frequencyData);
  };

  const handleSave = (frequencyData: any = null) => {
    // Validation
    if (!selectedService || !title.trim()) {
      alert('Please fill required fields');
      return;
    }

    // Prepare preference data with context
    const preferenceData = {
      serviceId: selectedService,
      serviceName: services.find(s => s.id === selectedService)?.name,
      title: title.trim(),
      preferenceType,
      customParameters,
      daysSchedule,
      totalMaxFrequency: getEnabledParameter('setFrequency') ? totalMaxFrequency : null,
      frequencyPeriod: getEnabledParameter('setFrequency') ? frequencyPeriod : null,
      occurrence: getEnabledParameter('setOccurrence') ? occurrence : null,
      selectedOccurrenceItems: getEnabledParameter('setOccurrence') ? selectedOccurrenceItems : null,
      timePeriodType: getEnabledParameter('setTimePeriod') ? timePeriodType : null,
      duration: getEnabledParameter('setTimePeriod') && timePeriodType === 'rolling' ? duration : null,
      durationUnit: getEnabledParameter('setTimePeriod') && timePeriodType === 'rolling' ? durationUnit : null,
      startDate: getEnabledParameter('setTimePeriod') && timePeriodType === 'specific' ? startDate : null,
      endDate: getEnabledParameter('setTimePeriod') && timePeriodType === 'specific' ? endDate : null,
      // Add frequency application data if available
      frequencyApplication: frequencyData,
      // Add context information
      contextLevel: context.level,
      entityId: context.entityId,
      entityName: context.entityName,
    };

    // Check for conflicts (this could be an API call)
    const hasRuleConflicts = Math.random() > 0.3; // Simulate conflict detection
    const hasAppointmentConflicts = Math.random() > 0.5;

    if (hasRuleConflicts) {
      // Navigate to conflicts page with data in URL state
      navigate(`${navigationHelpers.getBasePath(context.level, context.entityId)}/services/conflicts`, { 
        state: { preferenceData }
      });
    } else if (hasAppointmentConflicts) {
      // Navigate to appointment conflicts
      navigate(`${navigationHelpers.getBasePath(context.level, context.entityId)}/services/appointment-conflicts`, { 
        state: { preferenceData }
      });
    } else {
      // No conflicts, navigate back to preferences list and show success there
      navigate(navigationHelpers.getBasePath(context.level, context.entityId), { 
        state: { 
          showSuccess: true, 
          preferenceData 
        }
      });
    }
  };

  const handleSavePreference = () => {
    handleAdd();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={onBack} className="flex items-center space-x-2">
              <ChevronLeft className="h-4 w-4" />
              <span>Back</span>
            </Button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">{getContextTitle()}</h1>
              <p className="text-gray-600 mt-1">{getContextDescription()}</p>
            </div>
          </div>
          <Button variant="outline" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>View Schedule</span>
          </Button>
        </div>
      </div>

      <div className="flex h-screen py-4 px-6">
        {/* Sidebar */}
        <div className='border-r border-gray-200'>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-semibold">Add Preference</h1>
            </div>
          </div>
          
          {/* Service Selection in Sidebar */}
          <div className="p-4 border-b border-gray-200">
            <label className="block text-sm font-medium mb-2">Select Service *</label>
            <Select value={selectedService} onValueChange={setSelectedService as any}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a service" />
              </SelectTrigger>
              <SelectContent>
                {services.map((service) => (
                  <SelectItem key={service.id} value={service.id}>
                    {service.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <CustomParameterSidebar
            preferenceType={preferenceType}
            onPreferenceTypeChange={setPreferenceType}
            customParameters={customParameters}
            onParameterToggle={toggleParameter}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="mx-auto space-y-6">
            {/* Title */}
            <div className="border border-gray-200 p-4 rounded-lg bg-white flex gap-2 shadow-sm mx-auto">
              <div className='max-w-[80%] flex gap-2 w-full items-baseline'>
                <label className="block text-sm font-medium mb-2">Title</label>
                <div className='flex flex-col gap-2 w-full '>
                  <InputText placeholder="Enter Title Here"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="w-full"
                    maxLength={120}
                  />
                  <p className="text-sm text-gray-500 mt-1">{getCharacterCount()}/120 characters</p>
                </div>
              </div>
            </div>

            {/* Days and Time Configuration - Updated with dynamic columns */}
            {(getEnabledParameter('selectDays') || getEnabledParameter('setTimes')) && (
              <div className="border border-gray-200 p-4 rounded-lg bg-white flex gap-2 shadow-sm w-full">
                <DayScheduleTable
                  daysSchedule={daysSchedule}
                  onDayToggle={toggleDay}
                  onTimeSlotChange={updateDayTime}
                  onAddTimeSlot={addTimeSlot}
                  onRemoveTimeSlot={removeTimeSlot}
                  onFrequencyChange={updateDayFrequency}
                  showTimes={getEnabledParameter('setTimes')}
                  showFrequency={getEnabledParameter('selectDays') && getEnabledParameter('setFrequency')}
                />
              </div>
            )}

            {/* Total Maximum Frequency - Always show when setFrequency is enabled */}
            {getEnabledParameter('setFrequency') && (
              <div className="border border-gray-200 p-4 rounded-lg bg-white flex gap-2 shadow-sm w-full">
                <div className="grid grid-cols-2 gap-4 items-end w-full">
                  <div>
                    <label className="block text-sm font-medium mb-2">Total Maximum Frequency</label>
                    <Input
                      value={totalMaxFrequency}
                      onChange={(e) => setTotalMaxFrequency(e.target.value)}
                    />
                  </div>
                  <div>
                    <Select value={frequencyPeriod} onValueChange={setFrequencyPeriod}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Monthly">Monthly</SelectItem>
                        <SelectItem value="Weekly">Weekly</SelectItem>
                        <SelectItem value="Daily">Daily</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {/* Select Occurrence */}
            {getEnabledParameter('setOccurrence') && (
              <div className="border border-gray-200 p-4 rounded-lg bg-white flex gap-2 shadow-sm w-full">
                <OccurrenceSelector
                  occurrence={occurrence}
                  onOccurrenceChange={setOccurrence}
                  selectedItems={selectedOccurrenceItems}
                  onItemToggle={toggleOccurrenceItem}
                />
              </div>
            )}

            {/* Set Rule Duration */}
            {getEnabledParameter('setTimePeriod') && (
              <div className="border border-gray-200 p-4 rounded-lg bg-white flex gap-2 shadow-sm w-full">
                <RuleDurationSelector
                  timePeriodType={timePeriodType}
                  onTimePeriodTypeChange={setTimePeriodType}
                  duration={duration}
                  onDurationChange={setDuration}
                  durationUnit={durationUnit}
                  onDurationUnitChange={setDurationUnit}
                  startDate={startDate}
                  onStartDateChange={setStartDate}
                  endDate={endDate}
                  onEndDateChange={setEndDate}
                />
              </div>
            )}

            {/* Action Button */}
            <div className="flex justify-end">
              <Button
                onClick={handleSavePreference}
                disabled={!title.trim() || !selectedService}
              >
                {editingId ? 'Update' : 'Add'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Frequency Application Sheet */}
      <FrequencyApplicationSheet
        isOpen={showFrequencySheet}
        onClose={() => setShowFrequencySheet(false)}
        totalMaxFrequency={totalMaxFrequency}
        frequencyPeriod={frequencyPeriod}
        onComplete={handleFrequencyComplete}
      />
    </div>
  );
};

