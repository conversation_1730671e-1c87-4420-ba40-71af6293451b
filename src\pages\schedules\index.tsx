import { But<PERSON> } from "@/components/ui/button";
import { LuCalendarPlus } from "react-icons/lu";
import { IoSettingsOutline } from "react-icons/io5";
// import CalendarView from "./calendar-view";
import Filter from "./filter";
import Calendar from "./calendar";
import FilterAppointment from "./filter-appointment";
import BookAppointment from "./book-appointment";
import { useEffect, useState } from "react";
import { useUIStore } from "@/stores/uiStore";

export default function SchedulePage() {
    const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
    const setPhContent = useUIStore(
        (state) => state.setPageHeaderContent
    );

    const [appointmentOpen, setAppointmentOpen] = useState<boolean>(false);

    useEffect(() => {
        setBreadcrumbs([
            {
                label: "Schedule",
                href: "/schedule",
            },
            {
                label: "Manage Schedule",
                isCurrentPage: true,
            },
            {
                label: "Location Name",
                isCurrentPage: true,
            },
        ]);
        // setPhContent("Manage Schedule");

        // Cleanup breadcrumbs when component unmounts
        return () => {
            setBreadcrumbs([]);
        };
    }, [setBreadcrumbs]);

    useEffect(() => {
        const headerContent = (
            <div className="flex flex-1 items-center justify-between">
                <div>
                    <h1 className="text-foreground text-2xl font-bold">
                        Manage Schedule
                    </h1>
                    <p className="text-[#71717A] font-regular text-base">[Location Name]</p>
                </div>
                <div className="flex items-center gap-x-2.5">
                    <FilterAppointment />
                    <Button variant="outline" className="py-5">
                        <IoSettingsOutline />
                    </Button>
                    <Button
                        className="py-5 cursor-pointer"
                        type="button"
                        onClick={() => setAppointmentOpen(true)}
                    >
                        <LuCalendarPlus />
                        Add Appointment
                    </Button>
                </div>
            </div>
        );

        setPhContent(headerContent);

        return () => {
            setPhContent(null);
        };
    }, [setPhContent]);

    return (
        <section>

            <BookAppointment
                open={appointmentOpen}
                onOpenChange={setAppointmentOpen}
            />


            <div className="grid w-full max-w-screen grid-cols-[350px_1fr] gap-x-6 mt-4 h-screen max-h-screen">

                <Filter />

                <div className="overflow-x-auto w-full">
                    <Calendar />
                </div>

            </div>

        </section>
    );
}