import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Pencil } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Settings } from "lucide-react";

export function AccountSettingsContent() {
	const [showProfileUpdate, setShowProfileUpdate] = useState(false);
    const [showPasswordUpdate, setShowPasswordUpdate] = useState(false);
    
    const [emailAuthEnabled, setEmailAuthEnabled] = useState(true);
	const [thirdPartyAuthEnabled, setThirdPartyAuthEnabled] = useState(false);

	return (
		<div className="flex w-full flex-col gap-8">
			{/* Heading & Description */}
			<div className="w-full border-b border-b-[#E4E4E7] pb-3">
				<h1 className="text-left text-2xl font-bold">
					Account Settings
				</h1>
				<p className="text-muted-foreground mt-1 text-sm">
					Change or update your account information.
				</p>
			</div>

			{/* Profile Information */}
			<section className="flex w-full flex-col gap-4 border-b border-b-[#E4E4E7] pb-8">
				<div className="flex w-full items-center justify-between">
					<h2 className="text-xl font-semibold">
						Profile Information
					</h2>
					<Button
						variant="outline"
						size="sm"
						onClick={() => setShowProfileUpdate((prev) => !prev)}
						className="flex cursor-pointer items-center gap-2"
					>
						<Pencil className="h-4 w-4" />
						Edit
					</Button>
				</div>

				<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
					<div>
						<Label htmlFor="firstName" className="pb-2">
							First Name *
						</Label>
						<Input
							id="firstName"
							placeholder="John Doe"
							className="h-10 border-[#E4E4E7]"
						/>
					</div>
					<div>
						<Label htmlFor="lastName" className="pb-2">
							Last Name *
						</Label>
						<Input
							id="lastName"
							placeholder="Nicol"
							className="h-10 border-[#E4E4E7]"
						/>
					</div>
				</div>

				<div className="mt-6 grid grid-cols-1 gap-6">
					<div>
						<Label htmlFor="email" className="pb-2">
							Email *
						</Label>
						<Input
							id="email"
							type="email"
							placeholder="<EMAIL>"
							className="h-10 border-[#E4E4E7]"
						/>
					</div>
					<div>
						<Label htmlFor="phone" className="pb-2">
							Phone *
						</Label>
						<Input
							id="phone"
							type="tel"
							placeholder="+234 ************"
							className="h-10 border-[#E4E4E7]"
						/>
					</div>
				</div>

				{showProfileUpdate && (
					<Button className="mt-4 self-start">Update Profile</Button>
				)}
			</section>

			{/* Password Section */}
			<section className="flex flex-col gap-4 border-b border-b-[#E4E4E7] pb-8">
				<div className="flex w-full items-center justify-between">
					<h2 className="text-xl font-semibold">Password</h2>
					<Button
						variant="outline"
						size="sm"
						onClick={() => setShowPasswordUpdate((prev) => !prev)}
						className="flex cursor-pointer items-center gap-2"
					>
						<Pencil className="h-4 w-4" />
						Edit
					</Button>
				</div>

				<div className="grid grid-cols-1 gap-6">
					<div>
						<Label htmlFor="currentPassword" className="pb-2">
							Current Password *
						</Label>
						<Input
							id="currentPassword"
							type="password"
							className="h-10 border-[#E4E4E7]"
						/>
					</div>
					<div>
						<Label htmlFor="newPassword" className="pb-2">
							New Password *
						</Label>
						<Input
							id="newPassword"
							type="password"
							className="h-10 border-[#E4E4E7]"
						/>
						<p className="text-muted-foreground mt-2 text-xs">
							Password must be 8+ characters with an uppercase
							letter, lowercase letter, number, and special
							character.
						</p>
					</div>
					<div>
						<Label htmlFor="confirmPassword" className="pb-2">
							Confirm New Password *
						</Label>
						<Input
							id="confirmPassword"
							type="password"
							className="h-10 border-[#E4E4E7]"
						/>
					</div>
				</div>

				{showPasswordUpdate && (
					<Button
						className="mt-4 cursor-pointer self-start"
						variant="outline"
					>
						Change Password
					</Button>
				)}
			</section>

			<section className="mt-2 flex flex-col gap-8">
				{/* Section Heading */}
				<div className="flex w-full flex-col gap-1 border-b border-b-[#E4E4E7] pb-3">
					<h2 className="text-xl font-semibold">Authentication</h2>
					<p className="text-muted-foreground text-sm">
						Manage how your account is verified and accessed.
					</p>
				</div>

				{/* Email Authentication */}
				<div className="flex items-center justify-between">
					<div>
						<h3 className="text-sm font-medium">
							Email Authentication
						</h3>
						<p className="text-muted-foreground mt-1 text-xs">
							Securely verify your account using your email.
						</p>
					</div>
					<Switch
						checked={emailAuthEnabled}
						onCheckedChange={setEmailAuthEnabled}
						className="cursor-pointer"
					/>
				</div>

				{/* Third Party Auth */}
				<div className="flex items-center justify-between">
					<div>
						<h3 className="text-sm font-medium">
							Third-Party Authentication
						</h3>
						<p className="text-muted-foreground mt-1 text-xs">
							Authenticate via a one-time password from a trusted
							third-party service.
						</p>
					</div>
					<Switch
						checked={thirdPartyAuthEnabled}
						onCheckedChange={setThirdPartyAuthEnabled}
						className="cursor-pointer"
					/>
				</div>

				{/* Reconfigure OTP */}
				<div className="flex items-center justify-between">
					<div>
						<h3 className="text-sm font-medium">Reconfigure OTP</h3>
						<p className="text-muted-foreground mt-1 text-xs">
							Update or reset your one-time password
							configuration.
						</p>
					</div>
					<Button
						variant="outline"
						size="sm"
						className="flex items-center cursor-pointer gap-2"
					>
						<Settings className="h-4 w-4" />
						Reconfigure
					</Button>
				</div>

				{/* Close Account Warning */}

				<div className="mt-8 flex flex-col gap-2 border-t border-t-[#E4E4E7] pt-4">
					<h3 className="text-lg font-medium text-[#DC2626]">
						Close Account
					</h3>
					<p className="text-muted-foreground text-xs">
						Warning! Deleting your account will permanently erase
						your current profile. You won't be able to get it back.
						If you're sure, contact support to delete your account.
					</p>
				</div>
			</section>
		</div>
	);
}
