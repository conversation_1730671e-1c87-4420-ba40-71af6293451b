import packageInfo from "../../package.json";

// How to use this:
// ============================================================
// This file is used to store all the environment variables and constants used in the application.

// # To add a new variable:
// ============================================================
// - For environment variables & constants that are the same across all environments, add them to the GLOBAL_CONSTANTS object.
// - For environment-specific variables (i.e they change depending on the environemnt), add them to the environment's object in each of the CONFIG_BUILDER object.

// # To add a new environment:
// ============================================================
// 1. Add a new key to the CONFIG_BUILDER object with the environment name.
// 2. Duplicate the development object and replace the values with the new environment's values.

const APP_VERSION = packageInfo.version;
const NODE_ENV = import.meta.env.NODE_ENV || "development";

const GLOBAL_CONSTANTS = {
	// System Constants
	// ============================================================
	APP_NAME: packageInfo.name,
	ENVIRONMENT: import.meta.env.VITE_PUBLIC_ENVIRONMENT as
		| "dev"
		| "staging"
		| "production",
	URL: {
		API_BASE_URL: import.meta.env.VITE_PUBLIC_BASE_API_URL,
		API_VERSION: import.meta.env.VITE_PUBLIC_BASE_API_VERSION,
		CLIENT_URL: import.meta.env.VITE_PUBLIC_CLIENT_URL,
	},

	// App Level Configs

	// Sentry & Monitoring Configs
	// ============================================================
	// SENTRY: {
	// 	RELEASE: APP_VERSION,
	// 	DSN: import.meta.env.VITE_SENTRY_DSN,
	// 	PROJECT: import.meta.env.VITE_ORGANISATION_PROJECT,
	// 	ORGANISATION: import.meta.env.VITE_ORGANISATION,
	// },

	// Google Configs
	// ============================================================
	GOOGLE: {
		OAUTH_CLIENT_ID: import.meta.env.VITE_PUBLIC_GOOGLE_OAUTH_CLIENT_ID,
		OAUTH_PROVIDER_CLIENT_ID:
			import.meta.env.VITE_PUBLIC_GOOGLE_OAUTH_PROVIDER_CLIENT_ID,
		OAUTH_MAPS_KEY: import.meta.env.VITE_PUBLIC_GOOGLE_OAUTH_MAPS_KEY,
	},

	MICROSOFT: {
		TENANT_ID: import.meta.env.VITE_PUBLIC_AZURE_TENANT_ID,
		CLIENT_ID: import.meta.env.VITE_PUBLIC_MICROSOFT_CLIENT_ID,
	},

	HELP_CRUNCH: {
		HELP_CRUNCH_APP_ID: import.meta.env.VITE_PUBLIC_HELP_CRUNCH_APP_ID,
	},

	INTERCOM: {
		INTERCOM_APP_ID: import.meta.env.VITE_PUBLIC_INTERCOM_APP_ID,
	},
};

// NOTE: Uncomment if you have different credentials for dev and prod

// const CONFIG_BUILDER = {
// 	development: {
// 		...GLOBAL_CONSTANTS,

// 		// System Constants
// 		// ============================================================
// 		URL: {
// 			API_BASE_URL: import.meta.env.VITE_BASE_URL,
// 		},

// 		// App Level Configs
// 		// ============================================================

// 		// e.g
// 		// STRIPE: {
// 		//     PUBLIC_KEY: "pk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
// 		// },
// 	},

// 	production: {
// 		...GLOBAL_CONSTANTS,

// 		// System Constants
// 		// ============================================================
// 		URL: {
// 			API_BASE_URL: "https://api.trail.com",
// 		},

// 		// App Level Configs
// 		// ============================================================

// 		// e.g
// 		// STRIPE: {
// 		//     PUBLIC_KEY: "pk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
// 		// },
// 	},
// } as const;

// Check if NODE_ENV is valid
// if (!Object.keys(CONFIG_BUILDER).includes(NODE_ENV)) {
// 	throw new Error(`Invalid NODE_ENV: ${NODE_ENV}`);
// }

// const CONFIGS = CONFIG_BUILDER[NODE_ENV as keyof typeof CONFIG_BUILDER];

const CONFIGS = GLOBAL_CONSTANTS;

// Uncomment below to check configs set
// console.log("CONFIGS:", CONFIGS);

export { NODE_ENV, APP_VERSION, CONFIGS };
