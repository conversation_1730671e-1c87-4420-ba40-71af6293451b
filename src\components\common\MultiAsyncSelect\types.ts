export interface Option {
	label: string;
	value: string; // should be unique, and not empty
}

export interface MultiAsyncSelectProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement> {
	/**
	 * An array of objects to be displayed in the Select.Option.
	 */
	options: Option[];

	/**
	 * Whether the select is async. If true, the getting options should be async.
	 * Optional, defaults to false.
	 */
	async?: boolean;

	/**
	 * Whether is fetching options. If true, the loading indicator will be shown.
	 * Optional, defaults to false. Works only when async is true.
	 */
	loading?: boolean;

	/**
	 * The error object. If true, the error message will be shown.
	 * Optional, defaults to null. Works only when async is true.
	 */
	error?: Error | null;

	/** The default selected values when the component mounts. */
	defaultValue?: string[];

	/**
	 * Placeholder text to be displayed when no values are selected.
	 * Optional, defaults to "Select options".
	 */
	placeholder?: string;

	/**
	 * Placeholder text to be displayed when the search input is empty.
	 * Optional, defaults to "Search...".
	 */
	searchPlaceholder?: string;

	/**
	 * Maximum number of items to display. Extra selected items will be summarized.
	 * Optional, defaults to 3.
	 */
	maxCount?: number;

	/**
	 * The modality of the popover. When set to true, interaction with outside elements
	 * will be disabled and only popover content will be visible to screen readers.
	 * Optional, defaults to false.
	 */
	modalPopover?: boolean;

	/**
	 * Additional class names to apply custom styles to the multi-select component.
	 * Optional, can be used to add custom styles.
	 */
	className?: string;

	/**
	 * Text to be displayed when the clear button is clicked.
	 * Optional, defaults to "Clear".
	 */
	clearText?: string;

	/**
	 * Text to be displayed when the close button is clicked.
	 * Optional, defaults to "Close".
	 */
	closeText?: string;

	/**
	 * Callback function triggered when the selected values change.
	 * Receives an array of the new selected values.
	 */
	onValueChange: (value: string[]) => void;

	/**
	 * Callback function triggered when the search input changes.
	 * Receives the search input value.
	 */
	onSearch?: (value: string) => void;
}

/**
 * Utility type for creating option objects
 */
export type CreateOption = (value: string, label?: string) => Option;

/**
 * Helper function to create option objects
 */
export const createOption: CreateOption = (value, label) => ({
	value,
	label: label || value,
});

/**
 * Predefined default configurations
 */
export const MULTI_ASYNC_SELECT_DEFAULTS = {
	placeholder: "Select options",
	searchPlaceholder: "Search...",
	clearText: "Clear",
	closeText: "Close",
	maxCount: 3,
	modalPopover: false,
	async: false,
	loading: false,
	error: null,
} as const;
