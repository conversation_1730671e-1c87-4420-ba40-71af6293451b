// import * as React from "react";
// import { cn } from "@/lib/utils";
// import { Inbox } from "lucide-react";
// import { Button } from "@/components/ui/button";

// export interface ActionButton {
// 	label: string;
// 	onClick: () => void;
// 	variant?: "primary" | "outline";
// 	disabled?: boolean;
// }

// export interface EmptyContentProps
// 	extends React.HTMLAttributes<HTMLDivElement> {
// 	title: string;
// 	description: string;
// 	icon?: React.ReactNode;
// 	actions?: ActionButton[];
// 	variant?: "default" | "compact";
// 	width?: string;
// }

// const EmptyContent = React.forwardRef<HTMLDivElement, EmptyContentProps>(
// 	(
// 		{
// 			className,
// 			title,
// 			description,
// 			icon,
// 			actions = [],
// 			variant = "default",
// 			width = "w-[480px]",
// 			...props
// 		},
// 		ref
// 	) => {
// 		const defaultIcon = <Inbox className="h-7 w-7 text-gray-700" />;

// 		return (
// 			<div
// 				ref={ref}
// 				className={cn(
// 					"inline-flex flex-col items-center justify-start gap-6 rounded-lg p-6 outline outline-1 outline-offset-[-1px] outline-[#E4E4E7]",
// 					width,
// 					variant === "compact" && "gap-4 p-4",
// 					className
// 				)}
// 				{...props}
// 			>
// 				<div className="relative h-8 w-8 overflow-hidden">
// 					{icon || defaultIcon}
// 				</div>

// 				<div className="flex flex-col items-center justify-start gap-2 self-stretch">
// 					<div className="self-stretch text-center text-lg leading-7 font-semibold text-gray-900">
// 						{title}
// 					</div>
// 					<div className="self-stretch text-center text-xs leading-none font-normal text-gray-500">
// 						{description}
// 					</div>
// 				</div>

// 				{actions.length > 0 && (
// 					<div className="inline-flex items-center justify-center gap-3 self-stretch">
// 						{actions.map((action, index) => (
// 							<Button
// 								key={index}
// 								onClick={action.onClick}
// 								disabled={action.disabled}
// 								variant={
// 									action.variant === "outline"
// 										? "outline"
// 										: "default"
// 								}
// 								size="sm"
// 								className="text-xs leading-none font-medium"
// 							>
// 								{action.label}
// 							</Button>
// 						))}
// 					</div>
// 				)}
// 			</div>
// 		);
// 	}
// );

// EmptyContent.displayName = "EmptyContent";

// export { EmptyContent };

import * as React from "react";
import { cn } from "@/lib/utils";
import { Inbox } from "lucide-react";
import { Button } from "@/components/ui/button";

export interface ActionButton {
	label: string;
	onClick: () => void;
	variant?: "primary" | "outline";
	disabled?: boolean;
}

export interface EmptyContentProps
	extends React.HTMLAttributes<HTMLDivElement> {
	title: string;
	description: string;
	icon?: React.ReactNode;
	actions?: ActionButton[];
	variant?: "default" | "compact";
	width?: string;
}

const EmptyContent = React.forwardRef<HTMLDivElement, EmptyContentProps>(
	(
		{
			className,
			title,
			description,
			icon,
			actions = [],
			variant = "default",
			width = "w-[480px]",
			...props
		},
		ref
	) => {
		const defaultIcon = <Inbox className="h-7 w-7 text-gray-700" />;

		return (
			<div
				ref={ref}
				className={cn(
					"inline-flex flex-col items-center justify-start gap-6 rounded-lg border border-dashed border-[#E4E4E7] p-6",
					width,
					variant === "compact" && "gap-4 p-4",
					className
				)}
				{...props}
			>
				<div className="relative h-8 w-8 overflow-hidden">
					{icon || defaultIcon}
				</div>

				<div className="flex flex-col items-center justify-start gap-2 self-stretch">
					<div className="self-stretch text-center text-lg leading-7 font-semibold text-gray-900">
						{title}
					</div>
					<div className="self-stretch text-center text-xs leading-none font-normal text-gray-500">
						{description}
					</div>
				</div>

				{actions.length > 0 && (
					<div className="inline-flex items-center justify-center gap-3 self-stretch">
						{actions.map((action, index) => (
							<Button
								key={index}
								onClick={action.onClick}
								disabled={action.disabled}
								variant={
									action.variant === "outline"
										? "outline"
										: "default"
								}
								size="lg"
								className="text-xs leading-none font-medium"
							>
								{action.label}
							</Button>
						))}
					</div>
				)}
			</div>
		);
	}
);

EmptyContent.displayName = "EmptyContent";

export { EmptyContent };