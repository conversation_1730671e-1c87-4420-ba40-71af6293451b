import React from "react";
import { Info, Image, Check, Trash2, <PERSON><PERSON><PERSON>, Settings2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import type { Location, LocationProvider } from "../../locations/types";
import { Badge } from "@/components/ui/badge";

export interface LocationProviderCardProps {
	locationProvider: LocationProvider;
	onEdit?: (locationProvider: LocationProvider) => void;
	onView?: (locationProvider: LocationProvider) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
}

export const LocationProviderCard: React.FC<LocationProviderCardProps> = ({
	locationProvider,
	onEdit,
	onView,
	isSelected = false,
	onSelectionChange,
}) => {
	const formatAddress = (address: Location["address"]): string => {
		return `${address.street}, ${address.city}.`;
	};

	const providersCount = 0; // TODO: Get actual providers count
	const servicesCount = locationProvider.services?.length || 0;

	return (
		<div
			className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
			onClick={() => onView?.(locationProvider)}
		>
			{/* Checkbox Section */}
			<div
				className="flex h-16 items-center px-4"
				onClick={(e) => e.stopPropagation()}
			>
				<Checkbox
					checked={isSelected}
					onCheckedChange={onSelectionChange}
					className="cursor-pointer"
				/>
			</div>

			{/* Location Info Section */}
			<div className="flex flex-2 items-center px-3">
				<div className="flex items-center gap-3">
					{/* Location Icon */}
					<div className="flex h-10 w-10 items-center justify-center rounded-full bg-[rgba(9,36,75,0.04)]">
						<Image size={14} />
					</div>

					{/* Location Details */}
					<div className="flex flex-1 flex-col gap-0.5">
						<div className="text-base leading-6 font-medium">
							{locationProvider.name}
						</div>
						<div className="flex items-center gap-1">
							<span className="text-xs leading-4 text-green-600">
								Available Today
							</span>
						</div>
					</div>
				</div>
			</div>

			{/* Providers Section */}
			<div className="flex h-16 w-[85px] min-w-[85px] flex-2 items-center justify-start gap-2.5 px-3">
				<Badge
					variant="outline"
					className="border-transparent bg-[#c3efce] text-[#0a2914]"
				>
					<Check className="h-4 w-4" />
					Active
				</Badge>
				<Badge
					variant="outline"
					className="border-transparent bg-[#c3efce] text-[#0a2914]"
				>
					<Check className="h-4 w-4" />
					Walk-in
				</Badge>
			</div>

			{/* Actions Section */}
			<div className="flex h-16 min-w-[72px] items-center justify-end px-3">
				<div className="flex items-center gap-2.5">
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(locationProvider);
						}}
					>
						<Info className="h-4 w-4" color="#71717A" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onEdit?.(locationProvider);
						}}
					>
						<Pencil className="h-4 w-4" color="#71717A" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							console.log(
								"QR Code action for:",
								locationProvider.name
							);
						}}
					>
						<Trash2 className="h-4 w-4" color="#71717A" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(locationProvider);
						}}
					>
						<Settings2 className="h-4 w-4" color="#71717A" />
					</Button>
				</div>
			</div>
		</div>
	);
};
