import React from "react";

const WorkplaceIcon: React.FC = () => {
	return (
		<svg
			width="32"
			height="33"
			viewBox="0 0 32 33"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M26.6665 23.4162C25.1238 24.5013 22.9474 24.5013 18.5948 24.5013H13.2853C8.93264 24.5013 6.75629 24.5013 5.21357 23.4162C4.64281 23.0148 4.14637 22.5161 3.74673 21.9428C2.6665 20.393 2.6665 18.2069 2.6665 13.8346C2.6665 9.46233 2.6665 7.27618 3.74673 5.72652C4.14637 5.15318 4.64281 4.6545 5.21357 4.25306C6.75629 3.16797 8.93264 3.16797 13.2853 3.16797"
				fill="#C4F6D9"
			/>
			<path
				d="M26.6665 23.4162C25.1238 24.5013 22.9474 24.5013 18.5948 24.5013H13.2853C8.93264 24.5013 6.75629 24.5013 5.21357 23.4162C4.64281 23.0148 4.14637 22.5161 3.74673 21.9428C2.6665 20.393 2.6665 18.2069 2.6665 13.8346C2.6665 9.46233 2.6665 7.27618 3.74673 5.72652C4.14637 5.15318 4.64281 4.6545 5.21357 4.25306C6.75629 3.16797 8.93264 3.16797 13.2853 3.16797"
				stroke="black"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path d="M16 24.5V29.8333" stroke="black" strokeWidth="2" />
			<path
				d="M10.6665 29.832H21.3332"
				stroke="black"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path
				d="M24.0002 3.16797C26.5143 3.16797 27.7714 3.16797 28.5524 3.94902C29.3335 4.73006 29.3335 5.98714 29.3335 8.5013V13.8346C29.3335 16.3488 29.3335 17.6058 28.5524 18.3869C27.7714 19.168 26.5143 19.168 24.0002 19.168H22.6668C20.1527 19.168 18.8956 19.168 18.1146 18.3869C17.3335 17.6058 17.3335 16.3488 17.3335 13.8346V8.5013C17.3335 5.98714 17.3335 4.73006 18.1146 3.94902C18.8956 3.16797 20.1527 3.16797 22.6668 3.16797H24.0002Z"
				stroke="black"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path
				d="M23.3335 15.832H23.3455"
				stroke="black"
				strokeWidth="2.66667"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
		</svg>
	);
};

export default WorkplaceIcon;
