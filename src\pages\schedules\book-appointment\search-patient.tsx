import { Input } from "@/components/ui/input";
import clsx from "clsx";
import { Check, Search } from "lucide-react";

type Props = {
    selectedPatient: number | null;
    setSelectedPatient: (selectedPatient: number) => void;
    onDoubleClick: () => void
}

export default function SearchPatient({
    selectedPatient,
    setSelectedPatient,
    onDoubleClick,
}: Props) {
    return (
        <div className="mx-10 mt-5">
            <div className="relative h-fit">
                <Search
                    color="#71717A"
                    size={17}
                    className="absolute left-4.5 top-3"
                />
                <Input
                    name="search-patient"
                    id="search-patient"
                    type="text"
                    placeholder="Search Patient"
                    className="py-5 pl-10.5 placeholder:font-light"
                />
            </div>

            <div className="space-y-4 pr-6 mt-5 custom-scrollbar overflow-y-auto max-h-[85vh]">
                {Array.from({ length: 10 }).map((_, index) => (
                    <button
                        key={index}
                        className={clsx("cursor-pointer w-full border-[1.5px] py-3.5 px-4 rounded-[10px] flex items-start justify-between transition-all duration-300 hover:border-[#005893]")}
                        tabIndex={0}
                        onClick={() => {
                            setSelectedPatient(index)
                            onDoubleClick()
                        }}
                    >
                        <div className="flex items-center justify-center gap-x-3">
                            <div className="w-12 grid place-content-center">
                            </div>
                            <div className="size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                AB
                            </div>
                            <div>
                                <h1 className="text-[#27272A] text-base">Thomas Edison</h1>
                                <p className="text-[#71717A] text-sm font-light"><EMAIL></p>
                            </div>
                        </div>
                        <h1 className="text-[#27272A] font-medium text-base">#UID</h1>
                    </button>
                ))}
            </div>

        </div>
    )
}