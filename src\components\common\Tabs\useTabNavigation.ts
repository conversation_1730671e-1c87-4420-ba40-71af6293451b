import { useNavigate, useSearchParams } from "react-router";

export interface UseTabNavigationOptions {
	searchParamKey?: string;
	defaultTab?: string;
}

export function useTabNavigation(options: UseTabNavigationOptions = {}) {
	const { searchParamKey = "tab", defaultTab } = options;
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();

	const currentTab = searchParams.get(searchParamKey) || defaultTab;

	const navigateToTab = (tabValue: string) => {
		const newSearchParams = new URLSearchParams(searchParams);
		newSearchParams.set(searchParamKey, tabValue);
		navigate(`?${newSearchParams.toString()}`, { replace: true });
	};

	const removeTabParam = () => {
		const newSearchParams = new URLSearchParams(searchParams);
		newSearchParams.delete(searchParamKey);
		const paramsString = newSearchParams.toString();
		navigate(paramsString ? `?${paramsString}` : "", { replace: true });
	};

	return {
		currentTab,
		navigateToTab,
		removeTabParam,
		searchParams,
	};
}
