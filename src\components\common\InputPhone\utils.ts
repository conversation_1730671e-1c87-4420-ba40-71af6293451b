import parsePhoneNumber, { isValidPhoneNumber } from "libphonenumber-js";
import { lookup } from "country-data-list";
import { z } from "zod";
import type { CountryData, PhoneFormat } from "./types";

// Export isValidPhoneNumber for use in components
export { isValidPhoneNumber };

// Phone validation schema
export const phoneSchema = z.string().refine((value) => {
	try {
		return isValidPhoneNumber(value);
	} catch {
		return false;
	}
}, "Invalid phone number");

// Format phone number based on format type
export const formatPhoneNumber = (
	value: string,
	format: PhoneFormat
): string => {
	try {
		const parsed = parsePhoneNumber(value);
		if (!parsed) return value;

		switch (format) {
			case "international":
				return parsed.formatInternational();
			case "national":
				return parsed.formatNational();
			case "e164":
				return parsed.format("E.164");
			default:
				return parsed.formatInternational();
		}
	} catch {
		return value;
	}
};

// Parse and validate phone number
export const parseAndValidatePhone = (value: string) => {
	try {
		// Ensure the value starts with "+"
		let normalizedValue = value;
		if (!normalizedValue.startsWith("+")) {
			// Replace "00" at the start with "+" if present
			if (normalizedValue.startsWith("00")) {
				normalizedValue = "+" + normalizedValue.slice(2);
			} else if (normalizedValue.length > 0) {
				// Otherwise just add "+" at the start
				normalizedValue = "+" + normalizedValue;
			}
		}

		const parsed = parsePhoneNumber(normalizedValue);

		return {
			parsed,
			isValid: parsed?.isValid() || false,
			isPossible: parsed?.isPossible() || false,
			country: parsed?.country,
			countryCallingCode: parsed?.countryCallingCode,
			nationalNumber: parsed?.nationalNumber,
			formattedNumber: parsed?.number,
		};
	} catch (error) {
		return {
			parsed: null,
			isValid: false,
			isPossible: false,
			country: undefined,
			countryCallingCode: undefined,
			nationalNumber: undefined,
			formattedNumber: value,
		};
	}
};

// Get country data by country code
export const getCountryData = (
	countryCode: string
): CountryData | undefined => {
	try {
		const countries = lookup.countries({
			alpha2: countryCode.toLowerCase(),
		});
		return countries[0];
	} catch {
		return undefined;
	}
};

// Get all countries for dropdown
export const getAllCountries = (): CountryData[] => {
	try {
		return lookup
			.countries({})
			.filter(
				(country: any) =>
					country.countryCallingCodes &&
					country.countryCallingCodes.length > 0
			)
			.sort((a: any, b: any) => a.name.localeCompare(b.name));
	} catch {
		return [];
	}
};

// Get country calling code with + prefix
export const getCountryCallingCode = (countryCode: string): string => {
	const countryData = getCountryData(countryCode);
	return countryData?.countryCallingCodes?.[0] || "";
};
