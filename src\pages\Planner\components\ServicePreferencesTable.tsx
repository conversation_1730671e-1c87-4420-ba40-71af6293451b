import React, { useState } from 'react';
import { Search, Plus, Edit, Trash2, ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';

interface ServicePreference {
  id: string;
  serviceName: string;
  preferenceTypes: string[];
  numberOfRules: number;
}

interface ServicePreferencesTableProps {
  onAddPreference: () => void;
  onViewServiceRules: (service: ServicePreference) => void;
  onEditPreference: (preferenceId: string) => void;
}

export const ServicePreferencesTable: React.FC<ServicePreferencesTableProps> = ({
  onAddPreference,
  onViewServiceRules,
  onEditPreference
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Sample data - replace with actual data from your store/API
  const servicePreferences: ServicePreference[] = [
    {
      id: '1',
      serviceName: 'General Medical Test',
      preferenceTypes: ['Availability', 'Frequency'],
      numberOfRules: 2,
    },
    {
      id: '2',
      serviceName: 'Blood Work',
      preferenceTypes: ['Availability', 'Frequency'],
      numberOfRules: 2,
    },
    {
      id: '3',
      serviceName: 'Immunization',
      preferenceTypes: ['Availability'],
      numberOfRules: 2,
    },
    {
      id: '4',
      serviceName: 'Report Reading',
      preferenceTypes: ['Availability', 'Frequency', 'Unavailability'],
      numberOfRules: 2,
    },
    {
      id: '5',
      serviceName: 'Video Consultation',
      preferenceTypes: ['Availability', 'Frequency'],
      numberOfRules: 2,
    },
    {
      id: '6',
      serviceName: 'General Consultation',
      preferenceTypes: ['Availability', 'Frequency'],
      numberOfRules: 2,
    },
  ];

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'availability':
        return 'bg-green-100 text-green-800';
      case 'frequency':
        return 'bg-blue-100 text-blue-800';
      case 'unavailability':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDeletePreference = (preferenceId: string) => {
    console.log('Deleting preference:', preferenceId);
  };

  const filteredPreferences = servicePreferences.filter(pref => 
    pref.serviceName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      {/* Table Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold">Service Preferences</h2>
        <div className="flex items-center space-x-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input 
              placeholder="Search" 
              className="pl-10 w-64"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button 
            className="bg-blue-600 hover:bg-blue-700"
            onClick={onAddPreference}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Preference
          </Button>
        </div>
      </div>

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-50">
            <TableHead className="w-12">
              <Checkbox />
            </TableHead>
            <TableHead>Service Name</TableHead>
            <TableHead>Preference Types</TableHead>
            <TableHead>No.of Rules</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredPreferences.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8">
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                    <Search className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-gray-900 mb-1">
                      {searchTerm ? 'No services found' : 'No Service Preferences Added'}
                    </h3>
                    <p className="text-gray-500">
                      {searchTerm 
                        ? 'No services found matching your search.' 
                        : 'Add Preferences to customize your experience.'
                      }
                    </p>
                    {!searchTerm && (
                      <Button 
                        onClick={onAddPreference}
                        className="mt-4 bg-blue-600 hover:bg-blue-700"
                      >
                        Add Preference
                      </Button>
                    )}
                  </div>
                </div>
              </TableCell>
            </TableRow>
          ) : (
            filteredPreferences.map((preference) => (
              <TableRow 
                key={preference.id} 
                className="hover:bg-gray-50 cursor-pointer"
                onClick={() => onViewServiceRules(preference)}
              >
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <Checkbox />
                </TableCell>
                <TableCell className="font-medium">{preference.serviceName}</TableCell>
                <TableCell>
                  <div className="flex space-x-1">
                    {preference.preferenceTypes.map((type) => (
                      <Badge key={type} className={getTypeColor(type)} variant="secondary">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell>{preference.numberOfRules}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditPreference(preference.id);
                      }}
                      title="Edit preference"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeletePreference(preference.id);
                      }}
                      title="Delete preference"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};