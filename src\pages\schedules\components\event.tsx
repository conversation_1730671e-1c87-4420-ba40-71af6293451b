import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuShortcut, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import moment from "moment"
import { IoEllipsisVerticalSharp } from "react-icons/io5"
import { TbUserSquare } from "react-icons/tb";
import { LuMessageCircleMore, LuTimerReset, LuTrash2 } from "react-icons/lu";
import { PiBellRinging } from "react-icons/pi";

type CustomDayEventProps = {
    index: number;
    start: Date;
    end: Date;
    title: string;
    arrLength: number;
    appointmentColors: { color: string, border: string }[];
}

const CustomDropdownMenu = () => {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <IoEllipsisVerticalSharp color="#3B5466" className="self-start" size={19} />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-80" align="end">
                <DropdownMenuLabel className="font-bold">Appointment Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                    <DropdownMenuItem>
                        <TbUserSquare />
                        <p className="ml-1">View Patient Information</p>
                        <DropdownMenuShortcut>
                            ⇧⌘P
                        </DropdownMenuShortcut>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                        <LuMessageCircleMore />
                        <p className="ml-1">Chat</p>
                        <DropdownMenuShortcut>
                            ⌘⇧C
                        </DropdownMenuShortcut>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                        <PiBellRinging />
                        <p className="ml-1">Send Reminder</p>
                        <DropdownMenuShortcut>
                            ⌘⇧R
                        </DropdownMenuShortcut>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                        <LuTimerReset />
                        <p className="ml-1">Reschedule Appointment</p>
                        <DropdownMenuShortcut>
                            ⌘⇧A
                        </DropdownMenuShortcut>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                    <DropdownMenuItem>
                        <LuTrash2 color="#DC2626" />
                        <p className="ml-1">Delete Appointment</p>
                        <DropdownMenuShortcut>
                            ⌘⇧D
                        </DropdownMenuShortcut>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

export const CustomEventComponent = ({
    index,
    start,
    end,
    title,
    arrLength,
    appointmentColors
}: CustomDayEventProps) => {
    const currentColor = appointmentColors[index] || appointmentColors[0];

    if (arrLength > 1) {
        return (
            <div className="rounded-xl flex flex-col justify-center gap-y-2 px-2" style={{
                backgroundColor: currentColor.color,
                border: `2px solid ${currentColor.border}`
            }}>
                <div className="flex items-start justify-between -mt-4">
                    <h1 className="text-[#18181B] text-base truncate font-semibold">{title}</h1>
                    <CustomDropdownMenu />
                </div>
                <div className="flex items-center gap-x-1">
                    <p className="text-[#71717A] text-sm truncate">
                        {moment(start).format("h:mm A")} – {moment(end).format("h:mm A")}
                    </p>
                </div>
            </div>
        )
    }
    return (
        <div key={index} className="rounded-xl px-3 py-3"
            style={{
                backgroundColor: currentColor.color,
                border: `2px solid ${currentColor.border}`
            }}
        >
            <div>
                <div className="flex items-start justify-between w-full mb-3">
                    <h1 className="text-[#18181B] text-base font-semibold">{title}</h1>
                    <CustomDropdownMenu />
                </div>
                <p className="text-[#71717A] text-sm">
                    {moment(start).format("h:mm A")} – {moment(end).format("h:mm A")}
                </p>
            </div>
        </div>
    )
}