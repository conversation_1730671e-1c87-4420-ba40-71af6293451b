import { useState, useMemo, useEffect } from "react";
import { X, Upload, User, Search, Info, HelpCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { InputPhone } from "@/components/common/InputPhone";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	Sheet,
	Sheet<PERSON>ontent,
	SheetHeader,
	SheetTitle,
} from "@/components/ui/sheet";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { Checkbox } from "@/components/common/Checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useAllStations } from "@/features/locations/hooks/useStations";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import type { Location } from "@/features/locations/types";
import type { StationData } from "@/features/locations/api/stationsApi";

interface TeamMember {
	id: number;
	name: string;
	email: string;
	phone_number: string;
	roles: Array<{
		role: string;
		stations?: Array<{ id: number; name: string }>;
		locations?: Array<{ id: number; name: string }>;
	}>;
	has_accepted: boolean;
	// Additional computed fields for UI
	displayRole?: string;
	status?: "Active" | "Unverified" | "Pending";
	dateOnboarded?: string;
	avatar?: string;
	// Additional details for editing
	socialRoles?: string[];
	roleDetails?: {
		[key: string]: string[];
	};
	locationAccess?: string[];
	timeZone?: string;
	autoMessage?: string;
	serviceManager?: string;
}

interface MemberFormData {
	memberInformation: {
		fullName: string;
		email: string;
		phoneNumber: string;
	};
	socialRoles: string[];
	roleDetails: {
		[key: string]: string[];
	};
	locationAccess: string[];
	autoMessage: string;
	serviceManager: string;
	timeZone: string;
}

interface EditMemberSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	member: TeamMember | null;
	onSubmit: (data: any) => void;
}

export function EditMemberSheet({
	open,
	onOpenChange,
	member,
	onSubmit,
}: EditMemberSheetProps) {
	// Initialize form data from member or with empty values
	const [formData, setFormData] = useState<MemberFormData>({
		memberInformation: {
			fullName: "",
			email: "",
			phoneNumber: "",
		},
		socialRoles: [],
		roleDetails: {},
		locationAccess: [],
		autoMessage: "",
		serviceManager: "",
		timeZone: "EST (UTC-5)",
	});

	const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
	const [searchTerms, setSearchTerms] = useState({
		locations: "",
		stations: "",
		services: "",
		serviceManager: "",
	});

	// Get organization context and fetch data from APIs
	const { organizationId } = useOrganizationContext();

	// Fetch locations from API
	const {
		data: locationsResponse,
		isLoading: locationsLoading,
		error: locationsError,
	} = useLocations({}, organizationId || undefined);

	// Fetch all stations from API
	const {
		data: stationsResponse,
		isLoading: stationsLoading,
		error: stationsError,
	} = useAllStations({
		organizationId: organizationId || undefined,
		enabled: !!organizationId,
	});

	// Update form data when member changes
	useEffect(() => {
		console.log("Member data changed:", member);
		if (member) {
			// Extract roles from the API roles array and convert to display format
			const apiRoles = member.roles || [];
			const socialRoles: string[] = [];
			const roleDetails: { [key: string]: string[] } = {};
			const locationAccess: string[] = [];

			// Convert API roles to social roles format
			apiRoles.forEach((roleObj) => {
				const displayRole = roleObj.role
					.toLowerCase()
					.replace(/_/g, " ")
					.replace(/\b\w/g, (l) => l.toUpperCase());

				socialRoles.push(displayRole);

				// Extract stations and locations for this role
				const details: string[] = [];
				if (roleObj.stations) {
					details.push(...roleObj.stations.map((s) => s.name));
				}
				if (roleObj.locations) {
					details.push(...roleObj.locations.map((l) => l.name));
					// Also add to locationAccess
					roleObj.locations.forEach((loc) => {
						if (!locationAccess.includes(loc.name)) {
							locationAccess.push(loc.name);
						}
					});
				}
				if (details.length > 0) {
					roleDetails[displayRole] = details;
				}
			});

			setFormData({
				memberInformation: {
					fullName: member.name || "",
					email: member.email || "",
					phoneNumber: member.phone_number || "",
				},
				socialRoles: socialRoles,
				roleDetails: roleDetails,
				locationAccess: locationAccess,
				autoMessage: member.autoMessage || "",
				serviceManager: member.serviceManager || "",
				timeZone: member.timeZone || "EST (UTC-5)",
			});
			setAvatarPreview(member.avatar || null);
		}
	}, [member]);

	const socialRoleOptions = [
		{
			name: "Organization Manager",
			backendRole: "BUSINESS_MANAGER" as const,
			description:
				"Ultimate access for all system aspects, user management, system settings, data oversight, and full platform operation across all locations.",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>All Locations, All Stations, All Members</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove (
							<span className="text-blue-400">Locations</span>,{" "}
							<span className="text-blue-400">Team Members</span>,{" "}
							<span className="text-blue-400">Stations</span>,){" "}
							Edit Settings (
							<span className="text-blue-400">Business</span> +{" "}
							<span className="text-blue-400">Appointments</span>
							); Reset Account Details of Admins & Partners
						</p>
					</div>
				</div>
			),
		},
		{
			name: "Location Manager",
			backendRole: "LOCATION_MANAGER" as const,
			description:
				"Creates and oversees stations, waitlist, and schedule operations for selected location(s).",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>Selected Locations, All Stations, All Partners</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove (
							<span className="text-blue-400">Team Members</span>,{" "}
							<span className="text-blue-400">Stations</span>);{" "}
							Edit Settings(
							<span className="text-blue-400">
								Location +
							</span>,{" "}
							<span className="text-blue-400">Appointments</span>)
						</p>
					</div>
				</div>
			),
		},
		{
			name: "Station Manager",
			backendRole: "STATION_MANAGER" as const,
			description:
				"Creates and oversees waitlist and schedule operations for selected station(s).",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>Selected Stations, All Partners</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove (
							<span className="text-blue-400">
								Stations they manage
							</span>
							,{" "}
							<span className="text-blue-400">Appointments</span>,{" "}
							<span className="text-blue-400">Services</span>,{" "}
							<span className="text-blue-400">Forms</span>,{" "}
							<span className="text-blue-400">Categories</span>,{" "}
							<span className="text-blue-400">Stations</span>);{" "}
							Edit Settings(
							<span className="text-blue-400">
								Waitlist +{" "}
							</span>,{" "}
							<span className="text-blue-400">Schedule</span>)
						</p>
					</div>
				</div>
			),
		},
		{
			name: "Service Manager",
			backendRole: "SERVICE_MANAGER" as const,
			description:
				"Creates and oversees waitlist and schedule operations for their tagged station(s).",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>All Locations, All Stations</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove
							<span className="text-blue-400">Appointments</span>;
							View (
							<span className="text-blue-400">
								All Appointments
							</span>
							,{" "}
							<span className="text-blue-400">All Services</span>,{" "}
							<span className="text-blue-400">All Locations</span>
							,{" "}
							<span className="text-blue-400">All Stations</span>,{" "}
							)
						</p>
					</div>
				</div>
			),
		},
		{
			name: "Team Member",
			backendRole: "TEAM_MEMBER" as const,
			description:
				"Can view all activity at selected station/location(s).",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>Selected Stations, Patients</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove (
							<span className="text-blue-400">Services</span>,{" "}
							<span className="text-blue-400">Forms</span>,{" "}
							<span className="text-blue-400">Appointments</span>,{" "}
							<span className="text-blue-400">Waitlist</span>,{" "}
							<span className="text-blue-400">Clients</span>
							); Add Client Notes
						</p>
					</div>
				</div>
			),
		},
	];

	// Get locations and stations from API responses
	const locationOptions = useMemo(() => {
		if (!locationsResponse) return [];
		return locationsResponse.map((location: Location) => location.name);
	}, [locationsResponse]);

	const stationOptions = useMemo(() => {
		if (!stationsResponse?.data) return [];
		return stationsResponse.data.map(
			(station: StationData) => station.name
		);
	}, [stationsResponse]);

	// Define options for roles that have dropdowns
	const roleDropdownOptions = useMemo(
		() => ({
			"Station Manager": stationOptions,
			"Service Manager": [
				"Primary Care Services",
				"Specialty Services",
				"Emergency Services",
				"Preventive Care",
				"Diagnostic Services",
			], // TODO: Fetch from services API when available
			"Location Manager": locationOptions,
		}),
		[stationOptions, locationOptions]
	);

	const serviceManagers = [
		{ value: "janet-samuel", label: "Janet Samuel" },
		{ value: "michael-johnson", label: "Michael Johnson" },
		{ value: "sarah-lee", label: "Sarah Lee" },
		{ value: "david-brown", label: "David Brown" },
		{ value: "emma-wilson", label: "Emma Wilson" },
		{ value: "alex-garcia", label: "Alex Garcia" },
	];

	// Filtered data based on search terms
	const filteredLocations = useMemo(() => {
		return locationOptions.filter((location: string) =>
			location.toLowerCase().includes(searchTerms.locations.toLowerCase())
		);
	}, [searchTerms.locations, locationOptions]);

	const filteredStationOptions = useMemo(() => {
		if (!roleDropdownOptions["Station Manager"]) return [];
		return roleDropdownOptions["Station Manager"].filter(
			(station: string) =>
				station
					.toLowerCase()
					.includes(searchTerms.stations.toLowerCase())
		);
	}, [searchTerms.stations, roleDropdownOptions]);

	const filteredServiceOptions = useMemo(() => {
		if (!roleDropdownOptions["Service Manager"]) return [];
		return roleDropdownOptions["Service Manager"].filter(
			(service: string) =>
				service
					.toLowerCase()
					.includes(searchTerms.services.toLowerCase())
		);
	}, [searchTerms.services, roleDropdownOptions]);

	const filteredServiceManagers = useMemo(() => {
		return serviceManagers.filter((manager) =>
			manager.label
				.toLowerCase()
				.includes(searchTerms.serviceManager.toLowerCase())
		);
	}, [searchTerms.serviceManager]);

	const handleInputChange = (field: string, value: string) => {
		if (field.includes(".")) {
			const [section, subField] = field.split(".");
			setFormData((prev) => ({
				...prev,
				[section]: {
					...(prev[section as keyof MemberFormData] as object),
					[subField]: value,
				},
			}));
		} else {
			setFormData((prev) => ({
				...prev,
				[field]: value,
			}));
		}
	};

	const handleRoleToggle = (roleName: string) => {
		setFormData((prev) => {
			const isRoleSelected = prev.socialRoles.includes(roleName);
			const newSocialRoles = isRoleSelected
				? prev.socialRoles.filter((r) => r !== roleName)
				: [...prev.socialRoles, roleName];

			// If removing a role, also remove its details
			const newRoleDetails = { ...prev.roleDetails };
			if (isRoleSelected) {
				delete newRoleDetails[roleName];
			}

			return {
				...prev,
				socialRoles: newSocialRoles,
				roleDetails: newRoleDetails,
			};
		});
	};

	const handleRoleDetailToggle = (role: string, detail: string) => {
		setFormData((prev) => ({
			...prev,
			roleDetails: {
				...prev.roleDetails,
				[role]: prev.roleDetails[role]?.includes(detail)
					? prev.roleDetails[role].filter((d) => d !== detail)
					: [...(prev.roleDetails[role] || []), detail],
			},
		}));
	};

	const handleLocationToggle = (location: string) => {
		setFormData((prev) => ({
			...prev,
			locationAccess: prev.locationAccess.includes(location)
				? prev.locationAccess.filter((l) => l !== location)
				: [...prev.locationAccess, location],
		}));
	};

	const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setAvatarPreview(e.target?.result as string);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleSubmit = () => {
		onSubmit({ ...formData, id: member?.id });
		onOpenChange(false);
	};

	const isFormValid =
		formData.memberInformation.fullName &&
		formData.memberInformation.email &&
		formData.socialRoles.length > 0;

	return (
		<TooltipProvider>
			<Sheet open={open} onOpenChange={onOpenChange}>
				<SheetContent className="z-[1003] w-[600px] overflow-y-auto px-8 py-10 sm:max-w-[600px]">
					<SheetHeader className="pb-6">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<SheetTitle className="text-xl font-semibold">
									Edit Member
								</SheetTitle>
							</div>
						</div>
					</SheetHeader>

					<div className="w-full space-y-6">
						{/* Member Information */}
						<div className="w-full space-y-4">
							<div className="flex items-center gap-2">
								<h3 className="text-lg font-medium">
									Member Information
								</h3>
							</div>
							{/* Avatar Upload
						<div className="flex items-center gap-4">
							<Avatar className="h-16 w-16">
								<AvatarImage src={avatarPreview || undefined} />
								<AvatarFallback className="bg-gray-100">
									<User className="h-6 w-6 text-gray-400" />
								</AvatarFallback>
							</Avatar>
							<div>
								<input
									type="file"
									accept="image/*"
									onChange={handleAvatarUpload}
									className="hidden"
									id="avatar-upload"
								/>
								<label htmlFor="avatar-upload">
									<Button
										variant="outline"
										className="cursor-pointer"
										asChild
									>
										<span>
											<Upload className="mr-2 h-4 w-4" />
											Upload Photo
										</span>
									</Button>
								</label>
							</div>
						</div> */}
							<InputText
								label="Full Name *"
								variant={"with-label"}
								className="w-full"
								placeholder="e.g John Doe"
								value={formData.memberInformation.fullName}
								onChange={(e) =>
									handleInputChange(
										"memberInformation.fullName",
										e.target.value
									)
								}
							/>
							<InputText
								label="Email Address *"
								placeholder="e.g. <EMAIL>"
								type="email"
								variant={"with-label"}
								value={formData.memberInformation.email}
								onChange={(e) =>
									handleInputChange(
										"memberInformation.email",
										e.target.value
									)
								}
							/>
							<div className="space-y-2">
								<div className="flex items-center gap-2">
									<label className="text-sm font-medium text-gray-900">
										Phone Number
									</label>
								</div>
								<InputPhone
									variant="with-country-dropdown"
									value={
										formData.memberInformation.phoneNumber
									}
									onChange={(value) =>
										handleInputChange(
											"memberInformation.phoneNumber",
											value
										)
									}
									defaultCountry="US"
									placeholder="Enter phone number"
									className="w-full"
									showFlag={true}
									format="international"
									searchable={true}
									showValidation={true}
								/>
							</div>
						</div>

						{/* Social Roles */}
						<div className="space-y-4">
							<div>
								<div className="flex items-center gap-2">
									<h3 className="text-lg font-medium">
										Social Roles
									</h3>
								</div>
								<p className="mt-1 text-sm text-gray-500">
									Choose the role(s) you want to assign to
									this member. Some roles may carry more
									permissions than others.
								</p>
							</div>

							<div className="space-y-3">
								{socialRoleOptions.map((roleOption) => (
									<div
										key={roleOption.name}
										className="space-y-3 rounded-md border p-3"
									>
										<div className="flex items-center space-x-3">
											<Checkbox
												checked={formData.socialRoles.includes(
													roleOption.name
												)}
												onCheckedChange={() =>
													handleRoleToggle(
														roleOption.name
													)
												}
											/>
											<div className="flex-1">
												<div className="flex items-center gap-2">
													<label className="cursor-pointer text-sm font-medium text-gray-900">
														{roleOption.name}
													</label>
													<Tooltip>
														<TooltipTrigger asChild>
															<Info className="h-3 w-3 cursor-help text-gray-400" />
														</TooltipTrigger>
														<TooltipContent>
															{
																roleOption.tooltipContent
															}
														</TooltipContent>
													</Tooltip>
												</div>
												<p className="text-xs text-gray-500">
													{roleOption.description}
												</p>
											</div>
										</div>

										{/* Show dropdown options for specific roles */}
										{formData.socialRoles.includes(
											roleOption.name
										) &&
											roleDropdownOptions[
												roleOption.name as keyof typeof roleDropdownOptions
											] && (
												<div className="ml-8 space-y-2">
													<div className="flex items-center gap-2">
														<p className="text-sm font-medium text-gray-700">
															Select{" "}
															{roleOption.name.replace(
																" Manager",
																""
															)}{" "}
															Options:
														</p>
														<Tooltip>
															<TooltipTrigger
																asChild
															>
																<Info className="h-3 w-3 cursor-help text-gray-400" />
															</TooltipTrigger>
															<TooltipContent>
																<p>
																	{roleOption.name ===
																		"Station Manager" &&
																		"Choose which stations this manager will oversee"}
																	{roleOption.name ===
																		"Service Manager" &&
																		"Choose which services this manager will be responsible for"}
																	{roleOption.name ===
																		"Location Manager" &&
																		"Choose which locations this manager will oversee"}
																</p>
															</TooltipContent>
														</Tooltip>
													</div>
													{/* Search input for role options */}
													<div className="relative">
														<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
														<Input
															placeholder={`Search ${roleOption.name.replace(" Manager", "").toLowerCase()}...`}
															className="pl-10"
															value={
																roleOption.name ===
																"Station Manager"
																	? searchTerms.stations
																	: roleOption.name ===
																		  "Service Manager"
																		? searchTerms.services
																		: ""
															}
															onChange={(e) => {
																const value =
																	e.target
																		.value;
																setSearchTerms(
																	(prev) => ({
																		...prev,
																		...(roleOption.name ===
																			"Station Manager" && {
																			stations:
																				value,
																		}),
																		...(roleOption.name ===
																			"Service Manager" && {
																			services:
																				value,
																		}),
																	})
																);
															}}
														/>
													</div>
													<div className="max-h-32 space-y-2 overflow-y-auto">
														{(roleOption.name ===
														"Station Manager"
															? filteredStationOptions
															: roleOption.name ===
																  "Service Manager"
																? filteredServiceOptions
																: roleDropdownOptions[
																		roleOption.name as keyof typeof roleDropdownOptions
																	]
														).map(
															(
																option: string
															) => (
																<div
																	key={option}
																	className="flex items-center space-x-2"
																>
																	<Checkbox
																		checked={
																			formData.roleDetails[
																				roleOption
																					.name
																			]?.includes(
																				option
																			) ||
																			false
																		}
																		onCheckedChange={() =>
																			handleRoleDetailToggle(
																				roleOption.name,
																				option
																			)
																		}
																	/>
																	<label className="cursor-pointer text-sm text-gray-600">
																		{option}
																	</label>
																</div>
															)
														)}
														{/* Show "No results" message */}
														{((roleOption.name ===
															"Station Manager" &&
															filteredStationOptions.length ===
																0) ||
															(roleOption.name ===
																"Service Manager" &&
																filteredServiceOptions.length ===
																	0)) &&
															(roleOption.name ===
															"Station Manager"
																? searchTerms.stations
																: searchTerms.services) && (
																<p className="text-sm text-gray-500 italic">
																	No{" "}
																	{roleOption.name
																		.replace(
																			" Manager",
																			""
																		)
																		.toLowerCase()}{" "}
																	found
																</p>
															)}
													</div>
												</div>
											)}
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Footer Actions */}
					<div className="flex justify-end gap-3 border-t pt-6">
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="outline"
									onClick={() => onOpenChange(false)}
								>
									Cancel
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>Cancel and close without saving</p>
							</TooltipContent>
						</Tooltip>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									onClick={handleSubmit}
									disabled={!isFormValid}
									className="migranium-button"
								>
									Update Member
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									{!isFormValid
										? "Please fill in required fields (Name, Email, and at least one role)"
										: "Update the member's information"}
								</p>
							</TooltipContent>
						</Tooltip>
					</div>
				</SheetContent>
			</Sheet>
		</TooltipProvider>
	);
}
