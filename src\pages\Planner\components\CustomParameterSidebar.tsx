import React from 'react';
import { Check } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CustomParameter {
  id: string;
  label: string;
  enabled: boolean;
}

interface CustomParameterSidebarProps {
  preferenceType: 'availability' | 'restriction';
  onPreferenceTypeChange: (type: 'availability' | 'restriction') => void;
  customParameters: CustomParameter[];
  onParameterToggle: (id: string) => void;
}

export const CustomParameterSidebar: React.FC<CustomParameterSidebarProps> = ({
  preferenceType,
  onPreferenceTypeChange,
  customParameters,
  onParameterToggle
}) => {
  return (
    <div className="w-80 p-6">
      <div className="space-y-6">
        {/* Select Preferences for */}
        <div>
          <h3 className="font-medium mb-3">Select Preferences for</h3>
          <div className="space-y-2">
            <Button 
              variant={preferenceType === 'availability' ? 'default' : 'outline'}
              className="w-full justify-start"
              onClick={() => onPreferenceTypeChange('availability')}
            >
              Availability
            </Button>
            <Button 
              variant={preferenceType === 'restriction' ? 'default' : 'outline'}
              className="w-full justify-start"
              onClick={() => onPreferenceTypeChange('restriction')}
            >
              Restriction
            </Button>
          </div>
        </div>

        {/* Select Custom Parameters */}
        <div>
          <h3 className="font-medium mb-3">Select Custom Parameters</h3>
          <div className="space-y-2">
            {customParameters.map((param) => (
              <div 
                key={param.id} 
                className={`flex items-center justify-between p-3 border rounded cursor-pointer transition-colors ${
                  param.enabled ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:bg-gray-50'
                }`}
                onClick={() => onParameterToggle(param.id)}
              >
                <span className="text-sm">{param.label}</span>
                <div className={`w-4 h-4 rounded-full flex items-center justify-center transition-colors ${
                  param.enabled ? 'bg-blue-600' : 'bg-gray-400'
                }`}>
                  {param.enabled && <Check className="h-3 w-3 text-white" />}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};