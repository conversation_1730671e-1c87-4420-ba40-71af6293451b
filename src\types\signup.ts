// import { TokenResponse } from "@react-oauth/google";
import { z } from "zod";

export const SignUpSchema = z
	.object({
		name: z.string().regex(/^[a-zA-Z ]+$/, {
			message: "Name must contain only letters and spaces",
		}),

		password: z
			.string()
			.min(8, { message: "Password must be at least 8 characters long" })
			.regex(/[A-Z]/, {
				message: "Password must contain at least one uppercase letter",
			})
			.regex(/[a-z]/, {
				message: "Password must contain at least one lowercase letter",
			})
			.regex(/\d/, {
				message: "Password must contain at least one number",
			})
			.regex(/[@$!%*?&#]/, {
				message: "Password must contain at least one special character",
			}),
		password_confirmation: z.string(),

		email: z
			.string()
			.email({ message: "Invalid email address format" })
			.refine((value) => !value.endsWith(".ru"), {
				message: "Email domain '.ru' is not allowed",
			})
			.refine((value) => !value.endsWith(".xyz"), {
				message: "Email domain '.xyz' is not allowed",
			})
			.refine((value) => !value.endsWith(".tk"), {
				message: "Email domain '.tk' is not allowed",
			}),

		agree_to_terms: z.boolean(),
	})
	.refine((data) => data.password === data.password_confirmation, {
		message: "Passwords do not match",
		path: ["password_confirmation"],
	})
	.refine((data) => data.agree_to_terms === true, {
		message: "You must agree to the terms and conditions",
		path: ["agree_to_terms"],
	});

export type SignUpType = z.infer<typeof SignUpSchema>;

export const UserLoginSchema = z.object({
	email: z
		.string()
		.min(4, { message: "Email must be at least 4 characters" })
		.email({ message: "Invalid email address format" }),
	password: z.string().min(1, { message: "Password too short" }),
	remember_token: z.string().optional(),
	remember_me: z.boolean().optional(),
});

export type UserLoginType = z.infer<typeof UserLoginSchema>;

export interface User {
	id: number;
	name: string;
	email: string;
	role: string | null;
	job_title: string | null;
	stripe_customer_id: string | null;
	created_at: string;
	updated_at: string | null;
	stripe_id: string | null;
	pm_type: string | null;
	pm_last_four: string | null;
	trial_ends_at: string | null;
	business_id: number;
	is_email_verified: number;
	is_active: number;
	business: Business;
	onboarding_state: number;
	phone_number: string | null;
	theme: string | null;
	two_factor_enable: boolean;
}

export interface Business {
	id: number | null;
	name: string | null;
	address: string | null;
	country: string | null;
	state: string | null;
	city: string | null;
	phone_number: string | null;
	logo_url: string | null;
	admin_id: number | null;
	created_at: string | null;
	updated_at: string | null;
	is_active: number | null;
	theme: string | null;
	business_category_id: number | null;
	schedule_auto_confirm: number | null;
	locations: Location[];
	room_booking_locations: Location[];
	custom_fields: CustomFields[];
	products: any[];
	use_average_wait_time: number | null;
	average_waiting_time: string | null;
	zip_code?: string | null;
}

export interface CustomFields {
	id: number;
	business_id: number;
	field: string;
	is_optional: number; // Alternatively, use boolean if is_optional can only be 0 or 1
	is_active: number; // Alternatively, use boolean if is_active can only be 0 or 1
	fieldKey: string;
}

export interface Location {
	id: number;
	name: string;
	address: string;
	country: string | null;
	state: string | null;
	city: string | null;
	admin_id: number | null;
	business_id: number | null;
	approximate_waiting_time: string | null;
	created_at: string | null;
	updated_at: string | null;
	is_active: number | null;
	available_slots_per_day: number | null;
	time_zone: string | null;
	schedule_block_in_min: number | null;
	stations: Station[];
}

export interface Station {
	id: number;
	name: string;
	location_id: number;
	admin_id: number;
	created_at: string;
	updated_at: string | null;
	is_active: number | null;
	business_id: number;
	schedule_before: string | null;
	approximate_waiting_time: string | null;
	schedule_auto_confirm: number | null;
	url_code: string | null;
	schedule_url: string | null;
	queue_url: string | null;
	is_queue_active: boolean | null;
	is_authorized_to_user: boolean | null;
}

export interface GoogleAccessTokenData {
	access_token: string;
	token_type: string;
	expires_in: number;
	scope: string;
	authuser: string;
	prompt: string;
}

export interface GoogleUserProfile {
	id: string;
	email: string;
	verified_email: boolean;
	name: string;
	given_name: string;
	family_name: string;
	picture: string;
	locale: string;
}

export interface GoogleUserCompleteRegisterParama {
	access_token_data:
		| Omit<any, "error" | "error_description" | "error_uri">
		// | Omit<TokenResponse, "error" | "error_description" | "error_uri">
		| undefined;
	profile: GoogleUserProfile;
}

export interface BusinessCategory {
	id: number;
	name: string;
	created_at: Date | null;
	updated_at: Date | null;
}

export interface AuthTwoEnabledFactorResponse {
	status: boolean;
	message: string;
	data: {
		twoFactor: boolean;
		token: string;
		expires_in: number;
	};
}

export interface AzureUserToken {
	aud: string;
	iss: string;
	iat: number;
	nbf: number;
	exp: number;
	acct: number;
	acr: string;
	aio: string;
	amr: string[];
	app_displayname: string;
	appid: string;
	appidacr: string;
	family_name: string;
	given_name: string;
	idtyp: string;
	ipaddr: string;
	name: string;
	oid: string;
	platf: string;
	puid: string;
	rh: string;
	scp: string;
	sub: string;
	tenant_region_scope: string;
	tid: string;
	unique_name: string;
	upn: string;
	uti: string;
	ver: string;
	wids: string[];
	xms_ftd: string;
	xms_idrel: string;
	xms_st: {
		sub: string;
	};
	xms_tcdt: number;
}

export interface RegisterErrorResponse {
	status: boolean;
	message: string;
	errors: Record<string, string[]>;
}

export interface RegisterResponse {
	status: boolean;
	message: string;
	data: object;
}
