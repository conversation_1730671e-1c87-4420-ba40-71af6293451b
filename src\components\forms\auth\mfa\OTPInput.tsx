import React, { useRef, useEffect, useState } from "react";

const cn = (...classes: string[]) => classes.filter(Boolean).join(" ");

interface OTPInputProps {
	value: string;
	onChange: (value: string) => void;
	label?: string;
	labelStyles?: string;
	required?: boolean;
	length?: number;
	error?: string;
	onComplete?: (value: string) => void;
	seperator?: boolean;
	seperatorLine?: number;
	textMode?: boolean;
}

const OTPInput: React.FC<OTPInputProps> = ({
	value,
	onChange,
	label,
	labelStyles,
	required,
	length = 6,
	error,
	onComplete,
	seperator,
	seperatorLine,
	textMode,
}) => {
	const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
	const [otpArray, setOtpArray] = useState<string[]>(
		value.split("").slice(0, length)
	);

	useEffect(() => {
		inputRefs.current = inputRefs.current.slice(0, length);
	}, [length]);

	useEffect(() => {
		setOtpArray(
			value
				.split("")
				.slice(0, length)
				.concat(Array(length).fill(""))
				.slice(0, length)
		);
	}, [value, length]);

	const updateOTPValue = (index: number, newValue: string) => {
		const newOtpArray = [...otpArray];
		newOtpArray[index] = newValue;
		setOtpArray(newOtpArray);

		const newValue2 = newOtpArray.join("");
		onChange(newValue2);

		if (newValue2.length === length && !newValue2.includes("")) {
			onComplete?.(newValue2);
		}
	};

	const handleInput = (index: number, value: string) => {
		if (textMode) {
			updateOTPValue(index, value);

			if (index < length - 1 && value) {
				inputRefs.current[index + 1]?.focus();
			}
		} else {
			const numbersOnly = value.replace(/[^0-9]/g, "");
			updateOTPValue(index, numbersOnly);

			if (index < length - 1 && numbersOnly) {
				inputRefs.current[index + 1]?.focus();
			}
		}
	};

	const handleKeyDown = (
		index: number,
		e: React.KeyboardEvent<HTMLInputElement>
	) => {
		if (e.key === "Backspace") {
			e.preventDefault();

			if (otpArray[index]) {
				updateOTPValue(index, "");
			} else if (index > 0) {
				updateOTPValue(index - 1, "");
				inputRefs.current[index - 1]?.focus();
			}
		} else if (e.key === "ArrowLeft" && index > 0) {
			e.preventDefault();
			inputRefs.current[index - 1]?.focus();
		} else if (e.key === "ArrowRight" && index < length - 1) {
			e.preventDefault();
			inputRefs.current[index + 1]?.focus();
		}
	};

	const handlePaste = (e: React.ClipboardEvent, startIndex: number) => {
		e.preventDefault();
		const pastedData = e.clipboardData.getData("text");

		if (pastedData) {
			const newOtpArray = [...otpArray];
			pastedData.split("").forEach((char, i) => {
				const index = startIndex + i;
				if (index < length) {
					newOtpArray[index] = textMode
						? char
						: char.replace(/[^0-9]/g, "");
				}
			});

			setOtpArray(newOtpArray);
			onChange(newOtpArray.join(""));

			const nextIndex = Math.min(
				startIndex + pastedData.length,
				length - 1
			);
			inputRefs.current[nextIndex]?.focus();
		}
	};

	return (
		<div>
			{label && (
				<label
					className={cn(
						"mb-1.5 block text-sm font-medium",
						labelStyles || ""
					)}
				>
					{label}{" "}
					{required && <span className="text-red-600">*</span>}
				</label>
			)}
			<div className="space-y-2">
				<div className="flex flex-wrap items-center justify-start gap-2">
					{Array.from({ length }, (_, index) => (
						<div key={index}>
							<input
								key={index}
								type={textMode ? "text" : "text"}
								inputMode={textMode ? "text" : "numeric"}
								maxLength={1}
								value={otpArray[index] || ""}
								onChange={(e) =>
									handleInput(index, e.target.value)
								}
								onKeyDown={(e) => handleKeyDown(index, e)}
								onPaste={(e) => handlePaste(e, index)}
								ref={(el) => {
									inputRefs.current[index] = el;
								}}
								className={cn(
									"h-12 w-12 rounded-lg border-2 text-center text-xl transition-all duration-200",
									"focus:border-[#043a6c] focus:outline-none",
									error
										? "border-red-500"
										: "border-[#E5E5E7]",
									"disabled:cursor-not-allowed disabled:bg-gray-100"
								)}
								autoComplete="off"
								aria-label={`OTP digit ${index + 1}`}
							/>
							{seperator &&
								(index + 1) % (seperatorLine ?? 3) === 0 &&
								index !== length - 1 && (
									<span className="mx-2">-</span>
								)}
						</div>
					))}
				</div>
				{error && (
					<p className="text-center text-sm text-red-500">{error}</p>
				)}
			</div>
		</div>
	);
};

export default OTPInput;
