import { useUIStore } from "@/stores/uiStore";
import { useEffect, useCallback, useState, useMemo } from "react";
import { useNavigate } from "react-router";
import { Button } from "@/components/ui/button";
import {
	Settings,
	Download,
	Plus,
	Settings2,
	RefreshCcw,
	Upload,
	Search,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { useDebounce } from "@/hooks/useDebounce";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import {
	AllPatientListCard,
	type Patient,
} from "@/components/ui-components/AllPatientListCard";
import { Checkbox } from "@/components/common/Checkbox";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import { PatientDetailsSheet } from "@/components/dashboard/patient/PatientDetailsSheet";
import {
	AddPatientSheet,
	type PatientFormData,
} from "@/components/dashboard/patient/AddPatientSheet";
import { EditPatientSheet } from "@/components/dashboard/patient/EditPatientSheet";
import { PatientSettingsSheet } from "@/components/dashboard/patient/PatientSettingsSheet";
import type { PatientSettings } from "@/components/dashboard/patient/PatientSettingsSheet";
import { ClientFilterSheet } from "@/components/dashboard/patient/ClientFilterSheet";
import type { ClientFilterData } from "@/components/dashboard/patient/ClientFilterSheet";
import {
	useClients,
	transformClientToPatient,
	useDeleteClient,
} from "@/hooks/useClients";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";

export default function Clients() {
	const navigate = useNavigate();
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);

	const [currentPage, setCurrentPage] = useState(1);
	const [searchQuery, setSearchQuery] = useState("");
	const [statusFilter, setStatusFilter] = useState<string>("");
	const [appliedFilters, setAppliedFilters] =
		useState<ClientFilterData | null>(null);
	const itemsPerPage = 10;
	const debouncedSearchTerm = useDebounce(searchQuery, 300);

	const {
		data: clientsResponse,
		isLoading,
		error,
		refetch,
	} = useClients({
		page: currentPage,
		per_page: itemsPerPage,
		search: debouncedSearchTerm || undefined,
		status: statusFilter || undefined,
	});

	const allPatients = useMemo(() => {
		if (!clientsResponse?.data) return [];
		return clientsResponse.data.map(transformClientToPatient);
	}, [clientsResponse]);

	const patients = useMemo(() => {
		if (!appliedFilters) return allPatients;

		return allPatients.filter((patient) => {
			
			if (appliedFilters.patientsFrom.length > 0) {
			}

			if (appliedFilters.dateRange) {
			}

			// Location and station filtering would go here when that data is available
			if (
				appliedFilters.locations.length > 0 ||
				appliedFilters.stations.length > 0
			) {
			}

			return true;
		});
	}, [allPatients, appliedFilters]);

	const totalItems = clientsResponse?.meta?.pagination?.total || 0;
	const totalPages = clientsResponse?.meta?.pagination?.total_pages || 1;

	useEffect(() => {
		if (clientsResponse?.meta?.pagination) {
			const totalItemsCount = clientsResponse.meta.pagination.total;
			const maxPage = Math.ceil(totalItemsCount / itemsPerPage) || 1;

			if (currentPage > maxPage && maxPage > 0) {
				console.log(
					`Navigating from page ${currentPage} to page ${maxPage} after deletion`
				);
				setCurrentPage(maxPage);
			}
		}
	}, [clientsResponse?.meta?.pagination?.total, currentPage, itemsPerPage]);
	const [selectedPatients, setSelectedPatients] = useState<string[]>([]);
	const [selectedPatientForDetails, setSelectedPatientForDetails] =
		useState<Patient | null>(null);
	const [isPatientDetailsOpen, setIsPatientDetailsOpen] = useState(false);
	const [initialTab, setInitialTab] = useState<string>("details");
	const [isAddPatientOpen, setIsAddPatientOpen] = useState(false);
	const [isEditPatientOpen, setIsEditPatientOpen] = useState(false);
	const [selectedPatientForEdit, setSelectedPatientForEdit] =
		useState<Patient | null>(null);
	const [isPatientSettingsOpen, setIsPatientSettingsOpen] = useState(false);
	const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [patientToDelete, setPatientToDelete] = useState<Patient | null>(
		null
	);

	const deleteClientMutation = useDeleteClient({
		onSuccess: () => {
			setIsDeleteDialogOpen(false);
			setPatientToDelete(null);
			if (selectedPatientForDetails?.id === patientToDelete?.id) {
				setIsPatientDetailsOpen(false);
				setSelectedPatientForDetails(null);
			}
		},
		onError: (error) => {
			console.error("Failed to delete client:", error);
		},
	});

	const hasPatients = patients.length > 0;
	const currentPatients = patients;

	const handleSettings = useCallback(() => {
		setIsPatientSettingsOpen(true);
	}, []);

	const handleImportCSV = useCallback(() => {
		navigate("/dashboard/patients/import-csv");
	}, [navigate]);

	const handleAddPatient = useCallback(() => {
		setIsAddPatientOpen(true);
	}, []);

	const handleAddPatientSubmit = useCallback(
		(data: PatientFormData) => {
			refetch();
			setSelectedPatients([]);
			setCurrentPage(1);
		},
		[refetch]
	);

	const handleEditPatientSubmit = useCallback(
		(data: PatientFormData) => {
			refetch();
			setIsEditPatientOpen(false);
			setSelectedPatientForEdit(null);
		},
		[refetch]
	);

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
		setSelectedPatients([]);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		if (currentPage < totalPages) {
			handlePageChange(currentPage + 1);
		}
	}, [currentPage, totalPages, handlePageChange]);

	const handleEditPatient = useCallback((patient: Patient) => {
		setSelectedPatientForEdit(patient);
		setIsEditPatientOpen(true);
	}, []);

	const handleOpenPatientDetails = useCallback((patient: Patient) => {
		setSelectedPatientForDetails(patient);
		setInitialTab("details");
		setIsPatientDetailsOpen(true);
	}, []);

	const handleClosePatientDetails = useCallback(() => {
		setIsPatientDetailsOpen(false);
		setSelectedPatientForDetails(null);
	}, []);

	const handleDeletePatient = useCallback((patient: Patient) => {
		setPatientToDelete(patient);
		setIsDeleteDialogOpen(true);
	}, []);

	const handleConfirmDelete = useCallback(() => {
		if (patientToDelete) {
			deleteClientMutation.mutate(patientToDelete.id);
		}
	}, [patientToDelete, deleteClientMutation]);

	const handleCancelDelete = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setPatientToDelete(null);
	}, []);

	const handleInfoPatient = useCallback((patient: Patient) => {
		setSelectedPatientForDetails(patient);
		setInitialTab("details");
		setIsPatientDetailsOpen(true);
	}, []);

	const handleEmailPatient = useCallback((patient: Patient) => {
		setSelectedPatientForDetails(patient);
		setInitialTab("message");
		setIsPatientDetailsOpen(true);
	}, []);

	const handleSavePatientSettings = useCallback(
		(settings: PatientSettings) => {
			console.log("Saving patient settings:", settings);
		},
		[]
	);

	const handleOpenFilter = useCallback(() => {
		setIsFilterSheetOpen(true);
	}, []);

	const handleApplyFilters = useCallback((filters: ClientFilterData) => {
		console.log("Applying filters:", filters);
		setAppliedFilters(filters);
		setCurrentPage(1);
	}, []);

	const handleResetFilters = useCallback(() => {
		setStatusFilter("");
		setSearchQuery("");
		setAppliedFilters(null);
		setCurrentPage(1);
	}, []);

	const handleSelectAll = useCallback(
		(checked: boolean) => {
			if (checked) {
				const currentPagePatientIds = currentPatients.map(
					(patient) => patient.id
				);
				setSelectedPatients((prev) => {
					const newSelections = [
						...prev,
						...currentPagePatientIds.filter(
							(id) => !prev.includes(id)
						),
					];

					return newSelections;
				});
			} else {
				const currentPagePatientIds = currentPatients.map(
					(patient) => patient.id
				);
				setSelectedPatients((prev) => {
					const newSelections = prev.filter(
						(id) => !currentPagePatientIds.includes(id)
					);
					console.log(
						"Deselected all patients on current page:",
						currentPagePatientIds
					);
					return newSelections;
				});
			}
		},
		[currentPatients]
	);

	const handlePatientSelection = useCallback(
		(patientId: string, selected: boolean) => {
			if (selected) {
				setSelectedPatients((prev) => {
					const newSelection = [...prev, patientId];

					return newSelection;
				});
			} else {
				setSelectedPatients((prev) => {
					const newSelection = prev.filter((id) => id !== patientId);

					return newSelection;
				});
			}
		},
		[]
	);

	useEffect(() => {
		if (debouncedSearchTerm !== undefined) {
			setCurrentPage(1);
		}
	}, [debouncedSearchTerm]);

	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/patients",
			},
			{
				label: "All Patients",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<h1 className="text-foreground text-2xl font-bold">
					All Patients
				</h1>
				<div className="flex items-center space-x-3">
					<div className="relative max-w-md flex-1">
						<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
						<Input
							placeholder="Search by name, email..."
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							className="pl-10"
							id="search-field"
						/>
					</div>
					<Button variant="outline" size="icon" className="h-9 w-9">
						<Download className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-9 w-9"
						onClick={handleOpenFilter}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-9 w-9"
						onClick={() => refetch()}
					>
						<RefreshCcw className="h-4 w-4" />
					</Button>

					<Button
						variant="outline"
						onClick={handleImportCSV}
						className="h-9 px-4"
					>
						<Upload className="h-4 w-4" />
						Import CSV
					</Button>
					<Button
						variant="default"
						onClick={handleAddPatient}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Patient
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [
		setPageHeaderContent,
		searchQuery,
		handleOpenFilter,
		handleSettings,
		handleImportCSV,
		handleAddPatient,
		refetch,
	]);

	if (isLoading) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-[#005893]"></div>
					<p className="text-gray-600">Loading patients...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<p className="mb-4 text-red-600">Failed to load patients</p>
					<Button onClick={() => refetch()} variant="outline">
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-">
			{!hasPatients ? (
				<div className="flex min-h-[400px] items-center justify-center">
					<EmptyContent
						title="No patients added"
						description="Select file or Drag and drop here to import list CSV files up to 50 MB are accepted, or via Google Sheets and Microsoft Excel."
						actions={[
							{
								label: "Add New",
								onClick: handleAddPatient,
								variant: "primary",
							},
							{
								label: "Import",
								onClick: handleImportCSV,
								variant: "outline",
							},
						]}
					/>
				</div>
			) : (
				<div className="mt-2 overflow-hidden rounded-xl border border-[#E4E4E7]">
					<div className="overflow-x-auto">
						<table className="w-full min-w-[800px]">
							<thead>
								<tr className="border-b border-gray-200">
									<th className="w-12 px-4 py-[15.5px] text-left">
										<Checkbox
											checked={
												currentPatients.length > 0 &&
												currentPatients.every(
													(patient) =>
														selectedPatients.includes(
															patient.id
														)
												)
											}
											onCheckedChange={handleSelectAll}
											className="h-3 w-3 border-[#E4E4E7] [&_svg]:h-2 [&_svg]:w-2 [&>*]:flex [&>*]:items-center [&>*]:justify-center"
										/>
									</th>
									<th className="px-3 py-[15.5px] text-left">
										<div className="flex items-center gap-2">
											<p className="text-xs text-[#71717A]">
												Name
											</p>
											{selectedPatients.length > 0 && (
												<span className="text-xs text-gray-500">
													({selectedPatients.length}{" "}
													selected total)
												</span>
											)}
										</div>
									</th>
									<th className="w-24 px-3 py-[15.5px] text-left">
										<p className="text-xs text-[#71717A]">
											Status
										</p>
									</th>
									<th className="px-3 py-[15.5px] text-left">
										<p className="text-xs text-[#71717A]">
											Email
										</p>
									</th>
									<th className="w-32 px-3 py-[15.5px] text-left">
										<p className="text-xs text-[#71717A]">
											Phone
										</p>
									</th>
									<th className="w-28 px-3 py-[15.5px] text-left">
										<p className="text-xs text-[#71717A]">
											Last Visit
										</p>
									</th>
									<th className="w-20 px-3 py-[15.5px] text-right">
										<span className="sr-only">Actions</span>
									</th>
								</tr>
							</thead>
							<tbody>
								{currentPatients.map((patient) => (
									<AllPatientListCard
										key={patient.id}
										patient={patient}
										checked={selectedPatients.includes(
											patient.id
										)}
										onCheckboxChange={
											handlePatientSelection
										}
										onClick={() =>
											handleOpenPatientDetails(patient)
										}
										className="cursor-pointer hover:bg-gray-50"
										actions={[
											{
												type: "delete",
												onClick: handleDeletePatient,
											},
											{
												type: "edit",
												onClick: handleEditPatient,
											},
											{
												type: "email",
												onClick: handleEmailPatient,
											},
											{
												type: "info",
												onClick: handleInfoPatient,
											},
										]}
									/>
								))}
							</tbody>
						</table>
					</div>
				</div>
			)}
			{totalPages > 1 && (
				<div className="mt-2 flex justify-end">
					<div>
						<Pagination>
							<PaginationContent>
								<PaginationItem>
									<PaginationPrevious
										onClick={handlePreviousPage}
										className={
											currentPage === 1
												? "pointer-events-none opacity-50"
												: "cursor-pointer"
										}
									/>
								</PaginationItem>

								{totalPages <= 7 ? (
									Array.from(
										{ length: totalPages },
										(_, i) => i + 1
									).map((page) => (
										<PaginationItem key={page}>
											<PaginationLink
												onClick={() =>
													handlePageChange(page)
												}
												isActive={currentPage === page}
												className="cursor-pointer"
											>
												{page}
											</PaginationLink>
										</PaginationItem>
									))
								) : (
									<>
										<PaginationItem>
											<PaginationLink
												onClick={() =>
													handlePageChange(1)
												}
												isActive={currentPage === 1}
												className="cursor-pointer"
											>
												1
											</PaginationLink>
										</PaginationItem>

										{currentPage > 3 && (
											<PaginationItem>
												<PaginationEllipsis />
											</PaginationItem>
										)}

										{Array.from(
											{ length: totalPages },
											(_, i) => i + 1
										)
											.filter((page) => {
												if (
													page === 1 ||
													page === totalPages
												)
													return false;
												return (
													Math.abs(
														page - currentPage
													) <= 1
												);
											})
											.map((page) => (
												<PaginationItem key={page}>
													<PaginationLink
														onClick={() =>
															handlePageChange(
																page
															)
														}
														isActive={
															currentPage === page
														}
														className="cursor-pointer"
													>
														{page}
													</PaginationLink>
												</PaginationItem>
											))}

										{currentPage < totalPages - 2 && (
											<PaginationItem>
												<PaginationEllipsis />
											</PaginationItem>
										)}

										<PaginationItem>
											<PaginationLink
												onClick={() =>
													handlePageChange(totalPages)
												}
												isActive={
													currentPage === totalPages
												}
												className="cursor-pointer"
											>
												{totalPages}
											</PaginationLink>
										</PaginationItem>
									</>
								)}

								<PaginationItem>
									<PaginationNext
										onClick={handleNextPage}
										className={
											currentPage === totalPages
												? "pointer-events-none opacity-50"
												: "cursor-pointer"
										}
									/>
								</PaginationItem>
							</PaginationContent>
						</Pagination>
					</div>
				</div>
			)}
			<PatientDetailsSheet
				open={isPatientDetailsOpen}
				onClose={handleClosePatientDetails}
				clientId={selectedPatientForDetails?.id}
				patient={
					selectedPatientForDetails
						? {
								name: selectedPatientForDetails.name,
								status: selectedPatientForDetails.status,
							}
						: undefined
				}
				onDelete={() => {}}
				initialTab={initialTab}
			/>
			<AddPatientSheet
				open={isAddPatientOpen}
				onOpenChange={setIsAddPatientOpen}
				onSubmit={handleAddPatientSubmit}
			/>
			<EditPatientSheet
				open={isEditPatientOpen}
				onOpenChange={setIsEditPatientOpen}
				onSubmit={handleEditPatientSubmit}
				clientId={selectedPatientForEdit?.id}
			/>
			<PatientSettingsSheet
				open={isPatientSettingsOpen}
				onOpenChange={setIsPatientSettingsOpen}
				onSave={handleSavePatientSettings}
			/>
			<ClientFilterSheet
				open={isFilterSheetOpen}
				onOpenChange={setIsFilterSheetOpen}
				onApplyFilters={handleApplyFilters}
				onResetFilters={handleResetFilters}
			/>
			<DeleteConfirmationDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
				title="Are you sure you want to delete this patient?"
				description={`This action cannot be undone and will permanently delete ${patientToDelete?.name || "this patient"} and all their information.`}
				onConfirm={handleConfirmDelete}
				onCancel={handleCancelDelete}
				confirmText="Delete Patient"
				isLoading={deleteClientMutation.isPending}
			/>
		</div>
	);
}
