import React, { useState, useEffect, forwardRef, useMemo } from "react";
import { Controller } from "react-hook-form";
import { CircleFlag } from "react-circle-flags";
import {
	GlobeIcon,
	ChevronDownIcon,
	SearchIcon,
	CheckCircleIcon,
	XCircleIcon,
	LoaderIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	FormControl,
	FormDescription,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type {
	InputPhoneProps,
	DefaultPhoneProps,
	WithCountryDropdownProps,
	FormPhoneProps,
	CountryData,
} from "./types";
import {
	parseAndValidatePhone,
	formatPhoneNumber,
	getCountryData,
	getAllCountries,
	getCountryCallingCode,
	isValidPhoneNumber,
} from "./utils";

// Default Phone Input Implementation
const DefaultPhoneInput = forwardRef<HTMLInputElement, DefaultPhoneProps>(
	(
		{
			value = "",
			onChange,
			onCountryChange,
			defaultCountry = "US",
			showFlag = true,
			format = "international",
			className,
			placeholder = "Enter phone number",
			...props
		},
		ref
	) => {
		const [countryData, setCountryData] = useState<
			CountryData | undefined
		>();
		const [displayFlag, setDisplayFlag] = useState<string>("");
		const [hasInitialized, setHasInitialized] = useState(false);

		// Initialize with default country
		useEffect(() => {
			if (defaultCountry && !hasInitialized) {
				const newCountryData = getCountryData(defaultCountry);
				if (newCountryData) {
					setCountryData(newCountryData);
					setDisplayFlag(defaultCountry.toLowerCase());

					// Set initial country calling code if no value
					if (!value && newCountryData.countryCallingCodes?.[0]) {
						onChange?.(newCountryData.countryCallingCodes[0]);
					}
				}
				setHasInitialized(true);
			}
		}, [defaultCountry, onChange, value, hasInitialized]);

		const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			const inputValue = e.target.value;
			const phoneData = parseAndValidatePhone(inputValue);

			if (phoneData.country) {
				// Update country data and flag
				const newCountryData = getCountryData(phoneData.country);
				setCountryData(newCountryData);
				setDisplayFlag(phoneData.country.toLowerCase());
				onCountryChange?.(newCountryData);
			} else if (!inputValue.trim()) {
				// Clear country data if input is empty
				setCountryData(undefined);
				setDisplayFlag("");
				onCountryChange?.(undefined);
			}

			// Format the phone number based on the format prop
			const formattedValue = phoneData.formattedNumber || inputValue;
			onChange?.(
				format === "international" ? formattedValue : inputValue
			);
		};

		return (
			<div
				className={cn(
					"border-input has-[input:focus]:ring-ring relative flex h-9 items-center gap-2 rounded-md border bg-transparent px-3 text-base shadow-sm transition-colors disabled:cursor-not-allowed disabled:opacity-50 has-[input:focus]:ring-1 has-[input:focus]:outline-none md:text-sm",
					className
				)}
			>
				{showFlag && (
					<div className="flex h-4 w-4 shrink-0 items-center justify-center rounded-full">
						{displayFlag ? (
							<CircleFlag
								countryCode={displayFlag}
								height={16}
								width={16}
							/>
						) : (
							<GlobeIcon
								size={16}
								className="text-muted-foreground"
							/>
						)}
					</div>
				)}
				<input
					ref={ref}
					value={value}
					onChange={handlePhoneChange}
					placeholder={placeholder}
					type="tel"
					autoComplete="tel"
					className="placeholder:text-muted flex h-9 w-full border-none bg-transparent p-0 py-1 text-base leading-none transition-colors outline-none md:text-sm"
					{...props}
				/>
			</div>
		);
	}
);

// With Country Dropdown Implementation
const WithCountryDropdownPhoneInput = forwardRef<
	HTMLInputElement,
	WithCountryDropdownProps
>(
	(
		{
			value = "",
			onChange,
			onCountryChange,
			onCountrySelect,
			defaultCountry = "US",
			showFlag = true,
			format = "international",
			className,
			dropdownClassName,
			searchable = true,
			showValidation = false,
			error = false,
			disabled = false,
			placeholder = "Enter phone number",
			...props
		},
		ref
	) => {
		const [selectedCountry, setSelectedCountry] = useState<
			CountryData | undefined
		>();
		const [countries, setCountries] = useState<CountryData[]>([]);
		const [searchQuery, setSearchQuery] = useState("");
		const [isDropdownOpen, setIsDropdownOpen] = useState(false);
		const [isValid, setIsValid] = useState<boolean | null>(null);
		const [isLoadingCountries, setIsLoadingCountries] = useState(true);
		const [isValidating, setIsValidating] = useState(false);

		// Load countries with loading state
		useEffect(() => {
			const loadCountries = async () => {
				setIsLoadingCountries(true);
				try {
					// Simulate a small delay to show loading state
					// await new Promise((resolve) => setTimeout(resolve, 100));
					const allCountries = getAllCountries();
					setCountries(allCountries);
				} catch (error) {
					console.error("Failed to load countries:", error);
					setCountries([]);
				} finally {
					setIsLoadingCountries(false);
				}
			};

			loadCountries();
		}, []);

		// Filter countries based on search query with memoization
		const filteredCountries = useMemo(() => {
			if (isLoadingCountries) return [];

			return countries.filter(
				(country) =>
					country.name
						.toLowerCase()
						.includes(searchQuery.toLowerCase()) ||
					country.alpha2
						.toLowerCase()
						.includes(searchQuery.toLowerCase()) ||
					country.countryCallingCodes?.[0]?.includes(searchQuery)
			);
		}, [countries, searchQuery, isLoadingCountries]);

		// Validate phone number in real-time with loading state
		const validatePhoneNumber = async (phoneValue: string) => {
			if (!phoneValue || phoneValue.length < 3) {
				setIsValid(null);
				setIsValidating(false);
				return;
			}

			setIsValidating(true);
			try {
				// Add a small delay to show validation loading state
				await new Promise((resolve) => setTimeout(resolve, 200));
				const valid = isValidPhoneNumber(phoneValue);
				setIsValid(valid);
			} catch {
				setIsValid(false);
			} finally {
				setIsValidating(false);
			}
		};

		// Initialize with default country
		useEffect(() => {
			if (defaultCountry) {
				const countryData = getCountryData(defaultCountry);
				setSelectedCountry(countryData);
				onCountryChange?.(countryData);
			}
		}, [defaultCountry, onCountryChange]);

		// Clear search when dropdown closes
		useEffect(() => {
			if (!isDropdownOpen) {
				setSearchQuery("");
			}
		}, [isDropdownOpen]);

		const handleCountrySelect = (country: CountryData) => {
			setSelectedCountry(country);
			onCountryChange?.(country);
			onCountrySelect?.(country.alpha2);
			setSearchQuery(""); // Clear search when country is selected
			setIsDropdownOpen(false); // Close dropdown

			// Update phone number with new country code
			const callingCode = country.countryCallingCodes?.[0];
			if (callingCode) {
				onChange?.(callingCode);
			}
		};

		const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			const inputValue = e.target.value;
			const phoneData = parseAndValidatePhone(inputValue);

			// Update selected country if detected from phone number
			if (phoneData.country) {
				const newCountryData = getCountryData(phoneData.country);
				if (
					newCountryData &&
					newCountryData.alpha2 !== selectedCountry?.alpha2
				) {
					setSelectedCountry(newCountryData);
					onCountryChange?.(newCountryData);
				}
			}

			const formattedValue = phoneData.formattedNumber || inputValue;
			const finalValue =
				format === "international" ? formattedValue : inputValue;

			// Validate phone number if showValidation is enabled
			if (showValidation) {
				validatePhoneNumber(finalValue);
			}

			onChange?.(finalValue);
		};

		return (
			<div className="flex gap-0">
				{/* Country Dropdown */}
				<DropdownMenu
					open={isDropdownOpen}
					onOpenChange={setIsDropdownOpen}
				>
					<DropdownMenuTrigger asChild>
						<Button
							variant="outline"
							className={cn(
								"h-9 rounded-r-none border-r-0 bg-transparent px-3 hover:bg-inherit",
								error && "border-destructive",
								(disabled || isLoadingCountries) &&
									"cursor-not-allowed opacity-50",
								dropdownClassName
							)}
						>
							<div className="flex items-center gap-2">
								{isLoadingCountries ? (
									<LoaderIcon
										size={16}
										className="text-muted-foreground animate-spin"
									/>
								) : showFlag && selectedCountry ? (
									<CircleFlag
										countryCode={selectedCountry.alpha2.toLowerCase()}
										height={16}
										width={16}
									/>
								) : (
									<GlobeIcon
										size={16}
										className="text-muted-foreground"
									/>
								)}
								<span className="text-muted-foreground text-sm">
									{isLoadingCountries
										? "..."
										: selectedCountry
												?.countryCallingCodes?.[0] ||
											"+1"}
								</span>
								<ChevronDownIcon
									size={12}
									className="text-muted-foreground"
								/>
							</div>
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent className="z-[1050] w-80 p-0">
						{/* Search Input */}
						{searchable && (
							<div className="border-b p-2">
								<div className="relative">
									<SearchIcon className="text-muted-foreground absolute top-1/2 left-2 h-4 w-4 -translate-y-1/2" />
									<Input
										placeholder="Search countries..."
										value={searchQuery}
										onChange={(e) =>
											setSearchQuery(e.target.value)
										}
										className="pl-8 text-sm"
									/>
								</div>
							</div>
						)}

						{/* Countries List */}
						<div className="max-h-60 overflow-y-auto">
							{isLoadingCountries ? (
								<div className="flex items-center justify-center py-8">
									<div className="text-muted-foreground flex items-center gap-2">
										<LoaderIcon
											size={16}
											className="animate-spin"
										/>
										<span className="text-sm">
											Loading countries...
										</span>
									</div>
								</div>
							) : filteredCountries.length > 0 ? (
								filteredCountries.map((country) => (
									<DropdownMenuItem
										key={country.alpha2}
										onClick={() =>
											handleCountrySelect(country)
										}
										className="flex cursor-pointer items-center gap-2 px-3 py-2"
									>
										<div className="h-4 w-4 shrink-0">
											<CircleFlag
												countryCode={country.alpha2.toLowerCase()}
												height={16}
												width={16}
											/>
										</div>
										<span className="flex-1 truncate">
											{country.name}
										</span>
										<span className="text-muted-foreground text-sm">
											{country.countryCallingCodes?.[0]}
										</span>
									</DropdownMenuItem>
								))
							) : (
								<div className="text-muted-foreground px-3 py-6 text-center text-sm">
									{searchQuery
										? "No countries found"
										: "No countries available"}
								</div>
							)}
						</div>
					</DropdownMenuContent>
				</DropdownMenu>

				{/* Phone Input */}
				<div
					className={cn(
						"border-input has-[input:focus]:ring-ring relative flex h-9 flex-1 items-center rounded-md rounded-l-none border border-l-0 bg-transparent px-3 text-base shadow-sm transition-colors disabled:cursor-not-allowed disabled:opacity-50 has-[input:focus]:ring-1 has-[input:focus]:outline-none md:text-sm",
						error &&
							"border-destructive has-[input:focus]:ring-destructive",
						disabled && "cursor-not-allowed opacity-50",
						className
					)}
				>
					<input
						ref={ref}
						value={value}
						onChange={handlePhoneChange}
						placeholder={placeholder}
						disabled={disabled}
						type="tel"
						autoComplete="tel"
						className="placeholder:text-muted flex h-9 w-full border-none bg-transparent p-0 py-1 pr-8 text-base leading-none transition-colors outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
						{...props}
					/>

					{/* Validation Indicator */}
					{showValidation && value && value.length > 2 && (
						<div className="absolute top-1/2 right-2 -translate-y-1/2">
							{isValidating ? (
								<LoaderIcon className="h-4 w-4 animate-spin text-blue-500" />
							) : isValid === true ? (
								<CheckCircleIcon className="h-4 w-4 text-green-500" />
							) : isValid === false ? (
								<XCircleIcon className="h-4 w-4 text-red-500" />
							) : null}
						</div>
					)}
				</div>
			</div>
		);
	}
);

function FormPhoneInput({
	name,
	control,
	label,
	description,
	rules,
	defaultCountry = "US",
	showFlag = true,
	format = "international",
	className,
	placeholder = "Enter phone number",
	onCountryChange,
}: FormPhoneProps) {
	return (
		<Controller
			name={name}
			control={control}
			rules={rules}
			render={({ field, fieldState }) => (
				<FormItem>
					{label && <FormLabel>{label}</FormLabel>}
					<FormControl>
						<DefaultPhoneInput
							value={field.value}
							onChange={field.onChange}
							onBlur={field.onBlur}
							defaultCountry={defaultCountry}
							showFlag={showFlag}
							format={format}
							placeholder={placeholder}
							onCountryChange={onCountryChange}
							className={cn(
								fieldState.error &&
									"border-destructive focus-visible:ring-destructive",
								className
							)}
						/>
					</FormControl>
					{description && (
						<FormDescription>{description}</FormDescription>
					)}
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}

// Main InputPhone Component
export const InputPhone = forwardRef<HTMLInputElement, InputPhoneProps>(
	(props, ref) => {
		const { variant = "default", ...otherProps } = props;

		switch (variant) {
			case "with-country-dropdown":
				return (
					<WithCountryDropdownPhoneInput
						ref={ref}
						{...(otherProps as WithCountryDropdownProps)}
					/>
				);
			case "form":
				return <FormPhoneInput {...(otherProps as FormPhoneProps)} />;
			default:
				return (
					<DefaultPhoneInput
						ref={ref}
						{...(otherProps as DefaultPhoneProps)}
					/>
				);
		}
	}
);

InputPhone.displayName = "InputPhone";

DefaultPhoneInput.displayName = "DefaultPhoneInput";
WithCountryDropdownPhoneInput.displayName = "WithCountryDropdownPhoneInput";
