import React from "react";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { LocationTab } from "@/features/locations/components/LocationTab";
import { OrganizationTab } from "@/features/organizations/components/OrganizationTab";
import { ProviderTab } from "@/features/providers/components/ProviderTab";
import { CustomTab } from "@/features/custom/components/CustomTab";

interface ManagementTabsProps {
	className?: string;
}

export function ManagementTabs({ className }: ManagementTabsProps) {
	// Define tab items with counts - you can update these counts based on actual data
	const tabItems = [
		{ value: "organization", label: "Organization", count: 12 },
		{ value: "location", label: "Location" },
		{ value: "provider", label: "Provider" },
		{ value: "custom", label: "Custom", count: 12 },
	];

	return (
		<div className={className}>
			<Tabs
				items={tabItems}
				useRouting={true}
				searchParamKey="tab"
				defaultTab="organization"
				className="w-full"
			>
				<TabsContent value="organization">
					<OrganizationTab className="mt-6" />
				</TabsContent>

				<TabsContent value="location">
					<LocationTab className="mt-6" />
				</TabsContent>

				<TabsContent value="provider">
					<ProviderTab className="mt-6" />
				</TabsContent>

				<TabsContent value="custom">
					<CustomTab className="mt-6" />
				</TabsContent>
			</Tabs>
		</div>
	);
}
