import { useQuery } from "@tanstack/react-query";
import { stationsApi } from "../api/stationsApi";
import type { GetStationsResponse } from "../api/stationsApi";

interface UseStationsParams {
	locationId?: string;
	organizationId?: number;
	enabled?: boolean;
}

interface UseAllStationsParams {
	organizationId?: number;
	enabled?: boolean;
}

export function useStations({
	locationId,
	organizationId,
	enabled = true,
}: UseStationsParams) {
	return useQuery<GetStationsResponse, Error>({
		queryKey: ["stations", locationId, organizationId],
		queryFn: () => {
			if (!locationId || !organizationId) {
				throw new Error("LocationId and organizationId are required");
			}
			return stationsApi.getStations(locationId, organizationId);
		},
		enabled: enabled && !!locationId && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}

export function useAllStations({
	organizationId,
	enabled = true,
}: UseAllStationsParams) {
	return useQuery<GetStationsResponse, Error>({
		queryKey: ["allStations", organizationId],
		queryFn: () => {
			if (!organizationId) {
				throw new Error("OrganizationId is required");
			}
			return stationsApi.getAllStations(organizationId);
		},
		enabled: enabled && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}
