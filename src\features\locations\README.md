# Locations Feature

The locations feature manages business locations within the workplace module. It provides comprehensive location management capabilities including creation, editing, viewing, and organizing locations.

## Structure

```
src/features/locations/
├── api/                    # API layer
│   ├── index.ts           # Barrel exports
│   └── locationsApi.ts    # Location API functions
├── components/            # UI components
│   ├── index.ts          # Barrel exports
│   ├── LocationsList.tsx # Main locations grid/list view
│   ├── LocationCard.tsx  # Individual location card
│   ├── LocationForm.tsx  # Create/edit location form
│   └── LocationDetails.tsx # Detailed location view modal
├── hooks/                 # Custom hooks
│   ├── index.ts          # Barrel exports
│   ├── useLocations.ts   # Fetch locations list
│   ├── useLocation.ts    # Fetch single location
│   └── useLocationMutations.ts # CRUD operations
├── types/                 # TypeScript definitions
│   └── index.ts          # Location-related types
├── utils/                 # Utility functions
│   ├── index.ts          # Barrel exports
│   └── locationUtils.ts  # Location helper functions
├── index.ts              # Feature barrel exports
└── README.md            # This file
```

## Key Features

### 1. Location Management

- **Create** new business locations
- **Edit** existing location details
- **Delete** locations (with confirmation)
- **Toggle** active/inactive status

### 2. Location Information

- **Basic Details**: Name, description, capacity
- **Address**: Complete address with timezone
- **Contact**: Phone number and email
- **Operating Hours**: Day-specific hours with closed days
- **Services**: Available services at location
- **Amenities**: Location amenities and features
- **Coordinates**: Latitude/longitude for mapping

### 3. Search & Filtering

- **Search** by location name or address
- **Filter** by active/inactive status
- **Filter** by city, state, or services
- **Sort** by name, city, creation date, or last updated
- **Pagination** for large location lists

## Types

### Core Types

```typescript
interface Location {
	id: string;
	name: string;
	address: Address;
	phone?: string;
	email?: string;
	description?: string;
	isActive: boolean;
	timezone: string;
	coordinates?: Coordinates;
	operatingHours?: OperatingHours[];
	services?: string[];
	capacity?: number;
	amenities?: string[];
	organizationId: string;
	createdAt: string;
	updatedAt: string;
}
```

### Request/Response Types

- `CreateLocationRequest` - For creating new locations
- `UpdateLocationRequest` - For updating existing locations
- `LocationsFilters` - For search and filtering
- `LocationsResponse` - Paginated API response

## API Integration

### Endpoints

- `GET /api/locations` - List locations with filters
- `GET /api/locations/:id` - Get single location
- `POST /api/locations` - Create new location
- `PUT /api/locations/:id` - Update location
- `DELETE /api/locations/:id` - Delete location
- `PATCH /api/locations/:id` - Update status/partial update

### Query Keys

Follows the established pattern in `src/lib/query/keys.ts`:

```typescript
locations: {
  all: ["locations"],
  lists: () => [...locations.all, "list"],
  list: (filters) => [...locations.lists(), filters],
  details: () => [...locations.all, "detail"],
  detail: (id) => [...locations.details(), id],
}
```

## Hooks

### Data Fetching

- `useLocations(filters)` - Fetch paginated locations list
- `useLocation(id)` - Fetch single location details

### Mutations

- `useCreateLocation()` - Create new location
- `useUpdateLocation()` - Update existing location
- `useDeleteLocation()` - Delete location
- `useToggleLocationStatus()` - Toggle active status

## Components

### LocationsList

Main component displaying locations in a grid layout with:

- Search and filtering controls
- Create location button
- Pagination controls
- Loading and error states

### LocationCard

Individual location card showing:

- Location name and status
- Address and contact info
- Services and capacity
- Action menu (view/edit/delete)

### LocationForm

Modal form for creating/editing locations with:

- Form validation using zod
- Step-by-step input sections
- Error handling and success feedback

### LocationDetails

Detailed view modal showing:

- Complete location information
- Edit and delete actions
- Status toggle functionality

## Utilities

### Formatting Functions

- `formatAddress()` - Format complete address string
- `formatPhoneNumber()` - Format phone numbers
- `formatOperatingHours()` - Format hours display

### Status Functions

- `getLocationStatus()` - Get status string
- `getLocationStatusColor()` - Get status color class
- `isLocationOpen()` - Check if location is currently open

### Location Functions

- `calculateDistance()` - Calculate distance between coordinates

## Usage Example

```typescript
import { LocationsList } from '@/features/locations';

// In a page component
const LocationsPage = () => {
  return (
    <div className="p-6">
      <LocationsList />
    </div>
  );
};
```

## Integration Points

### Navigation

- Accessible via Workplace → Locations
- Route: `/workplace/locations`
- Icon: LocationsIcon from sidebar

### Permissions

- Controlled by `workplace:locations` permissions
- Can be feature-flagged via `locations_module`

### Breadcrumbs

- Workplace → Locations
- Workplace → Locations → Location Name (details)

## Development Status

### ✅ Completed

- [x] Basic folder structure
- [x] TypeScript interfaces and types
- [x] Query keys configuration
- [x] API function signatures
- [x] Hook interfaces
- [x] Utility functions
- [x] Basic component structure

### 🚧 In Progress

- [ ] API integration (pending backend endpoints)
- [ ] Form validation and submission
- [ ] Real-time updates and caching
- [ ] Component styling and UX

### 📋 TODO

- [ ] Operating hours management UI
- [ ] Map integration for coordinates
- [ ] Advanced filtering options
- [ ] Location analytics/reporting
- [ ] Bulk operations (import/export)
- [ ] Location templates
- [ ] Image upload for locations

## Contributing

When working on the locations feature:

1. **Follow the established patterns** from other features
2. **Use TypeScript strictly** - define all interfaces
3. **Implement error handling** in all API calls
4. **Add proper loading states** for better UX
5. **Write comprehensive tests** for all functionality
6. **Update this README** when adding new features

## Dependencies

- React Query for data fetching
- React Hook Form + Zod for forms
- Lucide React for icons
- Tailwind CSS for styling
- shadcn/ui components
