// // import { useAuthStore } from '../../stores/permissionStore'

// // Context interface for scoped permissions
// export interface PermissionContext {
//   location?: string
//   station?: string
// }

// // Basic permission check
// export const can = (
//   permission: string, 
//   context?: PermissionContext
// ): boolean => {
//   const { hasPermission } = useAuthStore.getState()
//   return hasPermission(permission, context)
// }

// // Check multiple permissions (OR logic)
// export const canAny = (
//   permissions: string[], 
//   context?: PermissionContext
// ): boolean => {
//   const { hasAnyPermission } = useAuthStore.getState()
//   return hasAnyPermission(permissions, context)
// }

// // Check multiple permissions (AND logic)
// export const canAll = (
//   permissions: string[], 
//   context?: PermissionContext
// ): boolean => {
//   const { hasAllPermissions } = useAuthStore.getState()
//   return hasAllPermissions(permissions, context)
// }

// // Role checks
// export const hasRole = (role: string): boolean => {
//   const { hasRole } = useAuthStore.getState()
//   return hasRole(role)
// }

// export const hasAnyRole = (roles: string[]): boolean => {
//   const { hasAnyRole } = useAuthStore.getState()
//   return hasAnyRole(roles)
// }

// // Group checks
// export const inGroup = (group: string): boolean => {
//   const { inGroup } = useAuthStore.getState()
//   return inGroup(group)
// }

// export const inAnyGroup = (groups: string[]): boolean => {
//   const { inAnyGroup } = useAuthStore.getState()
//   return inAnyGroup(groups)
// }

// // Feature checks
// export const hasFeature = (feature: string): boolean => {
//   const { featureEnabled } = useAuthStore.getState()
//   return featureEnabled(feature)
// }

// // Field control utilities
// export const canViewField = (form: string, field: string): boolean => {
//   const { isFieldVisible } = useAuthStore.getState()
//   return isFieldVisible(form, field)
// }

// export const canEditField = (form: string, field: string): boolean => {
//   const { isFieldEditable } = useAuthStore.getState()
//   return isFieldEditable(form, field)
// }

// export const isFieldRequired = (form: string, field: string): boolean => {
//   const { isFieldRequired } = useAuthStore.getState()
//   return isFieldRequired(form, field)
// }

// export const getFieldControl = (form: string, field: string) => {
//   const { getFieldControl } = useAuthStore.getState()
//   return getFieldControl(form, field)
// }

// // Complex permission helpers
// export const canManageResource = (
//   resource: string, 
//   context?: PermissionContext
// ): boolean => {
//   return canAll([`${resource}:create`, `${resource}:edit`, `${resource}:delete`], context)
// }

// export const canViewResource = (
//   resource: string, 
//   context?: PermissionContext
// ): boolean => {
//   return can(`${resource}:view`, context)
// }

// // Location-specific helpers
// export const canInLocation = (
//   permission: string, 
//   locationId: string
// ): boolean => {
//   return can(permission, { location: locationId })
// }

// export const canInStation = (
//   permission: string, 
//   stationId: string
// ): boolean => {
//   return can(permission, { station: stationId })
// }

// // Combined permission and role check
// export const canWithRole = (
//   permission: string, 
//   requiredRole: string, 
//   context?: PermissionContext
// ): boolean => {
//   return hasRole(requiredRole) && can(permission, context)
// }

// // Admin check (combines role and permissions)
// export const isAdmin = (): boolean => {
//   return hasRole('admin') || can('admin:all')
// }

// // User context helpers
// export const getCurrentContext = () => {
//   const state = useAuthStore.getState()
//   return {
//     organizationId: state.organizationId,
//     productId: state.productId,
//     location: state.currentLocation,
//     station: state.currentStation,
//     role: state.role,
//   }
// } 