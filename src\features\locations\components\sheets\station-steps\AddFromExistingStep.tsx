import { useState } from "react";
import { ChevronLeft, Search } from "lucide-react";
import { InputText } from "@/components/common/InputText";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import { useStations } from "../../../hooks/useStations";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { Skeleton } from "@/components/ui/skeleton";

interface AddFromExistingStepProps {
	onBack?: () => void;
	onImport?: (selectedProviders: Provider[]) => void;
	locationId?: string;
}

interface Provider {
	id: string;
	name: string;
	email: string;
	avatar?: string;
	stationName: string;
	stationId: number;
}

export function AddFromExistingStep({
	onBack,
	onImport,
	locationId,
}: AddFromExistingStepProps) {
	const { organizationId } = useOrganizationContext();
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedProviders, setSelectedProviders] = useState<string[]>([]);

	// Fetch stations which contain provider information
	const {
		data: stationsResponse,
		isLoading: isLoadingStations,
		error: stationsError,
	} = useStations({
		locationId,
		organizationId: organizationId || undefined,
		enabled: !!locationId && !!organizationId,
	});

	// Transform stations data to providers format
	const providers: Provider[] =
		stationsResponse?.data
			?.filter((station) => station.service_providers.length > 0) // Only include stations with providers
			?.flatMap((station) =>
				station.service_providers.map((provider) => ({
					id: provider.id.toString(),
					name: `${provider.first_name} ${provider.last_name || ""}`.trim(),
					email: provider.email,
					avatar: station.image,
					stationName: station.name,
					stationId: station.id,
				}))
			) || [];

	// Remove duplicates based on provider email (in case same provider has multiple stations)
	const uniqueProviders = providers.filter(
		(provider, index, self) =>
			index === self.findIndex((p) => p.email === provider.email)
	);

	const filteredProviders = uniqueProviders.filter(
		(provider) =>
			provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			provider.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
			provider.stationName
				.toLowerCase()
				.includes(searchQuery.toLowerCase())
	);

	const handleProviderSelection = (providerId: string, checked: boolean) => {
		if (checked) {
			setSelectedProviders((prev) => [...prev, providerId]);
		} else {
			setSelectedProviders((prev) =>
				prev.filter((id) => id !== providerId)
			);
		}
	};

	const handleImport = () => {
		const selectedProviderData = uniqueProviders.filter((provider) =>
			selectedProviders.includes(provider.id)
		);
		onImport?.(selectedProviderData);
	};

	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((word) => word.charAt(0))
			.join("")
			.toUpperCase()
			.substring(0, 2);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center gap-3">
				<button
					onClick={onBack}
					className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
				>
					<ChevronLeft className="h-5 w-5 text-gray-600" />
				</button>
				<div>
					<h3 className="text-lg font-bold text-gray-900">
						Add from Existing
					</h3>
					<p className="text-sm text-gray-500">
						If the service provider stations already exists on the
						<br />
						platform, duplicate selected settings & calendar
					</p>
					{!isLoadingStations &&
						!stationsError &&
						uniqueProviders.length > 0 && (
							<p className="mt-1 text-xs text-blue-600">
								{uniqueProviders.length} provider
								{uniqueProviders.length !== 1 ? "s" : ""}{" "}
								available
							</p>
						)}
				</div>
			</div>

			{/* Search Bar */}
			<div className="pt-4">
				<div className="relative">
					<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
					<InputText
						placeholder="Search providers, stations, or emails..."
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="w-full border-gray-200 pl-10"
						variant="default"
					/>
				</div>
			</div>

			{/* Loading State */}
			{isLoadingStations && (
				<div className="space-y-4 py-4">
					{Array.from({ length: 5 }).map((_, index) => (
						<div
							key={index}
							className="flex items-center space-x-3 p-3"
						>
							<Skeleton className="h-4 w-4" />
							<Skeleton className="h-10 w-10 rounded-full" />
							<div className="flex-1 space-y-1">
								<Skeleton className="h-4 w-32" />
								<Skeleton className="h-3 w-48" />
							</div>
						</div>
					))}
				</div>
			)}

			{/* Error State */}
			{stationsError && (
				<div className="flex items-center justify-center py-8">
					<span className="text-sm text-red-500">
						Failed to load providers. Please try again.
					</span>
				</div>
			)}

			{/* Provider List */}
			{!isLoadingStations && !stationsError && (
				<div className="scrollbar-hide max-h-96 space-y-3 overflow-y-auto">
					{filteredProviders.map((provider) => (
						<label
							htmlFor={provider.id}
							key={provider.id}
							className="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-200 p-3 hover:bg-gray-50"
						>
							<Avatar className="h-10 w-10 rounded-full">
								<AvatarImage src={provider.avatar} />
								<AvatarFallback className="bg-gray-100 text-sm font-medium text-gray-600">
									{getInitials(provider.name)}
								</AvatarFallback>
							</Avatar>
							<div className="flex-1">
								<div className="font-medium text-gray-900">
									{provider.name}
								</div>
								<div className="text-sm text-gray-500">
									{provider.email}
								</div>
								<div className="text-xs text-gray-400">
									Station: {provider.stationName}
								</div>
							</div>
							<Checkbox
								checked={selectedProviders.includes(
									provider.id
								)}
								onCheckedChange={(checked) =>
									handleProviderSelection(
										provider.id,
										checked as boolean
									)
								}
								id={provider.id}
							/>
						</label>
					))}
				</div>
			)}

			{!isLoadingStations &&
				!stationsError &&
				filteredProviders.length === 0 &&
				uniqueProviders.length === 0 && (
					<div className="py-8 text-center">
						<p className="text-gray-500">
							{stationsResponse?.data?.length === 0
								? "No stations found. Create your first station to see providers here."
								: "No service providers found. The stations in this location don't have assigned providers yet."}
						</p>
					</div>
				)}

			{!isLoadingStations &&
				!stationsError &&
				filteredProviders.length === 0 &&
				uniqueProviders.length > 0 && (
					<div className="py-8 text-center">
						<p className="text-gray-500">
							No providers found matching your search.
						</p>
					</div>
				)}
		</div>
	);
}
