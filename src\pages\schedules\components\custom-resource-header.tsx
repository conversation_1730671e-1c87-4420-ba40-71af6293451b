import { type ResourceHeaderProps } from "react-big-calendar";

export const CustomResourceHeader = ({ label, resource }: ResourceHeaderProps & {
    resource: { resourceId: number; resourceTitle: string; color: string }
}) => (
    <div className="relative flex items-center py-4 px-5">
        <p style={{ backgroundColor: resource.color }} className="w-[8px] h-4 rounded-[1px] mr-2"></p>
        <span>{label}</span>
        <div className="z-[1000] absolute inset-x-0 -bottom-0 w-full h-[2px]" style={{ backgroundColor: resource.color }}></div>
    </div>
);