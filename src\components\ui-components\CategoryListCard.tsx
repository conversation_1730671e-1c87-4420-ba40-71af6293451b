import * as React from "react";
import { cn } from "@/lib/utils";
import { More<PERSON><PERSON>zon<PERSON>, Edit, Trash2, Info, Eye } from "lucide-react";

export interface Category {
	id: string;
	name: string;
	color: string;
	status: "Active" | "Inactive";
	clients: number;
	stations: number;
	dateAdded: string;
}

export interface CategoryAction {
	type: "delete" | "edit" | "info" | "view";
	onClick: (category: Category) => void;
	disabled?: boolean;
}

export interface CategoryListCardProps
	extends React.HTMLAttributes<HTMLDivElement> {
	category: Category;
	actions?: CategoryAction[];
	variant?: "default" | "compact";
}

const CategoryListCard = React.forwardRef<
	HTMLDivElement,
	CategoryListCardProps
>(
	(
		{
			className,
			category,
			actions = [],
			variant = "default",
			...props
		},
		ref
	) => {
		const defaultActions: CategoryAction[] = [
			{
				type: "view",
				onClick: (category) => console.log("View", category.id),
			},
			{
				type: "delete",
				onClick: (category) => console.log("Delete", category.id),
			},
			{
				type: "edit",
				onClick: (category) => console.log("Edit", category.id),
			},
			{
				type: "info",
				onClick: (category) => console.log("Info", category.id),
			},
		];

		const finalActions = actions.length > 0 ? actions : defaultActions;

		const getActionIcon = (type: CategoryAction["type"]) => {
			switch (type) {
				case "edit":
					return <Edit className="h-3 w-3 text-gray-500" />;
				case "info":
					return <Info className="h-3 w-3 text-gray-500" />;
				case "view":
					return <Eye className="h-3 w-3 text-gray-500" />;
				case "delete":
					return <Trash2 className="h-3 w-3 text-gray-500" />;
				default:
					return <MoreHorizontal className="h-3 w-3 text-gray-500" />;
			}
		};

		const getStatusBadgeClass = (status: string) => {
			return status === "Active"
				? "bg-green-100 text-green-800"
				: "bg-gray-100 text-gray-600";
		};

		return (
			<div
				ref={ref}
				className={cn(
					"flex items-center border-t border-gray-200 bg-white transition-colors",
					variant === "default" ? "h-16" : "h-12",
					className
				)}
				{...props}
			>
				<div className="flex w-14 items-center justify-start gap-3 self-stretch px-3">
					<div className="inline-flex flex-1 flex-col items-start justify-start gap-0.5">
						<div
							className="inline-flex h-3 w-3 items-center justify-center gap-2.5 rounded-full px-2 py-1"
							style={{ backgroundColor: category.color }}
						/>
					</div>
				</div>

				<div className="flex min-w-20 flex-1 items-center justify-start gap-3 self-stretch px-3">
					<div className="text-sm font-normal leading-tight text-gray-900">
						{category.name}
					</div>
				</div>

				<div className="flex w-28 min-w-20 items-center justify-start gap-3 self-stretch px-3">
					<div
						className={cn(
							"flex items-center justify-center gap-2.5 rounded-md px-2 py-1",
							getStatusBadgeClass(category.status)
						)}
					>
						<div className="text-[10px] font-medium leading-3">
							{category.status}
						</div>
					</div>
				</div>

				{/* Clients Column */}
				<div className="flex w-44 min-w-20 items-center justify-start gap-3 self-stretch px-3">
					<div className="bg-gray-100 flex items-center justify-center gap-2.5 rounded-md px-2 py-1">
						<div className="text-[10px] font-medium leading-3 text-gray-900">
							{category.clients}
						</div>
					</div>
				</div>

				<div className="flex w-44 min-w-20 items-center justify-start gap-3 self-stretch px-3">
					<div className="bg-gray-100 flex items-center justify-center gap-2.5 rounded-md px-2 py-1">
						<div className="text-[10px] font-medium leading-3 text-gray-900">
							{category.stations}
						</div>
					</div>
				</div>

				<div className="flex w-48 min-w-20 items-center justify-start gap-3 self-stretch px-3">
					<div className="inline-flex flex-col items-start justify-start gap-0.5">
						<div className="text-xs font-normal leading-none text-gray-500">
							{category.dateAdded}
						</div>
					</div>
				</div>

				{finalActions.length > 0 && (
					<div className="flex min-w-16 items-center justify-end gap-1.5 self-stretch px-3">
						{finalActions.map((action, index) => (
							<button
								key={index}
								onClick={() => action.onClick(category)}
								disabled={action.disabled}
								className={cn(
									"flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-white p-2 transition-colors hover:bg-gray-50",
									action.disabled &&
										"cursor-not-allowed opacity-50"
								)}
							>
								{getActionIcon(action.type)}
							</button>
						))}
					</div>
				)}
			</div>
		);
	}
);

CategoryListCard.displayName = "CategoryListCard";

export { CategoryListCard };
