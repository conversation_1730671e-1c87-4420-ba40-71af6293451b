import { LuUser } from "react-icons/lu";
import { IoCalendarOutline } from "react-icons/io5";
import { SlLocationPin } from "react-icons/sl";
import { MdOutlineLocationCity, MdOutlineVideocam } from "react-icons/md";
import { RefactorMultiSelect } from "../components/custom-select";
import { Label } from "@/components/ui/label";
import { type UseFormReturn } from "react-hook-form";
import type { z } from "zod";
import { scheduleOptimizerSchema } from "../schema/schedule-optimizer";
import { GoCheckCircleFill } from "react-icons/go";
import { FiUser } from "react-icons/fi";
import { PiSpeakerHighThin } from "react-icons/pi";

const services = [
    "General Consultation",
    "Physical Exam",
    "Lab Test",
    "Imaging",
    "Immunizations",
    "Medication Management",
    "Mental Health",
    "Nutrition",
    "Pediatrics",
    "Primary Care",
    "Specialty Care",
]

export default function Providers({
    form,
}: {
    form: UseFormReturn<z.infer<typeof scheduleOptimizerSchema>>,
}) {
    return (
        <div className="px-4">
            <div className="py-4 px-5 rounded-xl bg-[#0058930D] border border-[#E4E4E7]">
                <div className="flex items-center gap-x-2.5 font-light text-base mb-7">
                    <LuUser className="text-base" />
                    <p >[Monarch Corps]</p>
                </div>
                <div className="flex items-center gap-x-2.5 font-light text-base mb-3">
                    <IoCalendarOutline className="text-base" />
                    <p>
                        12 May 2025 <span className="font-bold"> 8:30 AM - 8:30 AM</span>
                    </p>
                </div>
                <div className="flex items-center gap-x-2.5 font-light text-base mb-3">
                    <MdOutlineLocationCity className="text-base" />
                    <p >[Location Name]</p>
                </div>
                <div className="flex items-center gap-x-2.5 font-light text-base mb-3">
                    <SlLocationPin className="text-base" />
                    <p>
                        Dr. Adam200 University Ave W, Waterloo, ON N2L 3G1, Canada Sowenson
                    </p>
                </div>

            </div>
            <div className="flex flex-col gap-2 mt-5">
                <Label htmlFor="location" className="text-[#18181B] text-base">Filter Services</Label>

                <RefactorMultiSelect
                    value={form.watch("services")}
                    setValue={(value) => form.setValue("services", value as string[])}
                    placeholder="Select Services"
                    label="Filter Services"

                    options={services}
                />
            </div>

            <div className="flex flex-col gap-2 mt-6">
                <Label htmlFor="appointmentMethod" className="text-[#18181B] text-base">Appointment Method</Label>

                <div className="grid grid-cols-3 gap-2">
                    {["in-person", "audio", "video"].map((type, index) => (
                        <button
                            type="button"
                            key={index}
                            className={`cursor-pointer border flex items-center justify-between py-3 px-4 rounded-xl font-regular opacity-100 gap-x-3 text-center ${form.watch("appointmentMethod") === type ? "border-[#005893] bg-[#0058931A] text-[#005893]" : "border-[#D4D4D8]"}`}
                            onClick={() => form.setValue("appointmentMethod", type as "in-person" | "audio" | "video")}
                        >
                            {type === "in-person" && <FiUser color="#005893" className="text-xl" />}
                            {type === "audio" && <PiSpeakerHighThin color="#005893" className="text-xl" />}
                            {type === "video" && <MdOutlineVideocam color="#005893" className="text-xl" />}
                            <span>{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                            <GoCheckCircleFill color="#005893" />
                        </button>
                    ))}
                </div>

            </div>

        </div>
    )
}