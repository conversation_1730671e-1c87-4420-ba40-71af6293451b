import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import { googleLogin } from "@/lib/api/auth";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

export const useGoogleLogin = () => {
	const handleLoginSuccess = useHandleLoginSuccess();

	return useMutation<any, AxiosError, { token: string }>({
		mutationFn: googleLogin,
		onSuccess: (data) => {
			handleLoginSuccess(data);
		},
	});
};
