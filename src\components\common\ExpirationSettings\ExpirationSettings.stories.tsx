import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { useState } from "react";
import { TimeInput, ExpirationSettings } from "./ExpirationSettings";

const meta: Meta<typeof TimeInput> = {
	title: "Common/TimeInput",
	component: TimeInput,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		value: {
			control: "text",
		},
		unit: {
			control: "select",
			options: ["minutes", "hours", "days", "weeks", "months"],
		},
		useAlternativeOption: {
			control: "boolean",
		},
		disabled: {
			control: "boolean",
		},
		label: {
			control: "text",
		},
		alternativeOptionLabel: {
			control: "text",
		},
		placeholder: {
			control: "text",
		},
		orientation: {
			control: "select",
			options: ["horizontal", "vertical"],
		},
		inputType: {
			control: "select",
			options: ["number", "text"],
		},
		showAlternativeOption: {
			control: "boolean",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

// Story wrapper component to handle state
const TimeInputWrapper = (args: any) => {
	const [value, setValue] = useState(args.value || "5");
	const [unit, setUnit] = useState(args.unit || "days");
	const [useAlternativeOption, setUseAlternativeOption] = useState(
		args.useAlternativeOption || false
	);

	return (
		<div className="w-full max-w-2xl">
			<TimeInput
				{...args}
				value={value}
				onValueChange={setValue}
				unit={unit}
				onUnitChange={setUnit}
				useAlternativeOption={useAlternativeOption}
				onUseAlternativeOptionChange={setUseAlternativeOption}
			/>
		</div>
	);
};

// Generic TimeInput Stories
export const Default: Story = {
	render: TimeInputWrapper,
	args: {
		value: "5",
		unit: "days",
		useAlternativeOption: false,
		disabled: false,
		label: "Duration",
		alternativeOptionLabel: "Use custom option",
	},
};

export const SessionTimeout: Story = {
	render: TimeInputWrapper,
	args: {
		value: "30",
		unit: "minutes",
		useAlternativeOption: false,
		label: "Session timeout",
		alternativeOptionLabel: "Never expire",
		units: [
			{ value: "minutes", label: "Minutes" },
			{ value: "hours", label: "Hours" },
			{ value: "days", label: "Days" },
		],
	},
};

export const CacheExpiry: Story = {
	render: TimeInputWrapper,
	args: {
		value: "1",
		unit: "hours",
		useAlternativeOption: false,
		label: "Cache expires in",
		alternativeOptionLabel: "Use manual refresh",
		units: [
			{ value: "minutes", label: "Minutes" },
			{ value: "hours", label: "Hours" },
		],
	},
};

export const VerticalLayout: Story = {
	render: TimeInputWrapper,
	args: {
		value: "7",
		unit: "days",
		useAlternativeOption: false,
		label: "Reminder frequency",
		alternativeOptionLabel: "Custom schedule",
		orientation: "vertical",
	},
};

export const WithoutAlternativeOption: Story = {
	render: TimeInputWrapper,
	args: {
		value: "15",
		unit: "minutes",
		useAlternativeOption: false,
		label: "Automatic save interval",
		showAlternativeOption: false,
	},
};

export const Disabled: Story = {
	render: TimeInputWrapper,
	args: {
		value: "60",
		unit: "seconds",
		useAlternativeOption: false,
		label: "Cooldown period",
		alternativeOptionLabel: "Skip cooldown",
		disabled: true,
		units: [
			{ value: "seconds", label: "Seconds" },
			{ value: "minutes", label: "Minutes" },
		],
	},
};

// Backward compatibility story for ExpirationSettings
export const BookingLinkExpiration: Story = {
	render: (args: any) => {
		const [value, setValue] = useState("7");
		const [unit, setUnit] = useState("days");
		const [useSpecificDate, setUseSpecificDate] = useState(false);

		return (
			<div className="w-full max-w-2xl">
				<ExpirationSettings
					value={value}
					onValueChange={setValue}
					unit={unit}
					onUnitChange={setUnit}
					useSpecificDate={useSpecificDate}
					onUseSpecificDateChange={setUseSpecificDate}
				/>
			</div>
		);
	},
};
