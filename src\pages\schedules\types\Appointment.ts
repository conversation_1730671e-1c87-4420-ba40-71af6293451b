export type ListAppointmentsQueryParams = {
    client_id?: string;
    date_from?: string;
    date_to?: string;
    exclude?: string[];
    include?: string[];
    location_id?: string;
    ordering?: string;
    page?: number;
    per_page?: number;
    priority?: string;
    search?: string;
    service_id?: string;
    status?: string;
}

export type ListAppointmentsResponse = {
    count: number;
    next: string;
    previous: string;
    results: [
        {
            success: boolean;
            message: string;
            data:
            {
                id: string;
                booking_reference: string;
                status: string;
                priority: string;
                client_display_name: string;
                service_name: string;
                start_time: string;
                end_time: string;
                duration_minutes: number;
                created_at: string;
            }[],
            meta: {
                pagination: {
                    total: number;
                    page: number;
                    per_page: number;
                    total_pages: number;
                    has_next: boolean;
                    has_previous: boolean;
                },
                filters: {
                    status: string[];
                    date_range: {
                        from: string;
                        to: string;
                    }
                }
            }
        }
    ]
}

export type ActionAppointmentParams = {
    client_id: number,
    duration_minutes: number;
    emr_appointment_id: string;
    end_time: string;
    location_id: number;
    organization_id: string;
    start_time: string;
    station_id: number;
    service_id: number;
    status: string;
    priority: string;
    source: string;
    emr_metadata: {
        description: string;
    },
    notes: string;
    internal_notes: string;
    special_requirements: string;
}

export type CreateAppointmentResponse = {
    success: boolean;
    message: string;
    data: {
        id: string;
        booking_reference: string;
        status: string;
        priority: string;
        client_display_name: string;
        service_name: string;
        start_time: string;
        end_time: string;
        duration_minutes: number;
        created_at: string;
    };
};

export type GetAppointmentDetailsResponse = {
    success: boolean;
    message: string;
    data: {
        id: string;
        booking_reference: string;
        status: string;
        priority: string;
        client: {
            id: number;
            name: string;
            email: string;
            phone: string;
        },
        service: {
            id: number;
            name: string;
            duration_minutes: number;
            price: number;
            currency: string;
        },
        location: {
            id: number;
            name: string;
            address: string;
        },
        station: {
            id: number;
            name: string;
            type: string;
        },
        schedule: {
            start_time: string;
            end_time: string;
            duration_minutes: number;
            timezone: string;
        },
        status_info: {
            current_status: string;
            label: string;
            color: string;
        },
        actions: {
            can_cancel: boolean;
            can_reschedule: boolean;
            can_modify: boolean;
        },
        created_at: string;
        updated_at: string;
    }
}

export type UpdateAppointmentResponse = {
    success: boolean;
    message: string;
    data: {
        id: string;
        booking_reference: string;
        status: string;
        start_time: string;
        end_time: string;
        updated_at: string;
    }
}

export type DeleteAppointmentResponse = {
    success: boolean;
    message: string;
    data: {
        id: string;
        deleted_at: string;
    }
}

export type CancelAppointmentResponse = {
    success: boolean;
    message: string;
    data: {
        id: string;
        booking_reference: string;
        status: string;
        action_timestamp: string;
        reason: string;
        actions: {
            can_reschedule: boolean;
            can_cancel: boolean;
            can_modify: boolean;
        }
    }
}

export type CheckInAppointmentParams = {
    client_id: number;
    duration_minutes: number;
    emr_appointment_id: string;
    end_time: string;
    location_id: number;
    organization_id: string;
    start_time: string;
    station_id: number;
    service_id: number;
    status: string;
    priority: string;
    source: string;
    emr_metadata: {
        description: string;
    },
    notes: string;
    internal_notes: string;
    special_requirements: string;
}

export type CheckInAppointmentResponse = {
    success: boolean;
    message: string;
    data: {
        id: string;
        booking_reference: string;
        status: string;
        action_timestamp: string;
        estimated_wait_time: number;
    }
}

export type CompleteAppointmentResponse = {
    success: boolean;
    message: string;
    data: {
        id: string;
        booking_reference: string;
        status: string;
        action_timestamp: string;
        notes: string;
    }
}

export type RescheduleAppointmentParams = {
    new_start_time: string;
    new_location_id: number;
    new_station_id: number;
    reason: string;
}

export type RescheduleAppointmentResponse = {
    success: boolean;
    message: string;
    data: {
        id: string;
        booking_reference: string;
        status: string;
        action_timestamp: string;
        old_schedule: {
            start_time: string;
            end_time: string;
        },
        new_schedule: {
            start_time: string;
            end_time: string;
        },
        reason: string;
    }
}   