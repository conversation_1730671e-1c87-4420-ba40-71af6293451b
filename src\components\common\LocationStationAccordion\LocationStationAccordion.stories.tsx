import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { useState } from "react";
import { LocationStationAccordion } from "./LocationStationAccordion";
import type { Location } from "./LocationStationAccordion";

const meta: Meta<typeof LocationStationAccordion> = {
	title: "Common/LocationStationAccordion",
	component: LocationStationAccordion,
	parameters: {
		layout: "padded",
	},
	tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockLocations: Location[] = [
	{
		id: "loc-1",
		name: "Downtown Medical Center",
		stations: [
			{ id: "station-1", name: "Emergency Room" },
			{ id: "station-2", name: "Cardiology Wing" },
			{ id: "station-3", name: "Radiology Department" },
			{ id: "station-4", name: "Laboratory" },
		],
	},
	{
		id: "loc-2",
		name: "North Campus Clinic",
		stations: [
			{ id: "station-5", name: "General Practice" },
			{ id: "station-6", name: "Pediatrics" },
		],
	},
	{
		id: "loc-3",
		name: "Westside Health Hub",
		stations: [
			{ id: "station-7", name: "Urgent Care" },
			{ id: "station-8", name: "Pharmacy" },
			{ id: "station-9", name: "Physical Therapy" },
		],
	},
	{
		id: "loc-4",
		name: "East Medical Plaza",
		stations: [
			{ id: "station-10", name: "Orthopedics" },
			{ id: "station-11", name: "Dermatology" },
		],
	},
];

// Interactive story component
function LocationStationAccordionDemo() {
	const [selectedLocationIds, setSelectedLocationIds] = useState<Set<string>>(
		new Set(["loc-1"])
	);
	const [selectedStationIds, setSelectedStationIds] = useState<Set<string>>(
		new Set(["station-1", "station-2"])
	);

	const handleLocationSelectionChange = (
		locationId: string,
		checked: boolean
	) => {
		setSelectedLocationIds((prev) => {
			const newSet = new Set(prev);
			if (checked) {
				newSet.add(locationId);
			} else {
				newSet.delete(locationId);
			}
			return newSet;
		});
	};

	const handleStationSelectionChange = (
		stationId: string,
		checked: boolean
	) => {
		setSelectedStationIds((prev) => {
			const newSet = new Set(prev);
			if (checked) {
				newSet.add(stationId);
			} else {
				newSet.delete(stationId);
			}
			return newSet;
		});
	};

	return (
		<div className="max-w-md">
			<LocationStationAccordion
				locations={mockLocations}
				selectedLocationIds={selectedLocationIds}
				selectedStationIds={selectedStationIds}
				onLocationSelectionChange={handleLocationSelectionChange}
				onStationSelectionChange={handleStationSelectionChange}
				onBack={() => console.log("Back clicked")}
			/>
		</div>
	);
}

export const Default: Story = {
	render: () => <LocationStationAccordionDemo />,
};

export const WithoutBackButton: Story = {
	render: () => {
		const [selectedLocationIds, setSelectedLocationIds] = useState<
			Set<string>
		>(new Set());
		const [selectedStationIds, setSelectedStationIds] = useState<
			Set<string>
		>(new Set());

		return (
			<div className="max-w-md">
				<LocationStationAccordion
					locations={mockLocations}
					selectedLocationIds={selectedLocationIds}
					selectedStationIds={selectedStationIds}
					onLocationSelectionChange={(
						locationId: string,
						checked: boolean
					) => {
						setSelectedLocationIds((prev) => {
							const newSet = new Set(prev);
							if (checked) {
								newSet.add(locationId);
							} else {
								newSet.delete(locationId);
							}
							return newSet;
						});
					}}
					onStationSelectionChange={(
						stationId: string,
						checked: boolean
					) => {
						setSelectedStationIds((prev) => {
							const newSet = new Set(prev);
							if (checked) {
								newSet.add(stationId);
							} else {
								newSet.delete(stationId);
							}
							return newSet;
						});
					}}
					showBackButton={false}
				/>
			</div>
		);
	},
};

export const CustomText: Story = {
	render: () => {
		const [selectedLocationIds, setSelectedLocationIds] = useState<
			Set<string>
		>(new Set());
		const [selectedStationIds, setSelectedStationIds] = useState<
			Set<string>
		>(new Set());

		return (
			<div className="max-w-md">
				<LocationStationAccordion
					locations={mockLocations}
					selectedLocationIds={selectedLocationIds}
					selectedStationIds={selectedStationIds}
					onLocationSelectionChange={(
						locationId: string,
						checked: boolean
					) => {
						setSelectedLocationIds((prev) => {
							const newSet = new Set(prev);
							if (checked) {
								newSet.add(locationId);
							} else {
								newSet.delete(locationId);
							}
							return newSet;
						});
					}}
					onStationSelectionChange={(
						stationId: string,
						checked: boolean
					) => {
						setSelectedStationIds((prev) => {
							const newSet = new Set(prev);
							if (checked) {
								newSet.add(stationId);
							} else {
								newSet.delete(stationId);
							}
							return newSet;
						});
					}}
					allLocationsTitle="Custom All Facilities"
					allLocationsSubtitle="Apply to all medical facilities and departments."
					selectedLocationsTitle="Custom Selected Facilities Only"
					selectedLocationsSubtitle="Choose specific facilities and departments."
					searchPlaceholder="Search or select all facilities"
				/>
			</div>
		);
	},
};

export const PreselectedLocations: Story = {
	render: () => {
		const [selectedLocationIds, setSelectedLocationIds] = useState<
			Set<string>
		>(new Set(["loc-1", "loc-3"]));
		const [selectedStationIds, setSelectedStationIds] = useState<
			Set<string>
		>(new Set(["station-1", "station-2", "station-7"]));

		return (
			<div className="max-w-md">
				<LocationStationAccordion
					locations={mockLocations}
					selectedLocationIds={selectedLocationIds}
					selectedStationIds={selectedStationIds}
					onLocationSelectionChange={(
						locationId: string,
						checked: boolean
					) => {
						setSelectedLocationIds((prev) => {
							const newSet = new Set(prev);
							if (checked) {
								newSet.add(locationId);
							} else {
								newSet.delete(locationId);
							}
							return newSet;
						});
					}}
					onStationSelectionChange={(
						stationId: string,
						checked: boolean
					) => {
						setSelectedStationIds((prev) => {
							const newSet = new Set(prev);
							if (checked) {
								newSet.add(stationId);
							} else {
								newSet.delete(stationId);
							}
							return newSet;
						});
					}}
				/>
			</div>
		);
	},
};
