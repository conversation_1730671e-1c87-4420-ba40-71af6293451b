import { useState } from "react";

interface StepNavigationConfig {
	totalSteps: number;
	initialStep?: number;
	onStepChange?: (step: number) => void;
}

interface StepNavigationReturn {
	currentStep: number;
	isFirstStep: boolean;
	isLastStep: boolean;
	handleNext: () => void;
	handlePrevious: () => void;
	goToStep: (step: number) => void;
	canProceed: (validationFn?: () => boolean) => boolean;
}

export function useStepNavigation({
	totalSteps,
	initialStep = 1,
	onStepChange,
}: StepNavigationConfig): StepNavigationReturn {
	const [currentStep, setCurrentStep] = useState(initialStep);

	const isFirstStep = currentStep === 1;
	const isLastStep = currentStep === totalSteps;

	const handleNext = () => {
		if (currentStep < totalSteps) {
			const nextStep = currentStep + 1;
			setCurrentStep(nextStep);
			console.log("nextstep", "but didnt ", currentStep);
			onStepChange?.(nextStep);
		}
	};

	const handlePrevious = () => {
		if (currentStep > 1) {
			const prevStep = currentStep - 1;
			setCurrentStep(prevStep);
			onStepChange?.(prevStep);
		}
	};

	const goToStep = (step: number) => {
		if (step >= 1 && step <= totalSteps) {
			setCurrentStep(step);
			onStepChange?.(step);
		}
	};

	const canProceed = (validationFn?: () => boolean) => {
		if (validationFn) {
			return validationFn();
		}
		return true;
	};

	return {
		currentStep,
		isFirstStep,
		isLastStep,
		handleNext,
		handlePrevious,
		goToStep,
		canProceed,
	};
}
