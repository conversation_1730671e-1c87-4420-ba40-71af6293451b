import { InputText } from "@/components/common/InputText";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

export default function UniversityForm({
    back, next
}: { back: () => void; next: () => void }) {
    return (
        <div>
            <div className="mx-5 mt-5 border border-[#E5E5E7]">
                <div className="bg-[#043B6D] w-full h-[5px] rounded-t-2xl" />
                <div className="border-b border-[#E5E5E7] py-4 px-5 text-3xl font-light leading-[1.5] rounded-b-lg">
                    <h1 className="text-[#0F172A]">University of Waterloo Health Center’s Registration Form</h1>
                </div>
                <div className="pt-9 pb-5 px-4 border-b border-[#E5E5E7]">
                    <h3 className="text-xl font-light mb-5 text-[#0F172A]">Registration Form</h3>
                    <p className="font-light leading-[1.6] text-[#0F172A]">
                        Lorem ipsum is a dummy or placeholder text commonly used in graphic design, publishing, and web development to fill empty spaces in a layout that does not yet have content.
                    </p>
                </div>
                <div className="pt-10 px-4 pb-5 space-y-10">
                    <div className="flex flex-col gap-y-3">
                        <Label htmlFor="question_title" className="text-xl text-[#0F172A] font-extralight">Question Title</Label>
                        <InputText
                            name="question_title"
                            id="question_title"
                            placeholder="Enter your question title"
                        />
                    </div>
                    <div className="flex flex-col gap-y-3">
                        <Label htmlFor="question_answer" className="text-xl text-[#0F172A] font-extralight">Question Answer</Label>
                        <InputText
                            name="question_answer"
                            id="question_answer"
                            placeholder="Enter your question answer"
                        />
                    </div>
                </div>
            </div>
            <div className="flex items-center justify-between mx-5 mt-8">
                <div>
                    <Button variant="outline" className="bg-[#F4F4F5] cursor-pointer py-3" onClick={back}>
                        Back
                    </Button>
                </div>
                <div className="space-x-3">
                    <Button variant="outline" className="bg-[#F4F4F5] cursor-pointer py-3">
                        Skip
                    </Button>
                    <Button className="cursor-pointer py-3" onClick={next}>
                        Create Appointment
                    </Button>
                </div>
            </div>
        </div>
    )
}