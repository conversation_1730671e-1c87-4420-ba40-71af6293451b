import { QueryClient } from "@tanstack/react-query";

export const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			staleTime: 5 * 60 * 1000, // 5 minutes
			cacheTime: 10 * 60 * 1000, // 10 minutes
			retry: (failureCount, error: any) => {
				// Don't retry on 4xx errors except 408, 429
				if (
					error?.status >= 400 &&
					error?.status < 500 &&
					![408, 429].includes(error.status)
				) {
					return false;
				}
				return failureCount < 3;
			},
			refetchOnWindowFocus: false,
			refetchOnMount: true,
			refetchOnReconnect: "always",
		},
		mutations: {
			retry: (failureCount, error: any) => {
				// Don't retry mutations on 4xx errors
				if (error?.status >= 400 && error?.status < 500) {
					return false;
				}
				return failureCount < 2;
			},
		},
	},
});
