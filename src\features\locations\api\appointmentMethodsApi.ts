import { apiClient } from "@/lib/api/clients";
import type { AppointmentMethod, AppointmentMethodsResponse } from "../types";

const APPOINTMENT_METHODS_ENDPOINTS = {
	base: "/api/v1/appointment-methods",
} as const;

export const appointmentMethodsApi = {
	// Get all appointment methods
	getAppointmentMethods: async (
		orgId: number
	): Promise<AppointmentMethod[]> => {
		const response = await apiClient.get(
			APPOINTMENT_METHODS_ENDPOINTS.base,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);

		// Extract the data array from the response structure
		const responseData: AppointmentMethodsResponse = response.data;
		return responseData.data;
	},
};
