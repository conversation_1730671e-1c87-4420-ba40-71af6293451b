import { AxiosError } from 'axios';

/**
 * Extract error message from Axios error response
 * Handles different error response structures and provides fallback messages
 */
export const extractErrorMessage = (
  error: AxiosError,
  defaultMessage: string = "An error occurred. Please try again."
): string => {
  let errorMessage = defaultMessage;

  if (error.response?.data) {
    const responseData = error.response.data;
    if (typeof responseData === 'object' && responseData !== null) {
      // Try different possible error message locations

      if ('errors' in responseData && Array.isArray(responseData.errors) && responseData.errors.length > 0) {
        errorMessage = responseData.errors[0];
      } else if ('error' in responseData && typeof responseData.error === 'string') {
        errorMessage = responseData.error;
      } else if ('message' in responseData && typeof responseData.message === 'string') {
        errorMessage = responseData.message;
      } else if ('detail' in responseData && typeof responseData.detail === 'string') {
        errorMessage = responseData.detail;
      } else if ('msg' in responseData && typeof responseData.msg === 'string') {
        errorMessage = responseData.msg;
      }
    } else if (typeof responseData === 'string') {
      errorMessage = responseData;
    }
  }

  // Only apply HTTP status code messages if we still have the default message
  // (meaning no specific error message was found in the response)
  if (errorMessage === defaultMessage) {
    if (error.response?.status === 500) {
      errorMessage = "Server error occurred. Please try again later.";
    } else if (error.response?.status === 401) {
      errorMessage = "Authentication failed. Please log in again.";
    } else if (error.response?.status === 403) {
      errorMessage = "You don't have permission to perform this action.";
    } else if (error.response?.status === 404) {
      errorMessage = "The requested resource was not found.";
    } else if (error.response?.status === 422) {
      errorMessage = "Invalid data provided. Please check your input.";
    } else if (error.response?.status === 429) {
      errorMessage = "Too many requests. Please try again later.";
    }
  }

  return errorMessage;
};

/**
 * Extract validation errors from Axios error response
 * Returns an object with field names as keys and error messages as values
 */
export const extractValidationErrors = (error: AxiosError): Record<string, string> => {
  const validationErrors: Record<string, string> = {};

  if (error.response?.data && typeof error.response.data === 'object' && error.response.data !== null) {
    const responseData = error.response.data as any;

    if ('errors' in responseData && typeof responseData.errors === 'object') {
      Object.entries(responseData.errors).forEach(([field, messages]) => {
        if (Array.isArray(messages) && messages.length > 0) {
          validationErrors[field] = messages[0];
        } else if (typeof messages === 'string') {
          validationErrors[field] = messages;
        }
      });
    }
  }

  return validationErrors;
};

/**
 * Log error details for debugging
 */
export const logError = (error: AxiosError, context: string = "API Error"): void => {
  console.log(`${context}:`, {
    status: error.response?.status,
    statusText: error.response?.statusText,
    url: error.config?.url,
    method: error.config?.method,
    data: error.response?.data,
    message: extractErrorMessage(error),
  });
};


export const handle2FAError = (error: AxiosError): string => {
  if (error.response?.status === 401) {
    return "Authentication timeout. Please log in again.";
  }

  return extractErrorMessage(error, "Invalid 2FA code. Please try again.");
};

export const handleAuthError = (error: AxiosError): string => {
  if (error.response?.status === 401) {
    return "Invalid credentials. Please check your email and password.";
  }

  return extractErrorMessage(error, "Authentication failed. Please try again.");
};

/**
 * Handle password reset errors
 */
export const handlePasswordResetError = (error: AxiosError): string => {
  return extractErrorMessage(error, "Password reset failed. Please try again.");
};

/**
 * Handle general API errors with custom default message
 */
export const handleAPIError = (error: AxiosError, defaultMessage: string): string => {
  return extractErrorMessage(error, defaultMessage);
}; 