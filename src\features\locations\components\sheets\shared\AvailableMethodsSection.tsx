import type { LucideIcon } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

interface AvailableMethod {
	id: string;
	label: string;
	icon: LucideIcon;
}

interface AvailableMethodsSectionProps {
	availableMethods: AvailableMethod[];
	selectedMethods: string[];
	onMethodToggle: (methodId: string) => void;
	title?: string;
}

export function AvailableMethodsSection({
	availableMethods,
	selectedMethods,
	onMethodToggle,
	title = "Station Available in Methods",
}: AvailableMethodsSectionProps) {
	return (
		<div className="space-y-5 border-b border-[#bfbfbf] pb-3">
			<div className="space-y-2">
				<h3 className="text-sm font-medium text-zinc-900">{title}</h3>
				<div className="flex gap-3">
					{availableMethods.map((method) => {
						const Icon = method.icon;
						const isSelected = selectedMethods.includes(method.id);
						return (
							<button
								key={method.id}
								type="button"
								onClick={() => onMethodToggle(method.id)}
								className={`flex items-center gap-2.5 rounded-md border-[0.5px] p-2 ${
									isSelected
										? "border-zinc-100 bg-white"
										: "border-zinc-200 bg-white"
								}`}
							>
								<Icon className="h-3 w-3" />
								<span className="text-xs font-medium text-zinc-800">
									{method.label}
								</span>
								<Checkbox
									checked={isSelected}
									onCheckedChange={() =>
										onMethodToggle(method.id)
									}
									className="h-3 w-3"
								/>
							</button>
						);
					})}
				</div>
			</div>
		</div>
	);
}
