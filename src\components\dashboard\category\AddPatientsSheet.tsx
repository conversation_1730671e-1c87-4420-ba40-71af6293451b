import React, { useState, useCallback, useEffect, useMemo } from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { X, Search, Loader2 } from "lucide-react";
import { useClients, transformClientToPatient } from "@/hooks/useClients";
import { useDebounce } from "@/hooks/useDebounce";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";

interface AddPatientsSheetProps {
	open: boolean;
	onClose: () => void;
	categoryName: string;
	categoryId: string;
	onAddPatients: (patientIds: string[]) => void;
	isAddingPatients?: boolean;
}

export const AddPatientsSheet: React.FC<AddPatientsSheetProps> = ({
	open,
	onClose,
	categoryName,
	categoryId,
	onAddPatients,
	isAddingPatients = false,
}) => {
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedPatients, setSelectedPatients] = useState<Set<string>>(
		new Set()
	);
	const [currentPage, setCurrentPage] = useState(1);

	const patientsPerPage = 8;
	const debouncedSearchTerm = useDebounce(searchQuery, 300);

	const {
		data: clientsResponse,
		isLoading,
		error,
	} = useClients({
		page: currentPage,
		per_page: patientsPerPage,
		search: debouncedSearchTerm || undefined,
	});

	const patients = useMemo(() => {
		if (!clientsResponse?.data) return [];
		return clientsResponse.data.map(transformClientToPatient);
	}, [clientsResponse]);

	const totalPages = clientsResponse?.meta?.pagination?.total_pages || 1;
	const currentPatients = patients; 

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		if (currentPage < totalPages) {
			handlePageChange(currentPage + 1);
		}
	}, [currentPage, totalPages, handlePageChange]);

	useEffect(() => {
		setCurrentPage(1);
	}, [searchQuery]);

	useEffect(() => {
		if (open) {
			setSearchQuery("");
			setSelectedPatients(new Set());
			setCurrentPage(1);
		}
	}, [open]);

	const handlePatientSelect = (patientId: string, checked: boolean) => {
		const newSelected = new Set(selectedPatients);
		if (checked) {
			newSelected.add(patientId);
		} else {
			newSelected.delete(patientId);
		}
		setSelectedPatients(newSelected);
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked) {
			const allPatientIds = new Set(currentPatients.map((p) => p.id));
			setSelectedPatients(allPatientIds);
		} else {
			setSelectedPatients(new Set());
		}
	};

	const handleAddSelected = () => {
		const selectedIds = Array.from(selectedPatients);
		onAddPatients(selectedIds);
		onClose();
	};

	const isAllSelected =
		currentPatients.length > 0 &&
		currentPatients.every((patient) => selectedPatients.has(patient.id));
	const isIndeterminate = selectedPatients.size > 0 && !isAllSelected;

	return (
		<Sheet open={open} onOpenChange={onClose}>
			<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
				<div className="flex h-full flex-col gap-6">
					<div className="flex items-start justify-between gap-2.5">
						<div className="flex flex-1 flex-col items-start justify-start gap-2.5">
							<div className="text-base leading-7 font-semibold text-gray-900">
								Add Patients to {categoryName}
							</div>
							<div className="text-xs leading-none font-normal text-gray-500">
								Select which patients you'd like to add to this
								category
							</div>
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={onClose}
							className="h-9 w-9 rounded-md"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>

					<div className="relative">
						<Search className="absolute top-1/2 left-3 h-3 w-3 -translate-y-1/2 text-gray-400" />
						<Input
							placeholder="Select All Patients"
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							className="h-9 pl-9 text-xs"
						/>
					</div>

					<div className="flex flex-1 flex-col gap-2 overflow-hidden">
						{isLoading ? (
							<div className="flex h-full items-center justify-center">
								<div className="flex flex-col items-center gap-3">
									<Loader2 className="h-8 w-8 animate-spin text-gray-500" />
									<p className="text-sm text-gray-500">
										Loading patients...
									</p>
								</div>
							</div>
						) : error ? (
							<div className="flex h-full items-center justify-center">
								<div className="flex flex-col items-center gap-3">
									<p className="text-sm text-red-500">
										Error loading patients. Please try
										again.
									</p>
								</div>
							</div>
						) : currentPatients.length === 0 ? (
							<div className="flex h-full items-center justify-center">
								<div className="flex flex-col items-center gap-3">
									<p className="text-sm text-gray-500">
										{searchQuery.trim()
											? `No patients found matching "${searchQuery}"`
											: "No patients available"}
									</p>
								</div>
							</div>
						) : (
							<>
								<div className="flex-1 overflow-y-auto">
									<div className="flex flex-col overflow-hidden rounded-xl border border-gray-200">
										<div className="flex items-center border-b border-gray-200">
											<div className="flex h-12 items-center justify-start gap-2.5 px-3">
												<Checkbox
													checked={isAllSelected}
													onCheckedChange={
														handleSelectAll
													}
													className="h-3 w-3"
													{...(isIndeterminate && {
														"data-state":
															"indeterminate",
													})}
												/>
											</div>
											<div className="flex h-12 w-52 min-w-20 items-center justify-start gap-2.5 px-3">
												<div className="text-xs leading-none font-normal text-gray-500">
													Patient Name
												</div>
											</div>
											<div className="flex h-12 w-28 min-w-20 items-center justify-start gap-2.5 px-3">
												<div className="text-xs leading-none font-normal text-gray-500">
													Status
												</div>
											</div>
											<div className="flex h-12 w-28 min-w-20 items-center justify-start gap-2.5 px-3">
												<div className="text-xs leading-none font-normal text-gray-500">
													UID
												</div>
											</div>
										</div>

										{currentPatients.map(
											(patient, index) => (
												<div
													key={patient.id}
													className={`flex items-center bg-white ${
														index <
														currentPatients.length -
															1
															? "border-b border-gray-200"
															: ""
													}`}
												>
													<div className="flex min-w-20 flex-1 items-center justify-start gap-3 p-3">
														<Checkbox
															checked={selectedPatients.has(
																patient.id
															)}
															onCheckedChange={(
																checked
															) =>
																handlePatientSelect(
																	patient.id,
																	checked as boolean
																)
															}
															className="h-3 w-3"
														/>
														<div className="relative h-9 w-9 overflow-hidden rounded-full bg-gray-200">
															{patient.profile_picture_url ? (
																<>
																	<img
																		src={
																			patient.profile_picture_url
																		}
																		alt={
																			patient.name
																		}
																		className="h-full w-full object-cover"
																		onError={(
																			e
																		) => {
																			const img =
																				e.currentTarget;
																			const initialsDiv =
																				img.nextElementSibling as HTMLElement;
																			img.style.display =
																				"none";
																			if (
																				initialsDiv
																			) {
																				initialsDiv.style.display =
																					"block";
																			}
																		}}
																	/>
																	<div
																		className="absolute top-[8px] left-[8px] h-5 w-5 justify-center text-center text-xs leading-none font-medium text-gray-600"
																		style={{
																			display:
																				"none",
																		}}
																	>
																		{patient.name
																			.split(
																				" "
																			)
																			.map(
																				(
																					n
																				) =>
																					n.charAt(
																						0
																					)
																			)
																			.join(
																				""
																			)
																			.toUpperCase()
																			.slice(
																				0,
																				2
																			)}
																	</div>
																</>
															) : (
																<div className="absolute top-[8px] left-[8px] h-5 w-5 justify-center text-center text-xs leading-none font-medium text-gray-600">
																	{patient.name
																		.split(
																			" "
																		)
																		.map(
																			(
																				n
																			) =>
																				n.charAt(
																					0
																				)
																		)
																		.join(
																			""
																		)
																		.toUpperCase()
																		.slice(
																			0,
																			2
																		)}
																</div>
															)}
														</div>
														<div className="flex flex-1 flex-col items-start justify-start gap-0.5">
															<div className="text-sm leading-tight font-medium text-gray-900">
																{patient.name}
															</div>
															<div className="text-[10px] leading-3 font-normal text-gray-500">
																{patient.email}
															</div>
														</div>
													</div>
													<div className="flex min-w-20 flex-1 items-center justify-end gap-3 self-stretch p-3">
														<div
															className={`flex items-center justify-center gap-2.5 rounded-md px-2 py-1 ${
																patient.status ===
																"Active"
																	? "bg-green-100"
																	: "bg-gray-200"
															}`}
														>
															<div className="text-[10px] leading-3 font-medium text-gray-900">
																{patient.status}
															</div>
														</div>
														<div className="flex flex-1 flex-col items-end justify-start gap-0.5">
															<div className="text-sm leading-tight font-medium text-gray-900">
																#{patient.id}
															</div>
														</div>
													</div>
												</div>
											)
										)}
									</div>
								</div>

								{totalPages > 1 && (
									<div className="flex justify-end pt-4">
										<div>
											<Pagination>
												<PaginationContent>
													<PaginationItem>
														<PaginationPrevious
															onClick={
																handlePreviousPage
															}
															className={
																currentPage ===
																1
																	? "pointer-events-none opacity-50"
																	: "cursor-pointer"
															}
														/>
													</PaginationItem>
													{Array.from(
														{ length: totalPages },
														(_, i) => i + 1
													).map((page) => (
														<PaginationItem
															key={page}
														>
															<PaginationLink
																onClick={() =>
																	handlePageChange(
																		page
																	)
																}
																isActive={
																	currentPage ===
																	page
																}
																className="cursor-pointer"
															>
																{page}
															</PaginationLink>
														</PaginationItem>
													))}
													{totalPages > 5 &&
														currentPage <
															totalPages - 2 && (
															<PaginationItem>
																<PaginationEllipsis />
															</PaginationItem>
														)}
													<PaginationItem>
														<PaginationNext
															onClick={
																handleNextPage
															}
															className={
																currentPage ===
																totalPages
																	? "pointer-events-none opacity-50"
																	: "cursor-pointer"
															}
														/>
													</PaginationItem>
												</PaginationContent>
											</Pagination>
										</div>
									</div>
								)}
							</>
						)}
					</div>

					{/* Footer */}
					<div className="flex items-center justify-end gap-3">
						<Button
							variant="outline"
							onClick={onClose}
							className="h-9"
						>
							Close
						</Button>
						<Button
							onClick={handleAddSelected}
							disabled={
								selectedPatients.size === 0 || isAddingPatients
							}
							className="h-9 bg-[#005893] hover:bg-[#004a7a]"
						>
							{isAddingPatients ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									Adding...
								</>
							) : (
								`Add Selected (${selectedPatients.size})`
							)}
						</Button>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
};
