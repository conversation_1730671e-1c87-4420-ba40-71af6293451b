@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap");

/* Apply Inter font */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");

@import "tailwindcss";

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@theme {
	--color-background: hsl(var(--background));
	--color-foreground: hsl(var(--foreground));
	--color-foreground-muted: hsl(var(--foreground-muted));

	--color-card: hsl(var(--card));
	--color-card-foreground: hsl(var(--card-foreground));

	--color-popover: hsl(var(--popover));
	--color-popover-foreground: hsl(var(--popover-foreground));

	--color-primary: rgb(var(--primary));
	--color-primary-foreground: hsl(var(--primary-foreground));

	--color-secondary: hsl(var(--secondary));
	--color-secondary-foreground: hsl(var(--secondary-foreground));

	--color-muted: hsl(var(--muted));
	--color-muted-foreground: hsl(var(--muted-foreground));

	--color-accent: hsl(var(--accent));
	--color-accent-foreground: hsl(var(--accent-foreground));

	--color-destructive: hsl(var(--destructive));
	--color-destructive-foreground: hsl(var(--destructive-foreground));

	--color-border: hsl(var(--border));
	--color-border-muted: hsl(var(--border-muted));
	--color-input: hsl(var(--input));
	--color-ring: hsl(var(--ring));

	--color-chart-1: hsl(var(--chart-1));
	--color-chart-2: hsl(var(--chart-2));
	--color-chart-3: hsl(var(--chart-3));
	--color-chart-4: hsl(var(--chart-4));
	--color-chart-5: hsl(var(--chart-5));
	--color-chart-6: hsl(var(--chart-6));
	--color-chart-7: hsl(var(--chart-7));
	--color-chart-8: hsl(var(--chart-8));
	--color-chart-9: hsl(var(--chart-9));
	--color-chart-10: hsl(var(--chart-10));
	--color-sidebar: hsl(var(--sidebar-background));
	--color-sidebar-foreground: hsl(var(--sidebar-foreground));
	--color-sidebar-primary: hsl(var(--sidebar-primary));
	--color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
	--color-sidebar-accent: hsl(var(--sidebar-accent));
	--color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
	--color-sidebar-border: hsl(var(--sidebar-border));
	--color-sidebar-ring: hsl(var(--sidebar-ring));

	--color-shadow-location-card: hsl(var(--shadow-location-card));
	--color-subtle-subtle: hsl(var(--subtle));
	--color-foreground-subtle: hsl(var(--foreground-subtle));
	--color-subtle2: hsl(var(--subtle2));
	--color-foreground-success: hsl(var(--foreground-success));
	--color-foreground-warning: hsl(var(--foreground-warning));
	--color-foreground-disable: hsl(var(--foreground-disable));

	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}

		to {
			height: var(--radix-accordion-content-height);
		}
	}

	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}

		to {
			height: 0;
		}
	}
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@layer utilities {
	body {
		font-family: "Inter", Arial, Helvetica, sans-serif;
	}
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 240 6% 10%;
		--card: 0 0% 100%;
		--card-foreground: 0 0% 3.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 0 0% 3.9%;
		--primary: 0 89 148;
		--primary-foreground: 0 0% 98%;
		--secondary: 0 0% 96.1%;
		--secondary-foreground: 0 0% 9%;
		--muted: 240 4% 46%;
		--muted-foreground: 240 4% 46%;
		--foreground-muted: 240 5% 96%;
		--accent: 0 0% 96.1%;
		--accent-foreground: 0 0% 9%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 89.8%;
		--border-muted: 240 6% 90%;
		--input: 0 0% 89.8%;
		--ring: 0 0% 3.9%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--chart-6: 243 51% 56%;
		--chart-7: 243 50% 93%;
		--chart-8: 5 47% 55%;
		--chart-9: 5 46% 89%;
		--chart-10: 0 89 148;
		--radius: 0.5rem;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 240 5.3% 26.1%;
		--sidebar-primary: 240 5.9% 10%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 240 4.8% 95.9%;
		--sidebar-accent-foreground: 240 5.9% 10%;
		--sidebar-border: 220 13% 91%;
		--sidebar-ring: 217.2 91.2% 59.8%;
		--shadow-location-card: 0px 4px 20px 0px hsla(204, 15%, 66%, 0.18);
		/* --color-subtle: 0 0% 98%;background: var(--Foreground-Subtle, hsla(0, 0%, 98%, 1)); */

		--subtle: 0 0% 98%;
		--subtle2: 240 5% 65%;
		--foreground-subtle: 0 0% 98%;
		--foreground-success: 134 58% 85%;
		--foreground-warning: 48 96% 89%;
		--foreground-disable: 240 6% 90%;
	}

	.dark {
		--background: 0 0% 3.9%;
		--foreground: 0 0% 98%;
		--card: 0 0% 3.9%;
		--card-foreground: 0 0% 98%;
		--popover: 0 0% 3.9%;
		--popover-foreground: 0 0% 98%;
		--primary: 0 0% 98%;
		--primary-foreground: 0 0% 9%;
		--secondary: 0 0% 14.9%;
		--secondary-foreground: 0 0% 98%;
		--muted: 0 0% 14.9%;
		--muted-foreground: 0 0% 63.9%;
		--accent: 0 0% 14.9%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 14.9%;
		--input: 0 0% 14.9%;
		--ring: 0 0% 83.1%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
		--chart-6: 243 51% 56%;
		--chart-7: 243 50% 93%;
		--chart-8: 5 47% 55%;
		--chart-9: 5 46% 89%;
		--chart-10: 0 89 148;
		--sidebar-background: 240 5.9% 10%;
		--sidebar-foreground: 240 4.8% 95.9%;
		--sidebar-primary: 224.3 76.3% 48%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 240 3.7% 15.9%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
		--shadow-location-card: 204 15% 66%;
		--foreground-success: 134 58% 85%;
		--foreground-warning: 48 96% 89%;
		--foreground-disable: 240 6% 90%;
	}
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
	}
}

body {
	margin: 0;
	min-height: 100vh;
	font-family: "Inter", system-ui, sans-serif;
	background-color: hsl(var(--background));
	color: hsl(var(--foreground));
}

* {
	box-sizing: border-box;
}

.custom-scrollbar {
	scrollbar-width: thin;
	scrollbar-color: #005893 #E4E4E7;
	/* thumb color, track color */
}

.custom-scrollbar::-webkit-scrollbar {
	width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	background: #005893;
	border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
	background: #E4E4E7;
	border-radius: 8px;
	box-shadow: inset 0 0 5px grey;
}

.scrollbar-hide::-webkit-scrollbar {
	display: none;
}

.scrollbar-hide {
	-ms-overflow-style: none;
	scrollbar-width: none;
}

.rbc-time-content {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
}
.rbc-time-content::-webkit-scrollbar {
    display: none;             /* Chrome, Safari, Opera */
}
