import React from "react";

const ReferralsIcon: React.FC = () => {
	return (
		<svg
			width="32"
			height="33"
			viewBox="0 0 32 33"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M2.6665 15.168C5.77544 11.9117 10.1908 11.7584 13.3332 15.168H2.6665ZM11.3266 6.5013C11.3266 8.34225 9.83212 9.83464 7.98854 9.83464C6.14497 9.83464 4.65046 8.34225 4.65046 6.5013C4.65046 4.66036 6.14497 3.16797 7.98854 3.16797C9.83212 3.16797 11.3266 4.66036 11.3266 6.5013Z"
				fill="#C4F6D9"
			/>
			<path
				d="M2.6665 15.168C5.77544 11.9117 10.1908 11.7584 13.3332 15.168M11.3266 6.5013C11.3266 8.34225 9.83212 9.83464 7.98854 9.83464C6.14497 9.83464 4.65046 8.34225 4.65046 6.5013C4.65046 4.66036 6.14497 3.16797 7.98854 3.16797C9.83212 3.16797 11.3266 4.66036 11.3266 6.5013Z"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path
				d="M18.6665 29.832C21.7754 26.5758 26.1908 26.4224 29.3332 29.832H18.6665ZM27.3266 21.1654C27.3266 23.0063 25.8321 24.4987 23.9885 24.4987C22.145 24.4987 20.6505 23.0063 20.6505 21.1654C20.6505 19.3244 22.145 17.832 23.9885 17.832C25.8321 17.832 27.3266 19.3244 27.3266 21.1654Z"
				fill="#C4F6D9"
			/>
			<path
				d="M18.6665 29.832C21.7754 26.5758 26.1908 26.4224 29.3332 29.832M27.3266 21.1654C27.3266 23.0063 25.8321 24.4987 23.9885 24.4987C22.145 24.4987 20.6505 23.0063 20.6505 21.1654C20.6505 19.3244 22.145 17.832 23.9885 17.832C25.8321 17.832 27.3266 19.3244 27.3266 21.1654Z"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path
				d="M4 19.168C4 24.328 8.17333 28.5013 13.3333 28.5013L12 25.8346"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
			<path
				d="M20 4.5H28M20 8.5H28M20 12.5H24.6667"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
		</svg>
	);
};

export default ReferralsIcon;
