import { useState } from "react";
import { X, Filter, User, Video, Volume2 } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import MultiAsyncSelect from "@/components/common/MultiAsyncSelect";
import { Checkbox } from "@/components/ui/checkbox";
import { ToggleButton } from "@/components/common/ToggleButton";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useAllStations } from "@/features/locations/hooks/useStations";
import { useAppointmentMethods } from "@/features/locations/hooks/useAppointmentMethods";

interface FilterData {
	locations: string[];
	providers: string[];
	availableMethods: string[];
	status: string[];
}

interface FilterSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onApplyFilters?: (filters: FilterData) => void;
}

export function ServiceFilterSheet({
	open,
	onOpenChange,
	onApplyFilters,
}: FilterSheetProps) {
	const { organizationId } = useOrganizationContext();

	const [selectedTypes, setSelectedTypes] = useState<string[]>(["in-person"]);
	const [selectedStatus, setSelectedStatus] = useState<string[]>(["active"]);
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"all",
	]);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([
		"all",
	]);

	// Fetch real data from APIs
	const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
		{},
		organizationId || undefined
	);

	const { data: stationsData, isLoading: isLoadingStations } = useAllStations(
		{
			organizationId: organizationId || undefined,
			enabled: !!organizationId,
		}
	);

	const { data: appointmentMethods, isLoading: isLoadingMethods } =
		useAppointmentMethods(organizationId || null);

	const [filters, setFilters] = useState<FilterData>({
		locations: [],
		providers: [],
		availableMethods: [],
		status: ["active", "inactive"],
	});

	const handleLocationRemove = (location: string) => {
		setFilters((prev) => ({
			...prev,
			locations: prev.locations.filter((l) => l !== location),
		}));
	};

	const handleProviderRemove = (provider: string) => {
		setFilters((prev) => ({
			...prev,
			providers: prev.providers.filter((p) => p !== provider),
		}));
	};

	const handleMethodChange = (methods: string[]) => {
		setFilters((prev) => ({
			...prev,
			availableMethods: methods,
		}));
	};

	const handleStatusChange = (statuses: string[]) => {
		setFilters((prev) => ({
			...prev,
			status: statuses,
		}));
	};

	const handleReset = () => {
		setSelectedLocations(["all"]);
		setSelectedProviders(["all"]);
		setSelectedTypes([]);
		setSelectedStatus([]);
		setFilters({
			locations: [],
			providers: [],
			availableMethods: [],
			status: [],
		});
	};

	const handleApply = () => {
		// Transform selected values to actual data for filtering
		const appliedFilters: FilterData = {
			locations: selectedLocations.includes("all")
				? []
				: selectedLocations.filter((id) => id !== "all"),
			providers: selectedProviders.includes("all")
				? []
				: selectedProviders.filter((id) => id !== "all"),
			availableMethods: selectedTypes,
			status: selectedStatus,
		};

		onApplyFilters?.(appliedFilters);
		onOpenChange(false);
	};

	const handleCancel = () => {
		onOpenChange(false);
	};

	// Transform API data to options format
	const locationOptions = [
		{ value: "all", label: "All" },
		...(locationsData?.map((location) => ({
			value: location.id.toString(),
			label: location.name,
		})) || []),
	];

	const providerOptions = [
		{ value: "all", label: "All" },
		...(stationsData?.data?.map((station) => ({
			value: station.id.toString(),
			label: station.name,
		})) || []),
	];

	// Transform appointment methods from API
	const meetingTypes = appointmentMethods?.map((method) => {
		// Map common method names to icons
		const getMethodIcon = (name: string) => {
			const lowerName = name.toLowerCase();
			if (lowerName.includes("person") || lowerName.includes("office")) {
				return User;
			} else if (
				lowerName.includes("video") ||
				lowerName.includes("virtual")
			) {
				return Video;
			} else if (
				lowerName.includes("audio") ||
				lowerName.includes("phone")
			) {
				return Volume2;
			}
			return User; // Default icon
		};

		return {
			id: method.id.toString(),
			label: method.name,
			icon: getMethodIcon(method.name),
		};
	}) || [
		// Fallback to default if API fails
		{
			id: "in-person",
			label: "In Person",
			icon: User,
		},
		{
			id: "video",
			label: "Video",
			icon: Video,
			hasIndicator: true,
		},
		{
			id: "audio",
			label: "Audio",
			icon: Volume2,
		},
	];

	const toggleSelection = (typeId: string) => {
		setSelectedTypes((prev) =>
			prev.includes(typeId)
				? prev.filter((id) => id !== typeId)
				: [...prev, typeId]
		);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-9 py-9 sm:w-[540px] sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<SheetTitle className="flex items-center justify-between">
						<span>Filter</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetTitle>
					<p className="text-muted-foreground text-sm">
						Add Service information Below
					</p>
				</SheetHeader>

				<div className="space-y-4 py-6">
					{/* Locations Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Locations
						</label>
						<MultiAsyncSelect
							options={locationOptions}
							onValueChange={setSelectedLocations}
							defaultValue={selectedLocations}
							placeholder={
								isLoadingLocations
									? "Loading locations..."
									: "Select locations"
							}
							className="w-full"
							disabled={isLoadingLocations}
						/>
					</div>

					{/* Providers Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Providers/Stations
						</label>
						<MultiAsyncSelect
							options={providerOptions}
							onValueChange={setSelectedProviders}
							defaultValue={selectedProviders}
							placeholder={
								isLoadingStations
									? "Loading providers..."
									: "Select providers"
							}
							className="w-full"
							disabled={isLoadingStations}
						/>
					</div>

					{/* Services Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Services Available in Methods
						</label>
						{isLoadingMethods ? (
							<div className="flex items-center justify-center py-4">
								<span className="text-sm text-gray-500">
									Loading appointment methods...
								</span>
							</div>
						) : (
							<div className="flex flex-wrap gap-2">
								{meetingTypes.map((type) => (
									<ToggleButton
										key={type.id}
										label={type.label}
										icon={type.icon}
										isSelected={selectedTypes.includes(
											type.id
										)}
										onClick={() => toggleSelection(type.id)}
									/>
								))}
							</div>
						)}
					</div>

					{/* Status Section */}
					<div className="space-y-3">
						<label className="text-sm font-medium text-zinc-900">
							Status
						</label>
						<div className="flex flex-wrap gap-2">
							{filters.status.map((status) => (
								<ToggleButton
									key={status}
									label={status}
									isSelected={selectedStatus.includes(status)}
									onClick={() =>
										setSelectedStatus((prev) =>
											prev.includes(status)
												? prev.filter(
														(s) => s !== status
													)
												: [...prev, status]
										)
									}
									className="max-w-[120px] min-w-[154px]"
								/>
							))}
						</div>
					</div>
				</div>

				<SheetFooter className="flex-row justify-between">
					<Button
						variant="ghost"
						onClick={handleReset}
						className="text-muted-foreground hover:text-foreground"
					>
						Reset
					</Button>
					<div className="flex gap-3">
						<Button variant="outline" onClick={handleCancel}>
							Cancel
						</Button>
						<Button onClick={handleApply}>Apply</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
