import React from "react";
import { motion } from "framer-motion";

const DynamicStar: React.FC<{
	rating: number;
	size?: number;
}> = ({ rating, size = 90 }) => {
	const fillPercentage = (rating / 5) * 100;
	const uniqueId = React.useId();
	const clipPathId = `star-clip-${uniqueId}`;
	const gradientId = `star-gradient-${uniqueId}`;

	return (
		<svg
			width={size}
			height={size}
			viewBox="0 0 24 24"
			className="stroke-2"
		>
			{/* Define gradient for smooth transition */}
			<defs>
				<linearGradient id={gradientId}>
					<stop offset="0%" stopColor="#F59E0B" />
					<stop offset="100%" stopColor="#F59E0B" />
				</linearGradient>

				<clipPath id={clipPathId}>
					<motion.rect
						x="0"
						y="0"
						height="100%"
						initial={{ width: 0 }}
						animate={{ width: `${fillPercentage}%` }}
						transition={{
							duration: 1,
							ease: "easeOut",
						}}
					/>
				</clipPath>
			</defs>

			{/* Background star (gray) */}
			<path
				d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
				fill="#d9d9d9"
				stroke="#d9d9d9"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>

			{/* Animated foreground star */}
			<motion.path
				d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
				fill={`url(#${gradientId})`}
				stroke="#F59E0B"
				strokeLinecap="round"
				strokeLinejoin="round"
				clipPath={`url(#${clipPathId})`}
				initial={{ scale: 0.95, opacity: 0 }}
				animate={{ scale: 1, opacity: 1 }}
				transition={{
					duration: 0.3,
					ease: "easeOut",
				}}
			/>
		</svg>
	);
};

export default DynamicStar;
