import { useEffect, useState, type FC } from "react";
import { Search, Plus, MapPin, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { ServiceFilterSheet } from "@/features/locations/components/sheets";
import { useLocations } from "@/features/locations/hooks";
import type {
	Location,
	LocationsFilters,
	LocationsResponse,
} from "@/features/locations/types";
import { PatientsCard } from "./patients/PatientsCard";
import { SendBookingLinkSheet } from "@/features/schedule";

import {
	Tabs,
	TabsContent,
	TabsList,
	TabsTrigger,
} from "@/components/common/Tabs";
import { AllPatients } from "./patients/data-table/AllPatients";
import type { PatientResponse } from "../types";

interface PatientsTabProps {
	className?: string;
}

export const PatientsTab: FC<PatientsTabProps> = ({ className }) => {
	const [activeTab, setActiveTab] = useState("all");
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedServices, setSelectedServices] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddServiceForm, setShowAddServiceForm] = useState(false);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<Location | null>(
		null
	);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);

	const { data: locationsData2, isLoading } = useLocations({
		page: currentPage,
		search: searchTerm,
		limit: 10,
	});

	// Debounced search filter
	useEffect(() => {
		const timer = setTimeout(() => {
			setFilters((prev) => ({
				...prev,
				search: searchTerm || undefined,
				page: 1,
			}));
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	const locationsData: PatientResponse = {
		data: [
			{
				id: "1",
				name: "Dr. Abraham Lincoln",
				email: "<EMAIL>",
				phone: "+****************",
				lastVisit: "12 Aug 2024",
				isActive: true,
			},
			{
				id: "2",
				name: "Dr. Abraham Lincoln",
				email: "<EMAIL>",
				phone: "+****************",
				lastVisit: "12 Aug 2024",
				isActive: false,
			},
		],
		pagination: {
			page: 1,
			limit: 12,
			total: 2,
			totalPages: 1,
		},
	};

	const handleFilterChange = (newFilters: Partial<LocationsFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && locationsData?.data) {
			setSelectedServices(
				locationsData.data.map((location) => location.id)
			);
		} else {
			setSelectedServices([]);
		}
	};

	const handleLocationSelection = (locationId: string, selected: boolean) => {
		if (selected) {
			setSelectedServices((prev) => [...prev, locationId]);
		} else {
			setSelectedServices((prev) =>
				prev.filter((id) => id !== locationId)
			);
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleAddService = async (data: any) => {
		console.log("Adding new service:", data);
		setShowAddServiceForm(false);
	};

	const handleViewLocation = (location: Location) => {
		setSelectedLocation(location);
		setShowLocationDetails(true);
	};

	const handleApplyFilters = (filterData: any) => {
		console.log("Applying filters:", filterData);
	};

	const tabItems = [
		{ value: "all", label: "All" },
		{ value: "waitlisted", label: "Waitlisted" },
	];

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-3 pl-4">
				<h1 className="text-2xl font-bold">Patients</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						className="bg-primary hover:bg-primary/90 cursor-pointer text-white hover:text-white"
						onClick={() => setShowAddServiceForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add a Patient
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="">
				<Tabs
					items={tabItems}
					defaultValue="all"
					value={activeTab}
					onValueChange={setActiveTab}
					useRouting={true}
					searchParamKey="patients-tab"
					listClassName="max-w-md"
				>
					<div className="scrollbar-hide max-h-screen overflow-y-scroll">
						<TabsContent value="all" className="w-full">
							<AllPatients
								selectedServices={selectedServices}
								locationsData={locationsData}
								handleSelectAll={handleSelectAll}
								handleLocationSelection={
									handleLocationSelection
								}
								handleViewLocation={handleViewLocation}
								handlePageChange={handlePageChange}
								setShowAddServiceForm={setShowAddServiceForm}
								setShowSendBookingLinkSheet={
									setShowSendBookingLinkSheet
								}
							/>
						</TabsContent>
						<TabsContent value="waitlisted">
							<AllPatients
								selectedServices={selectedServices}
								locationsData={locationsData}
								handleSelectAll={handleSelectAll}
								handleLocationSelection={
									handleLocationSelection
								}
								handleViewLocation={handleViewLocation}
								handlePageChange={handlePageChange}
								setShowAddServiceForm={setShowAddServiceForm}
								setShowSendBookingLinkSheet={
									setShowSendBookingLinkSheet
								}
							/>
						</TabsContent>
					</div>
				</Tabs>
			</div>

			{/* Filter Sheet */}
			<ServiceFilterSheet
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
			/>

			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>
		</div>
	);
};
