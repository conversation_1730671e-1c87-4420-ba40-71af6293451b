import { useQuery } from "@tanstack/react-query";
import {
	formsApi,
	type GetFormsForConditionalResponse,
	type GetCustomIntakesForConditionalResponse,
} from "@/lib/api/formsApi";

export const useForms = (organizationId: number | null) => {
	return useQuery<GetFormsForConditionalResponse>({
		queryKey: ["forms", "conditional", organizationId],
		queryFn: () => formsApi.getFormsForConditional(organizationId!),
		enabled: !!organizationId,
		staleTime: 5 * 60 * 1000, 
	});
};

export const useCustomIntakes = (organizationId: number | null) => {
	return useQuery<GetCustomIntakesForConditionalResponse>({
		queryKey: ["customIntakes", "conditional", organizationId],
		queryFn: () => formsApi.getCustomIntakesForConditional(organizationId!),
		enabled: !!organizationId,
		staleTime: 5 * 60 * 1000, 
	});
};
