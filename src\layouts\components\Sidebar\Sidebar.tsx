import React from "react";
import { cn } from "@/lib/utils";
import { NavLink, useLocation } from "react-router";
import type { SidebarProps, NavigationItem } from "./types";
// import { useFilteredNavigationItems } from "./utils";
import { useUIStore } from "@/stores/uiStore";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Zap, ChevronRight, Settings, Clock, ChevronDown } from "lucide-react";
import AnalyticsIcon from "@/assets/icons/sidebar/analytics";
import ClientsIcon from "@/assets/icons/sidebar/clients";
import DashboardIcon from "@/assets/icons/sidebar/dashboard";
import FormsIcon from "@/assets/icons/sidebar/forms";
import LogoutIcon from "@/assets/icons/sidebar/logout";
import ScheduleIcon from "@/assets/icons/sidebar/schedule";
import SettingsIcon from "@/assets/icons/sidebar/settings";
// import TeamMembersIcon from "@/assets/icons/sidebar/teamMembers";
import HelpIcon from "@/assets/icons/sidebar/helpIcon";
import WaitlistIcon from "@/assets/icons/sidebar/waitlist";
import WorkplaceIcon from "@/assets/icons/sidebar/workplaceIcon";

const navigationItems: NavigationItem[] = [
	{
		id: "dashboard",
		label: "Dashboard",
		href: "/",
		icon: <DashboardIcon color="inherit" />,
		// Dashboard is accessible to all authenticated users
	},
	{
		id: "analytics",
		label: "Analytics",
		href: "/dashboard/analytics",
		icon: <AnalyticsIcon />,
		// permissions: ['analytics:view'],
		// roles: ['admin', 'manager', 'analyst'],
	},
	{
		id: "waitlist",
		label: "Waitlist",
		href: "/dashboard/waitlist",
		icon: <WaitlistIcon color="inherit" />,
		// permissions: ['waitlist:view'],
		// groups: ['waitlist_managers'],
	},
	{
		id: "schedule",
		label: "Schedule",
		href: "/dashboard/schedule",
		icon: <ScheduleIcon />,
		children: [
			{
				id: "schedule-manage",
				label: "Manage Appointments",
				href: "/dashboard/schedule/manage-appointments",
				// icon: <Settings className="h-3.5 w-3.5" />,
			},
			{
				id: "schedule-planner",
				label: "Planner",
				href: "/dashboard/schedule/planner",
				// icon: <Clock className="h-3.5 w-3.5" />,
			},
			{
				id: "schedule-history",
				label: "History",
				href: "/dashboard/schedule/history",
				// icon: <Clock className="h-3.5 w-3.5" />,
			},
		],
		// permissions: ['schedule:view'],
	},
	{
		id: "workplace",
		label: "Workplace",
		href: "/dashboard/workplace",
		icon: <WorkplaceIcon />,
		children: [
			// {
			// 	id: "workplace-manage",
			// 	label: "Organization",
			// 	href: "/dashboard/workplace/organization",
			// 	// icon: <Settings className="h-3.5 w-3.5" />,
			// },
			{
				id: "workplace-locations",
				label: "Locations",
				href: "/dashboard/workplace/locations",
				// icon: <Clock className="h-3.5 w-3.5" />,
			},
			// {
			// 	id: "workplace-providers",
			// 	label: "Providers",
			// 	href: "/dashboard/workplace/providers",
			// },
			{
				id: "workplace-services",
				label: "Services",
				href: "/dashboard/workplace/locations?location-tab=services",
				// icon: <Clock className="h-3.5 w-3.5" />,
			},
			{
				id: "workplace-members",
				label: "Members",
				href: "/dashboard/workplace/locations?location-tab=team-members",
				// icon: <Clock className="h-3.5 w-3.5" />,
			},
			{
				id: "organization-settings",
				label: "Organization Settings",
				href: "/dashboard/workplace/locations?location-tab=organization-settings",
				// icon: <Settings className="h-3.5 w-3.5" />,
			},
		],
		// permissions: ['workplace:view'],
		// roles: ['admin', 'manager'],
	},
	{
		id: "patients",
		label: "Patients",
		href: "/dashboard/patients",
		icon: <ClientsIcon size={20} />,
		children: [
			{
				id: "patients-all",
				label: "All Patients",
				href: "/dashboard/patients",
			},
			{
				id: "patients-categories",
				label: "Patient Categories",
				href: "/dashboard/patients/categories",
			},
			{
				id: "patients-reviews",
				label: "Patient Reviews",
				href: "/dashboard/patients/reviews",
			},
		],
		// permissions: ['patients:view'],
		// groups: ['patient_managers'],
	},
	{
		id: "forms",
		label: "Forms",
		href: "/dashboard/forms",
		icon: <FormsIcon />,
		// permissions: ['forms:view'],
		// features: ['forms_module'],
	},
];

export const Sidebar = ({
	className,
	isOpen = false,
	onClose,
	...props
}: SidebarProps) => {
	// const filteredNavigationItems = useFilteredNavigationItems(navigationItems);
	const filteredNavigationItems = navigationItems;
	const {
		sidebarCollapsed,
		mobileMenuOpen,
		setMobileMenuOpen,
		setSidebarCollapsed,
		expandedNavItem,
		toggleNavItem,
	} = useUIStore();

	const {
		organization,
		organizations,
		setCurrentOrganization,
		isLoading: isOrgLoading,
	} = useOrganizationContext();

	const location = useLocation();

	// Function to generate organization initials
	const getOrganizationInitials = (name: string) => {
		return name
			.split(" ")
			.map((word) => word.charAt(0).toUpperCase())
			.join("")
			.slice(0, 2);
	};

	// Function to get organization logo background color
	const getOrganizationColor = (name: string) => {
		// Generate a consistent color based on organization name
		const colors = [
			"bg-orange-500",
			"bg-blue-500",
			"bg-green-500",
			"bg-purple-500",
			"bg-red-500",
			"bg-indigo-500",
			"bg-pink-500",
			"bg-teal-500",
		];
		const hash = name.split("").reduce((acc, char) => {
			return char.charCodeAt(0) + ((acc << 5) - acc);
		}, 0);
		return colors[Math.abs(hash) % colors.length];
	};

	// Function to handle organization switching
	const handleOrganizationSwitch = (org: any) => {
		setCurrentOrganization(org);
	};

	// Custom isActive function that handles query parameters
	const isActiveWithQuery = (href: string) => {
		const currentPath = location.pathname;
		const currentSearch = location.search;

		// If the href contains a query string, check both path and query
		if (href.includes("?")) {
			const [path, query] = href.split("?");
			return currentPath === path && currentSearch === `?${query}`;
		}

		// For paths without query strings, check if it's an exact match
		// This prevents parent items from being active when child items with query params are active
		if (currentSearch) {
			// If current URL has query params, only match exact path without query params
			return currentPath === href && !currentSearch;
		}

		// For paths without query strings and no current search params
		return currentPath === href;
	};

	// Local state for hover functionality
	const [isHovered, setIsHovered] = React.useState(false);
	const sidebarRef = React.useRef<HTMLDivElement>(null);
	const dropdownTriggerRef = React.useRef<HTMLButtonElement>(null);
	const dropdownContentRef = React.useRef<HTMLDivElement>(null);

	// Use state for mobile detection to prevent infinite re-renders
	const [isMobile, setIsMobile] = React.useState(() => {
		if (typeof window !== "undefined") {
			return window.innerWidth < 768;
		}
		return false;
	});

	// Handle window resize with proper cleanup
	React.useEffect(() => {
		const handleResize = () => {
			setIsMobile(window.innerWidth < 768);
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	// Handle click outside to collapse sidebar on desktop
	React.useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			// Only handle click outside on desktop when sidebar is expanded
			if (
				!isMobile &&
				sidebarRef.current &&
				!sidebarRef.current.contains(event.target as Node) &&
				!sidebarCollapsed &&
				!dropdownTriggerRef.current?.contains(event.target as Node) &&
				!dropdownContentRef.current?.contains(event.target as Node)
			) {
				setSidebarCollapsed(true);
			}
		};

		// Only add the event listener on desktop
		if (!isMobile) {
			document.addEventListener("mousedown", handleClickOutside);
			return () => {
				document.removeEventListener("mousedown", handleClickOutside);
			};
		}
	}, [isMobile, sidebarCollapsed, setSidebarCollapsed]);

	// Determine if sidebar should appear expanded
	// On desktop: expand on hover or when not collapsed
	// On mobile: use existing mobile menu logic
	const shouldShowExpanded = isMobile
		? mobileMenuOpen
		: !sidebarCollapsed || isHovered;

	const handleItemClick = () => {
		// Close mobile menu when navigation item is clicked on mobile
		if (isMobile && mobileMenuOpen) {
			setMobileMenuOpen(false);
		}
		onClose?.();
	};

	const handleMouseEnter = () => {
		if (!isMobile) {
			setIsHovered(true);
		}
	};

	const handleMouseLeave = (event: React.MouseEvent) => {
		if (!isMobile) {
			// Check if mouse is moving to dropdown content
			const relatedTarget = event.relatedTarget as Element;
			if (
				dropdownContentRef.current &&
				(dropdownContentRef.current.contains(relatedTarget) ||
					dropdownTriggerRef.current?.contains(relatedTarget))
			) {
				return; // Don't collapse if moving to dropdown
			}
			setIsHovered(false);
		}
	};

	return (
		<>
			{/* Mobile overlay - only show on mobile when drawer is open */}
			{isMobile && mobileMenuOpen && (
				<div
					className="fixed inset-0 z-40 bg-gray-600 opacity-75 transition-opacity md:hidden"
					onClick={onClose}
				/>
			)}

			{/* Sidebar */}
			<div
				ref={sidebarRef}
				onMouseEnter={handleMouseEnter}
				onMouseLeave={handleMouseLeave}
				className={cn(
					"bg-primary fixed inset-y-0 left-0 z-50 transform overflow-hidden transition-all duration-300 ease-in-out",
					// Mobile behavior: slide in/out based on mobileMenuOpen
					"md:relative md:translate-x-0",
					isMobile
						? mobileMenuOpen
							? "translate-x-0"
							: "-translate-x-full"
						: "translate-x-0",
					// Desktop width: collapsed (64px) or expanded (256px), with hover consideration
					!isMobile && shouldShowExpanded ? "md:w-64" : "md:w-16",
					// Mobile width: always full drawer width when open
					"w-64",
					className
				)}
				{...props}
			>
				<div className="flex h-full flex-col overflow-y-auto py-4">
					{/* Header */}
					<div
						className={cn(
							"flex h-16 items-center",
							shouldShowExpanded
								? "justify-between px-4"
								: "justify-center px-2"
						)}
					>
						{shouldShowExpanded ? (
							<DropdownMenu>
								<DropdownMenuTrigger
									ref={dropdownTriggerRef}
									asChild
								>
									<Button
										variant="ghost"
										className="flex w-full items-center justify-between p-0 text-white hover:bg-white/10 hover:text-white"
										disabled={isOrgLoading}
									>
										<div className="flex items-center space-x-3">
											<div
												className={cn(
													"flex h-8 w-8 items-center justify-center rounded-full",
													organization?.name
														? getOrganizationColor(
																organization.name
															)
														: "bg-orange-500"
												)}
											>
												<span className="text-sm font-bold text-white">
													{organization?.name
														? getOrganizationInitials(
																organization.name
															)
														: "UN"}
												</span>
											</div>
											<span className="max-w-32 text-sm font-medium text-white">
												<p className="truncate text-left">
													{isOrgLoading
														? "Loading..."
														: organization?.name ||
															"Unknown Organization"}
												</p>
											</span>
										</div>
										<ChevronDown className="ui-open:rotate-180 h-4 w-4 text-gray-400 transition-transform" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent
									align="end"
									className="absolute top-full left-full w-64"
									ref={dropdownContentRef}
									onMouseEnter={() => setIsHovered(true)}
									onMouseLeave={(event) => {
										// Check if mouse is leaving to go back to sidebar
										const relatedTarget =
											event.relatedTarget as Element;
										if (
											!sidebarRef.current?.contains(
												relatedTarget
											)
										) {
											setIsHovered(false);
										}
									}}
								>
									<div className="p-2">
										<p className="mb-2 px-2 text-xs text-gray-500">
											Switch Organization
										</p>
										{isOrgLoading ? (
											<div className="flex items-center justify-center p-4">
												<span className="text-sm text-gray-500">
													Loading organizations...
												</span>
											</div>
										) : (
											organizations.map((org) => (
												<DropdownMenuItem
													key={org.id}
													onClick={() =>
														handleOrganizationSwitch(
															org
														)
													}
													className={cn(
														"flex cursor-pointer items-center space-x-3 rounded-md p-3",
														organization?.id ===
															org.id
															? "bg-blue-50"
															: ""
													)}
												>
													<div
														className={cn(
															"flex h-8 w-8 items-center justify-center rounded-full",
															getOrganizationColor(
																org.name
															)
														)}
													>
														<span className="text-sm font-bold text-white">
															{getOrganizationInitials(
																org.name
															)}
														</span>
													</div>
													<div className="min-w-0 flex-1">
														<p className="truncate text-sm font-medium">
															{org.name}
														</p>
													</div>
													{organization?.id ===
														org.id && (
														<div className="h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
													)}
												</DropdownMenuItem>
											))
										)}
									</div>
								</DropdownMenuContent>
							</DropdownMenu>
						) : (
							<div
								className={cn(
									"flex h-8 w-8 items-center justify-center rounded-full",
									organization?.name
										? getOrganizationColor(
												organization.name
											)
										: "bg-orange-500"
								)}
								title={
									organization?.name || "Unknown Organization"
								}
							>
								<span className="text-sm font-bold text-white">
									{organization?.name
										? getOrganizationInitials(
												organization.name
											)
										: "UN"}
								</span>
							</div>
						)}
					</div>

					{/* Navigation */}
					<nav
						className={cn(
							"flex-1 space-y-0.5 py-4",
							shouldShowExpanded ? "px-0" : "px-0"
						)}
					>
						{filteredNavigationItems.map((item) => (
							<div
								key={item.id}
								className={cn(
									expandedNavItem === item.id &&
										shouldShowExpanded
										? "bg-[#043B6D] pb-2"
										: "px-4",
									shouldShowExpanded ? "px-4" : "px-2"
								)}
							>
								{/* Parent Navigation Item */}
								{item.children && item.children.length > 0 ? (
									<div
										className={cn(
											expandedNavItem === item.id &&
												shouldShowExpanded
												? "px-3"
												: "",
											shouldShowExpanded ? "px-0" : "px-0"
										)}
									>
										<button
											onClick={() => {
												if (shouldShowExpanded) {
													toggleNavItem(item.id);
												}
											}}
											className={cn(
												"group flex w-full cursor-pointer items-center rounded-md text-sm font-medium transition-all duration-200",
												shouldShowExpanded
													? "px-3 py-2"
													: "justify-center rounded-md px-2 py-3",
												expandedNavItem === item.id &&
													shouldShowExpanded
													? "bg-[#043B6D] text-white"
													: "text-white hover:bg-gray-100 hover:text-gray-900"
											)}
											title={
												!shouldShowExpanded
													? item.label
													: undefined
											}
										>
											<span
												className={cn(
													"h-[18px] w-[18px] flex-shrink-0",
													shouldShowExpanded
														? "mr-3"
														: ""
												)}
											>
												{item.icon}
											</span>
											{shouldShowExpanded && (
												<>
													<span className="flex-1 text-left text-base tracking-[-0.16px]">
														{item.label}
													</span>
													<ChevronRight
														className={cn(
															"ml-auto h-4 w-4 transition-transform duration-200 ease-in-out",
															expandedNavItem ===
																item.id
																? "rotate-90"
																: "rotate-0"
														)}
													/>
												</>
											)}
										</button>

										{/* Child Navigation Items with smooth animation */}
										{shouldShowExpanded &&
											item.children && (
												<div
													className={cn(
														"overflow-hidden transition-all duration-300 ease-in-out",
														expandedNavItem ===
															item.id
															? "max-h-96 opacity-100"
															: "max-h-0 opacity-0"
													)}
												>
													<div className="space-y-0.5">
														{item.children.map(
															(child) => (
																<NavLink
																	key={
																		child.id
																	}
																	to={
																		child.href
																	}
																	onClick={
																		handleItemClick
																	}
																	className={() =>
																		cn(
																			"group flex h-10 items-center rounded-md px-10 py-0.5 pr-0 text-sm font-medium transition-colors",
																			isActiveWithQuery(
																				child.href
																			)
																				? "bg-sidebar-accent text-sidebar-accent-foreground"
																				: "text-white hover:bg-gray-100 hover:text-gray-900"
																		)
																	}
																>
																	{child?.icon && (
																		<span className="mr-3 h-3.5 w-3.5 flex-shrink-0">
																			{
																				child.icon
																			}
																		</span>
																	)}
																	<span className="text-sm tracking-[-0.14px]">
																		{
																			child.label
																		}
																	</span>
																</NavLink>
															)
														)}
													</div>
												</div>
											)}
									</div>
								) : (
									<NavLink
										to={item.href}
										onClick={handleItemClick}
										className={() =>
											cn(
												"group flex items-center rounded-md text-sm font-medium transition-colors",
												shouldShowExpanded
													? "px-3 py-2"
													: "justify-center px-2 py-3",
												isActiveWithQuery(item.href)
													? "bg-sidebar-accent text-sidebar-accent-foreground"
													: "text-white hover:bg-gray-100 hover:text-gray-900"
											)
										}
										title={
											!shouldShowExpanded
												? item.label
												: undefined
										}
									>
										<span
											className={cn(
												"h-[18px] w-[18px] flex-shrink-0",
												shouldShowExpanded ? "mr-3" : ""
											)}
										>
											{item.icon}
										</span>
										{shouldShowExpanded && (
											<span className="flex-1 text-base tracking-[-0.16px]">
												{item.label}
											</span>
										)}
									</NavLink>
								)}
							</div>
						))}
					</nav>

					{/* Bottom section */}
					<div
						className={cn(
							"space-y-1",
							shouldShowExpanded ? "p-4" : "p-2 pb-4"
						)}
					>
						{/* Help */}
						<NavLink
							to="/dashboard/help"
							className={() =>
								cn(
									"group flex items-center rounded-md text-sm font-medium transition-colors",
									shouldShowExpanded
										? "px-3 py-2"
										: "justify-center px-2 py-3",
									isActiveWithQuery("/dashboard/help")
										? "bg-sidebar-accent text-white"
										: "text-white hover:bg-gray-100 hover:text-gray-900"
								)
							}
							title={!shouldShowExpanded ? "Help" : undefined}
						>
							<div
								className={cn(
									"h-[18px] w-[18px] flex-shrink-0",
									shouldShowExpanded ? "mr-3" : ""
								)}
							>
								<HelpIcon />
							</div>
							{shouldShowExpanded && <span>Help</span>}
						</NavLink>

						{/* Settings */}
						<NavLink
							to="/dashboard/settings"
							className={() =>
								cn(
									"group flex items-center rounded-md text-sm font-medium transition-colors",
									shouldShowExpanded
										? "px-3 py-2"
										: "justify-center px-2 py-3",
									isActiveWithQuery("/dashboard/settings")
										? "bg-sidebar-accent text-white"
										: "text-white hover:bg-gray-100 hover:text-gray-900"
								)
							}
							title={!shouldShowExpanded ? "Settings" : undefined}
						>
							<div
								className={cn(
									"h-[18px] w-[18px] flex-shrink-0",
									shouldShowExpanded ? "mr-3" : ""
								)}
							>
								<SettingsIcon />
							</div>
							{shouldShowExpanded && <span>Settings</span>}
						</NavLink>

						{/* Sign out */}
						<Button
							onClick={() => {
								console.log("Sign out clicked");
							}}
							className={cn(
								"group mb-4 flex h-auto w-full cursor-pointer items-center justify-start rounded-md text-sm font-medium text-white hover:bg-gray-100 hover:text-gray-900",
								shouldShowExpanded
									? "gap-0 px-3 py-2"
									: "justify-center px-2 py-3"
							)}
							title={!shouldShowExpanded ? "Sign out" : undefined}
						>
							<div
								className={cn(
									"h-[18px] w-[18px] flex-shrink-0",
									shouldShowExpanded ? "mr-3" : ""
								)}
							>
								<LogoutIcon />
							</div>
							{shouldShowExpanded && <span>Sign out</span>}
						</Button>

						{/* Upgrade button */}
						<Button
							onClick={() => {
								console.log("Upgrade clicked");
							}}
							className={cn(
								"focus:ring-primary hover:bg-primary/90 h-auto w-full cursor-pointer rounded-md border border-white bg-[#043B6D] px-3 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none",
								shouldShowExpanded
									? "gap-0 px-3 py-2"
									: "justify-center px-2 py-3"
							)}
							title={!shouldShowExpanded ? "Upgrade" : undefined}
						>
							<div
								className={cn(
									"h-5 w-5 flex-shrink-0",
									shouldShowExpanded ? "mr-3" : ""
								)}
							>
								<Zap className="h-4 w-4" />
							</div>
							{shouldShowExpanded && <span>Upgrade</span>}
						</Button>
					</div>
				</div>
			</div>
		</>
	);
};
