import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { <PERSON>Check } from "lucide-react";

interface AuditLog {
	id: string;
	patient: string;
	login: string;
	ip: string;
	timestamp: string;
	event: string;
}

const mockAuditLogs: AuditLog[] = [
	{
		id: "1",
		patient: "<PERSON>",
		login: "<EMAIL>",
		ip: "************",
		timestamp: "Oct 25, 2024 — 04:30 PM",
		event: "Booked appointment with Dr. <PERSON><PERSON> (First Time Consultation)",
	},
	{
		id: "2",
		patient: "<PERSON>",
		login: "<EMAIL>",
		ip: "**********",
		timestamp: "Oct 23, 2024 — 11:15 AM",
		event: "Viewed billing history",
	},
	{
		id: "3",
		patient: "<PERSON>",
		login: "<EMAIL>",
		ip: "***********",
		timestamp: "Oct 20, 2024 — 07:40 PM",
		event: "Updated personal information",
	},
	{
		id: "4",
		patient: "<PERSON>",
		login: "<EMAIL>",
		ip: "************",
		timestamp: "Oct 25, 2024 — 04:32 PM",
		event: "Accepted privacy agreement",
	},
];

export function AuditLogsTabContent() {
	return (
		<div className="flex flex-col gap-6">
			{/* Header */}
			<div>
				<h1 className="flex items-center gap-2 text-left text-2xl font-bold">
					Audit Logs
				</h1>
				<p className="text-muted-foreground mt-1 text-sm">
					Monitor actions and system events linked to user accounts.
				</p>
			</div>

			{/* List */}
			<div className="flex w-full flex-col items-start justify-start">
				{mockAuditLogs.map((log) => (
					<Card
						key={log.id}
						className="relative p-0 flex w-full items-center justify-start rounded-none border-transparent border-t border-t-[#E4E4E7]  shadow-none"
						style={{ paddingLeft: "0px", paddingRight: "0px" }}
					>
						<CardHeader className="flex w-full flex-row flex-nowrap px-0 pb-4 relative -bottom-2 text-sm text-gray-800">
							<span className="font-semibold">
								{log.patient} •
							</span>
							{log.event}
						</CardHeader>
						<CardContent className="text-muted-foreground absolute top-2 p-0 left-0 flex w-full items-center justify-start gap-3 text-xs">
							<div>Login: {log.login}</div>
							<div>IP: {log.ip}</div>
							<div className="text-xs text-gray-500">
								{log.timestamp}
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
