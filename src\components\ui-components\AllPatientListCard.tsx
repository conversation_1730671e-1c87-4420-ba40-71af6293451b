import * as React from "react";
import { cn } from "@/lib/utils";
import {
	MoreHorizontal,
	Trash2,
	<PERSON><PERSON>l,
	MessageCircleMore,
	Info,
} from "lucide-react";
import { Checkbox } from "../common/Checkbox";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

export interface Patient {
	id: string;
	name: string;
	email: string;
	phone: string;
	status: "Active" | "Inactive";
	lastVisit: string;
	syncStatus: "synced" | "failed" | "pending" | null;
	profile_picture_url?: string;
}

export interface PatientAction {
	type: "delete" | "edit" | "info" | "email";
	onClick: (patient: Patient) => void;
	disabled?: boolean;
}

export interface AllPatientListCardProps
	extends React.HTMLAttributes<HTMLDivElement> {
	patient: Patient;
	actions?: PatientAction[];
	showCheckbox?: boolean;
	onCheckboxChange?: (patientId: string, checked: boolean) => void;
	checked?: boolean;
	variant?: "default" | "compact";
}

const AllPatientListCard = React.forwardRef<
	HTMLTableRowElement,
	AllPatientListCardProps
>(
	(
		{
			className,
			patient,
			actions = [],
			showCheckbox = true,
			onCheckboxChange,
			checked = false,
			variant = "default",
			...props
		},
		ref
	) => {
		const defaultActions: PatientAction[] = [
			{
				type: "delete",
				onClick: (patient) => console.log("Delete", patient.id),
			},
			{
				type: "edit",
				onClick: (patient) => console.log("Edit", patient.id),
			},
			{
				type: "info",
				onClick: (patient) => console.log("info", patient.phone),
			},
			{
				type: "email",
				onClick: (patient) => console.log("Email", patient.email),
			},
		];

		const finalActions = actions.length > 0 ? actions : defaultActions;

		const getActionIcon = (type: PatientAction["type"]) => {
			switch (type) {
				case "edit":
					return <Pencil className="h-3 w-3 text-gray-500" />;
				case "info":
					return <Info className="h-3 w-3 text-gray-500" />;
				case "email":
					return (
						<MessageCircleMore className="h-3 w-3 text-gray-500" />
					);
				case "delete":
					return <Trash2 className="h-3 w-3 text-gray-500" />;
				default:
					return <MoreHorizontal className="h-3 w-3 text-gray-500" />;
			}
		};

		const getInitials = (name: string) => {
			return name
				.split(" ")
				.map((n) => n[0])
				.join("")
				.toUpperCase();
		};

		const getSyncStatusText = (syncStatus: Patient["syncStatus"]) => {
			switch (syncStatus) {
				case "synced":
					return "Synced";
				case "failed":
					return "Sync Failed";
				case "pending":
					return "Sync in Progress";
				case null:
				default:
					return "Not Sync";
			}
		};

		const getSyncStatusColor = (syncStatus: Patient["syncStatus"]) => {
			switch (syncStatus) {
				case "synced":
					return "bg-green-500";
				case "failed":
					return "bg-red-500";
				case "pending":
					return "bg-yellow-500";
				case null:
				default:
					return "bg-gray-400";
			}
		};

		return (
			<tr
				ref={ref}
				className={cn(
					"border-t border-gray-200 transition-colors",
					variant === "default" ? "h-16" : "h-12",
					className
				)}
				{...props}
			>
				{showCheckbox && (
					<td className="w-12 px-4">
						<div onClick={(e) => e.stopPropagation()}>
							<Checkbox
								checked={checked}
								onCheckedChange={(isChecked) =>
									onCheckboxChange?.(patient.id, isChecked)
								}
								className="h-3 w-3 border-[#E4E4E7] [&_svg]:h-2 [&_svg]:w-2 [&>*]:flex [&>*]:items-center [&>*]:justify-center"
							/>
						</div>
					</td>
				)}

				<td className="px-3">
					<div className="flex items-center gap-3">
						<div className="relative flex h-9 w-9 items-center justify-center overflow-hidden rounded-full bg-gray-100">
							{patient.profile_picture_url ? (
								<>
									<img
										src={patient.profile_picture_url}
										alt={patient.name}
										className="h-full w-full object-cover"
										onError={(e) => {
											// Hide image and show initials on error
											const img = e.currentTarget;
											const initialsDiv =
												img.nextElementSibling as HTMLElement;
											img.style.display = "none";
											if (initialsDiv) {
												initialsDiv.style.display =
													"block";
											}
										}}
									/>
									<div
										className="absolute inset-0 flex items-center justify-center text-xs font-medium text-gray-600"
										style={{ display: "none" }}
									>
										{getInitials(patient.name)}
									</div>
								</>
							) : (
								<div className="text-xs font-medium text-gray-600">
									{getInitials(patient.name)}
								</div>
							)}
						</div>
						<div className="flex items-center gap-1">
							<div className="text-sm font-medium text-gray-900">
								{patient.name}
							</div>
							<Tooltip>
								<TooltipTrigger asChild>
									<div
										className={cn(
											"h-1.5 w-1.5 rounded-full",
											getSyncStatusColor(
												patient.syncStatus
											)
										)}
									/>
								</TooltipTrigger>
								<TooltipContent
									side="top"
									sideOffset={8}
									className="border-0 bg-white p-0 shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.06)] [&_svg]:!hidden [&>*:last-child]:!hidden [&>svg]:!hidden"
								>
									<div className="relative">
										<div className="max-w-44 rounded-md bg-white px-2 py-1.5">
											<div className="text-xs leading-none font-normal text-gray-900">
												{getSyncStatusText(
													patient.syncStatus
												)}
											</div>
										</div>
										<div className="absolute top-full right-4">
											<div
												className="h-1.5 w-3 bg-white"
												style={{
													clipPath:
														"polygon(50% 100%, 0% 0%, 100% 0%)",
												}}
											/>
										</div>
									</div>
								</TooltipContent>
							</Tooltip>
						</div>
					</div>
				</td>

				<td className="w-24 px-3">
					<div
						className={cn(
							"inline-flex items-center rounded-md px-2 py-1",
							patient.status === "Active"
								? "bg-green-50 text-green-700"
								: "bg-gray-100 text-gray-600"
						)}
					>
						<div className="text-[10px] font-medium">
							{patient.status}
						</div>
					</div>
				</td>

				<td className="px-3">
					<div className="text-xs text-gray-500">{patient.email}</div>
				</td>

				<td className="w-32 px-3">
					<div className="text-xs text-gray-500">{patient.phone}</div>
				</td>

				<td className="w-28 px-3">
					<div className="text-xs text-gray-500">
						{patient.lastVisit}
					</div>
				</td>

				{finalActions.length > 0 && (
					<td className="w-20 px-3">
						<div className="flex items-center justify-end gap-1.5">
							{finalActions.map((action, index) => (
								<Tooltip key={index}>
									<TooltipTrigger asChild>
										<button
											onClick={(e) => {
												e.stopPropagation();
												action.onClick(patient);
											}}
											disabled={action.disabled}
											className={cn(
												"flex h-6 w-6 cursor-pointer items-center justify-center rounded-md border border-gray-200 bg-gray-50 p-1 transition-colors hover:bg-gray-100",
												action.disabled &&
													"cursor-not-allowed opacity-50"
											)}
										>
											{getActionIcon(action.type)}
										</button>
									</TooltipTrigger>
									<TooltipContent
										side="top"
										sideOffset={8}
										className="border-0 bg-white p-0 shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.06)] [&_svg]:!hidden [&>*:last-child]:!hidden [&>svg]:!hidden"
									>
										<div className="relative">
											<div className="max-w-44 rounded-md bg-white px-2 py-1.5">
												<div className="text-xs leading-none font-normal text-gray-900">
													{action.type === "info"
														? "View Details"
														: action.type === "edit"
															? "Edit"
															: action.type ===
																  "delete"
																? "Delete"
																: action.type ===
																	  "email"
																	? "Message"
																	: "Action"}
												</div>
											</div>
											<div className="absolute top-full right-4">
												<div
													className="h-1.5 w-3 bg-white"
													style={{
														clipPath:
															"polygon(50% 100%, 0% 0%, 100% 0%)",
													}}
												/>
											</div>
										</div>
									</TooltipContent>
								</Tooltip>
							))}
						</div>
					</td>
				)}
			</tr>
		);
	}
);

AllPatientListCard.displayName = "AllPatientListCard";

export { AllPatientListCard };
