export type PlannerCategoriesListQueryParams = {
    category_id: string;
    client_category: string;
    is_active: boolean;
    ordering: string;
    page: number;
    per_page: number;
    planner: string;
    planner_id: string;
    preference_type: string;
    search: string;
    status: string;
}

export type PlannerCategoriesListResponse = {
    count: number;
    results: {
        category_name: string;
        client_category: string;
        core_category_id: number;
        created_at: string;
        id: string;
        rule_name: string;
        updated_at: string;
        priority: number;
        preference_type: string;
        weekdays: {
            description: string;
        },
        start_time: string;
        end_time: string;
        is_all_day: boolean;
        max_frequency: number;
        frequency_period: string;
        start_date: string;
        end_date: string;
        occurrence_pattern: string;
        recurrence_interval: number;
        status: string;
        is_active: boolean;
    }[],
    next: string;
    previous: string;
}

export type PlannerCategoryRulesResponse = {
    category_name: string;
    client_category: string;
    core_category_id: number,
    created_at: string;
    id: string;
    rule_name: string;
    updated_at: string;
    priority: number;
    preference_type: string;
    weekdays: {
        description: string;
    },
    start_time: string;
    end_time: string;
    is_all_day: false,
    max_frequency: number;
    frequency_period: string;
    start_date: string;
    end_date: string;
    occurrence_pattern: string;
    recurrence_interval: number,
    status: string;
    is_active: boolean;
}

export type PlannerCategoryRulesUpdateParams = {
    client_category: string;
    rule_name: string;
    priority: number;
    preference_type: string;
    weekdays: {
        description: string;
    },
    start_time: string;
    end_time: string;
    is_all_day: boolean;
    max_frequency: number;
    frequency_period: string;
    start_date: string;
    end_date: string;
    occurrence_pattern: string;
    recurrence_interval: number;
    status: string;
    is_active: boolean;
}

export type PlannerCategoryRulesUpdateResponse = {
    category_name: string;
    client_category: string;
    core_category_id: number;
    created_at: string;
    id: string;
    rule_name: string;
    updated_at: string;
    priority: number;
    preference_type: string;
    weekdays: {
        description: string;
    },
    start_time: string;
    end_time: string;
    is_all_day: boolean;
    max_frequency: number;
    frequency_period: string;
    start_date: string;
    end_date: string;
    occurrence_pattern: string;
    recurrence_interval: number;
    status: string;
    is_active: boolean;
}

export type PlannerCategoryRulesCreateParams = {
    client_category: string;
    rule_name: string;
    priority: number;
    preference_type: string;
    weekdays: {
        description: string;
    },
    start_time: string;
    end_time: string;
    is_all_day: boolean;
    max_frequency: number;
    frequency_period: string;
    start_date: string;
    end_date: string;
    occurrence_pattern: string;
    recurrence_interval: number;
    status: string;
    is_active: boolean;
}

export type PlannerCategoryRulesCreateResponse = {
    category_name: string;
    client_category: string;
    core_category_id: number;
    created_at: string;
    id: string;
    rule_name: string;
    updated_at: string;
    priority: number;
    preference_type: string;
    weekdays: {
        description: string;
    },
    start_time: string;
    end_time: string;
    is_all_day: boolean;
    max_frequency: number;
    frequency_period: string;
    start_date: string;
    end_date: string;
    occurrence_pattern: string;
    recurrence_interval: number;
    status: string;
    is_active: boolean;
}