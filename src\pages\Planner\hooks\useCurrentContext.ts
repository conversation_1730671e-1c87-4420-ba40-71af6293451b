import { useLocation, useParams } from 'react-router';
import type { Level } from '../utils/navigation';

export interface CurrentContext {
  level: Level;
  entityId?: string;
  entityName: string;
}

export const useCurrentContext = (): CurrentContext => {
  const location = useLocation();
  const params = useParams();

  const pathname = location.pathname;

  if (pathname.includes('/organization')) {
    return {
      level: 'organization',
      entityName: 'Organization'
    };
  } else if (pathname.includes('/location/')) {
    const locationId = params.locationId;
    return {
      level: 'location',
      entityId: locationId,
      entityName: `Location ${locationId}`
    };
  } else if (pathname.includes('/provider/')) {
    const providerId = params.providerId;
    return {
      level: 'provider',
      entityId: providerId,
      entityName: `Provider ${providerId}`
    };
  }

  return {
    level: 'organization',
    entityName: 'Organization'
  };
};