import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { TableCellNode, TableNode, TableRowNode } from "@lexical/table";
import { ListItemNode, ListNode } from "@lexical/list";
import { CodeHighlightNode, CodeNode } from "@lexical/code";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import { TRANSFORMERS } from "@lexical/markdown";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $getRoot, $createParagraphNode, $createTextNode } from "lexical";

import { useController, type Control } from "react-hook-form";

import ToolbarPlugin from "./ToolBarPlugin";
import { ImageNode } from "./nodes/ImageNode";
import { ImagePlugin } from "./plugins/ImagePlugin";

type RichTextEditorProps = {
	name: string;
	control: Control<any>;
	disableAutoFocus?: boolean;
};

function Placeholder() {
	return (
		<div className="absolute top-[1.125rem] left-[1.125rem] text-gray-400">
			Enter some text...
		</div>
	);
}

const editorConfig = {
	namespace: "MyEditor",
	onError(error: Error) {
		console.error("Lexical error:", error);
	},
	nodes: [
		HeadingNode,
		ListNode,
		ListItemNode,
		QuoteNode,
		CodeNode,
		CodeHighlightNode,
		TableNode,
		TableCellNode,
		TableRowNode,
		AutoLinkNode,
		LinkNode,
		ImageNode,
	],
	theme: {
		text: {
			bold: "font-bold",
			italic: "italic",
			underline: "underline",
			strikethrough: "line-through",
			underlineStrikethrough: "underline line-through",
		},
		paragraph: "my-bem",
	},
	editorState: null as any,
};

// This plugin handles the initial state loading
function InitialStatePlugin({
	initialContent,
}: {
	initialContent: string | null;
}) {
	const [editor] = useLexicalComposerContext();
	const hasInitializedRef = useRef(false);

	useEffect(() => {
		// Only initialize once
		if (!hasInitializedRef.current) {
			hasInitializedRef.current = true;

			// If we have content, try to parse and set it
			if (initialContent) {
				try {
					const parsedContent = JSON.parse(initialContent);
					const editorState = editor.parseEditorState(parsedContent);
					editor.setEditorState(editorState);
				} catch (e) {
					console.error("Failed to parse initial content:", e);
					editor.update(() => {
						const root = $getRoot();
						root.clear();
						const paragraph = $createParagraphNode();
						paragraph.append($createTextNode(initialContent));
						root.append(paragraph);
					});
				}
			} else {
				// If no initial content, ensure we have at least an empty paragraph
				// This helps with focus issues when starting with an empty editor
				editor.update(() => {
					const root = $getRoot();
					// Only add an empty paragraph if the root is empty
					if (root.getChildrenSize() === 0) {
						const paragraph = $createParagraphNode();
						root.append(paragraph);
					}
				});
			}
		}
	}, [editor, initialContent]);

	return null;
}

// This plugin handles changes to the editor content
function OnChangePlugin({
	onChange,
}: {
	onChange: (editorState: string) => void;
}) {
	const [editor] = useLexicalComposerContext();
	const [isFirstChange, setIsFirstChange] = useState(true);

	useEffect(() => {
		// Register for updates
		return editor.registerUpdateListener(({ editorState }) => {
			// Skip the first update which happens on initialization
			if (isFirstChange) {
				setIsFirstChange(false);
				return;
			}

			// Serialize the editor state to JSON and call the onChange handler
			editorState.read(() => {
				const jsonString = JSON.stringify(editorState.toJSON());
				onChange(jsonString);
			});
		});
	}, [editor, onChange, isFirstChange]);

	return null;
}

// This plugin helps maintain focus in the editor
function FocusTrackingPlugin() {
	const [editor] = useLexicalComposerContext();
	const editorRef = useRef<HTMLElement | null>(null);

	useEffect(() => {
		// Get the editor root element
		const editorElement = editor.getRootElement();
		if (editorElement) {
			editorRef.current = editorElement;
		}

		// Create a MutationObserver to watch for focus-related DOM changes
		const observer = new MutationObserver((mutations) => {
			// If we detect changes that might affect focus, ensure the editor is focused
			const contentEditableElement = editorElement?.querySelector(
				'[contenteditable="true"]'
			);
			if (
				contentEditableElement instanceof HTMLElement &&
				document.activeElement !== contentEditableElement
			) {
				// Only focus if the editor container or a child has focus
				if (editorElement?.contains(document.activeElement)) {
					contentEditableElement.focus();
				}
			}
		});

		// Start observing the editor element
		if (editorElement) {
			observer.observe(editorElement, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ["contenteditable", "class", "style"],
			});
		}

		return () => {
			observer.disconnect();
		};
	}, [editor]);

	return null;
}

export default function RichTextEditor({
	name,
	control,
	disableAutoFocus = false,
}: RichTextEditorProps) {
	const {
		field: { onChange, value },
	} = useController({ name, control });

	const editorRef = useRef<HTMLDivElement>(null);
	const [key, setKey] = useState(Date.now());
	const [isFocused, setIsFocused] = useState(false);

	// Handle changes to the editor content
	const handleEditorChange = useCallback(
		(editorState: string) => {
			onChange(editorState);
		},
		[onChange]
	);

	// Handle clicks on the editor container
	const handleContainerClick = useCallback((e: React.MouseEvent) => {
		// If clicking on the container but not on a child element, focus the editor
		if (e.target === e.currentTarget) {
			const editorElement = editorRef.current?.querySelector(
				'[contenteditable="true"]'
			);
			if (editorElement instanceof HTMLElement) {
				editorElement.focus();
				setIsFocused(true);
			}
		}
	}, []);

	// Handle focus events on the editor container
	const handleContainerFocus = useCallback((e: React.FocusEvent) => {
		// If the container receives focus, redirect it to the contentEditable element
		if (e.target === editorRef.current) {
			const editorElement = editorRef.current?.querySelector(
				'[contenteditable="true"]'
			);
			if (editorElement instanceof HTMLElement) {
				editorElement.focus();
				setIsFocused(true);
			}
		}
	}, []);

	// Track focus state
	const handleFocus = useCallback(() => {
		setIsFocused(true);
	}, []);

	const handleBlur = useCallback((e: React.FocusEvent) => {
		// Only blur if focus is moving outside the editor container
		if (!editorRef.current?.contains(e.relatedTarget as Node)) {
			setIsFocused(false);
		}
	}, []);

	// Create a custom config with proper theme settings
	const customConfig = {
		...editorConfig,
		theme: {
			...editorConfig.theme,
			// Add additional theme settings for proper formatting
			paragraph: "my-bem",
			heading: {
				h1: "text-2xl font-bold",
				h2: "text-xl font-bold",
				h3: "text-lg font-bold",
			},
			list: {
				ul: "list-disc list-inside",
				ol: "list-decimal list-inside",
			},
		},
	};

	return (
		<div
			ref={editorRef}
			className={`relative rounded-lg border ${isFocused ? "border-primary" : "border-gray-300"} bg-white`}
			onClick={handleContainerClick}
			onFocus={handleContainerFocus}
			tabIndex={-1}
		>
			<LexicalComposer initialConfig={customConfig} key={key}>
				<div className="editor-container">
					<ToolbarPlugin />
					<div className="relative">
						<RichTextPlugin
							contentEditable={
								<ContentEditable
									className="max-h-[200px] min-h-[150px] overflow-y-auto p-4 outline-none"
									onFocus={handleFocus}
									onBlur={handleBlur}
								/>
							}
							placeholder={<Placeholder />}
							ErrorBoundary={LexicalErrorBoundary}
						/>
						<InitialStatePlugin initialContent={value} />
						<OnChangePlugin onChange={handleEditorChange} />
						<FocusTrackingPlugin />
						<HistoryPlugin />
						{!disableAutoFocus && <AutoFocusPlugin />}
						<ListPlugin />
						<LinkPlugin />
						<ImagePlugin />
						<MarkdownShortcutPlugin transformers={TRANSFORMERS} />
					</div>
				</div>
			</LexicalComposer>
		</div>
	);
}
