import {
	Sheet,
	SheetContent,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface SurveyResponse {
	id: string;
	label: string;
	count: number;
	percentage: number;
}

interface SurveyQuestionSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	questionData?: {
		question: string;
		type: string;
		totalResponses: number;
		responses: SurveyResponse[];
	};
}

const defaultQuestionData = {
	question: "Which symptoms are you feeling?",
	type: "Custom Checkbox",
	totalResponses: 925,
	responses: [
		{
			id: "1",
			label: "Cough",
			count: 200,
			percentage: 72,
		},
		{
			id: "2",
			label: "Flu",
			count: 240,
			percentage: 14, 
		},
		{
			id: "3",
			label: "Itchy",
			count: 200,
			percentage: 60, 
		},
		{
			id: "4",
			label: "Nauseas",
			count: 80,
			percentage: 14, 
		},
		{
			id: "5",
			label: "Seasick",
			count: 32,
			percentage: 5, 
		},
	],
};

export function SurveyQuestionSheet({
	open,
	onOpenChange,
	questionData = defaultQuestionData,
}: SurveyQuestionSheetProps) {
	const handleClose = () => {
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full !max-w-[600px] overflow-y-auto p-4 [&>button]:hidden">
				<div className="inline-flex w-full flex-col items-center justify-start gap-3 bg-white">
					<div className="inline-flex w-full items-start justify-between">
						<div className="flex-1 text-base font-semibold leading-7 text-gray-900">
							{questionData.question}
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleClose}
							className="h-4 w-4 p-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>

					<div className="inline-flex w-full items-center justify-center gap-2.5">
						<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
							<div className="text-[10px] font-medium leading-3 text-gray-900">
								{questionData.type}
							</div>
						</div>
						<div className="flex-1 text-xs font-normal leading-none text-gray-500">
							{questionData.totalResponses} Responses
						</div>
					</div>

					<div className="h-px w-full border-t border-gray-200" />

					<div className="relative flex-1 self-stretch">
						<div className="absolute left-0 top-5 inline-flex w-full flex-col items-start justify-start gap-5">
							{questionData.responses.map((response) => (
								<div
									key={response.id}
									className="inline-flex w-full items-center justify-start gap-2"
								>
									<div className="w-24 text-xs font-medium leading-none text-gray-900">
										{response.label}
									</div>

									<div className="inline-flex h-3 flex-1 flex-col items-start justify-center gap-2.5">
										<div className="inline-flex flex-1 items-center justify-start gap-2.5 self-stretch rounded-full bg-gray-100 p-0.5">
											<div
												className="self-stretch rounded-full bg-[#005893]"
												style={{
													width: `${response.percentage}%`,
												}}
											/>
										</div>
									</div>

									<div className="h-5 w-10 text-right text-xs font-medium leading-none text-gray-900">
										{response.count}
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
