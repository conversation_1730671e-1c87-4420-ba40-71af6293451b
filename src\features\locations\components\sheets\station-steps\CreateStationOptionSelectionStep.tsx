import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronRight, UserPlus, UserPlus2, Building2 } from "lucide-react";

interface CreateStationOptionSelectionStepProps {
	onAddNewStation: () => void;
	onAddNewStationWithProvider: () => void;
	onAddFromExisting: () => void;
}

export function CreateStationOptionSelectionStep({
	onAddNewStation,
	onAddNewStationWithProvider,
	onAddFromExisting,
}: CreateStationOptionSelectionStepProps) {
	return (
		<div className="space-y-8.5">
			<div className="space-y-4">
				<Button
					onClick={onAddNewStation}
					variant="outline"
					className="h-[120px] w-full cursor-pointer items-start justify-between border-gray-200 p-4 hover:bg-gray-50"
				>
					<div className="flex w-full items-center justify-between gap-3">
						<div className="flex items-start gap-5">
							<div className="flex h-8.5 w-8.5 items-center justify-center rounded-md bg-gray-100">
								<Building2 className="h-5 w-5 text-gray-500" />
							</div>
							<div className="text-left">
								<div className="font-bold text-gray-900">
									Add New Station Only
								</div>
								<div className="text-sm text-wrap text-gray-500">
									Create a station without adding a service
									provider
								</div>
							</div>
						</div>
						<ChevronRight className="h-5 w-5 justify-self-end text-gray-400" />
					</div>
				</Button>

				<Button
					onClick={onAddNewStationWithProvider}
					variant="outline"
					className="h-[120px] w-full cursor-pointer items-start justify-between border-gray-200 p-4 hover:bg-gray-50"
				>
					<div className="flex w-full items-center justify-between gap-3">
						<div className="flex items-start gap-5">
							<div className="flex h-8.5 w-8.5 items-center justify-center rounded-md bg-gray-100">
								<UserPlus className="h-5 w-5 text-gray-500" />
							</div>
							<div className="text-left">
								<div className="font-bold text-gray-900">
									Add New Station with Service Provider
								</div>
								<div className="text-sm text-wrap text-gray-500">
									Create a station and add a service provider
								</div>
							</div>
						</div>
						<ChevronRight className="h-5 w-5 justify-self-end text-gray-400" />
					</div>
				</Button>

				<Button
					onClick={onAddFromExisting}
					variant="outline"
					className="h-[120px] w-full cursor-pointer items-start justify-between border-gray-200 p-4 hover:bg-gray-50"
				>
					<div className="flex w-full items-center justify-between gap-3">
						<div className="flex items-start gap-5">
							<div className="flex h-8.5 w-8.5 items-center justify-center rounded-md bg-gray-100">
								<UserPlus2 className="h-5 w-5 text-gray-500" />
							</div>
							<div className="text-left">
								<div className="font-bold text-gray-900">
									Add from Existing
								</div>
								<div className="text-sm text-wrap text-gray-500">
									If the service provider stations already
									exists on <br /> the platform, duplicate
									selected settings & calendar
								</div>
							</div>
						</div>
						<ChevronRight className="h-5 w-5 justify-self-end text-gray-400" />
					</div>
				</Button>
			</div>
		</div>
	);
}
