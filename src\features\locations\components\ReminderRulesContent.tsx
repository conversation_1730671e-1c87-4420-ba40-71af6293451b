import { useState } from "react";
import { Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { TimeInput } from "@/components/common/ExpirationSettings";

interface ReminderRule {
	id: string;
	value: string;
	unit: string;
	sms: boolean;
	email: boolean;
}

interface ReminderRules {
	scheduleVisibility: boolean;
	appointmentMethods: {
		inPerson: boolean;
		video: boolean;
		audio: boolean;
	};
	autoApprove: boolean;
	chat: boolean;
	chatMethod: "one-way" | "both-parties";
	scheduleBlock: boolean;
	scheduleBlockDate: Date | null;
	scheduleBlockWeeks: number;
	scheduleBlockSpecificDates: boolean;
	reminderRules: ReminderRule[];
}

export const ReminderRulesContent = () => {
	const [settings, setSettings] = useState<ReminderRules>({
		scheduleVisibility: true,
		appointmentMethods: {
			inPerson: true,
			video: false,
			audio: false,
		},
		autoApprove: true,
		chat: true,
		chatMethod: "both-parties",
		scheduleBlock: true,
		scheduleBlockDate: new Date("2025-08-04"),
		scheduleBlockWeeks: 4,
		scheduleBlockSpecificDates: true,
		reminderRules: [
			{
				id: "1",
				value: "2",
				unit: "weeks",
				sms: true,
				email: true,
			},
			{
				id: "2",
				value: "48",
				unit: "hours",
				sms: true,
				email: true,
			},
			{
				id: "3",
				value: "6",
				unit: "hours",
				sms: true,
				email: true,
			},
		],
	});

	const updateSettings = (updates: Partial<ReminderRules>) => {
		setSettings((prev) => ({ ...prev, ...updates }));
	};

	const updateAppointmentMethod = (
		method: keyof ReminderRules["appointmentMethods"],
		value: boolean
	) => {
		setSettings((prev) => ({
			...prev,
			appointmentMethods: {
				...prev.appointmentMethods,
				[method]: value,
			},
		}));
	};

	const updateReminderRule = (id: string, updates: Partial<ReminderRule>) => {
		setSettings((prev) => ({
			...prev,
			reminderRules: prev.reminderRules.map((rule) =>
				rule.id === id ? { ...rule, ...updates } : rule
			),
		}));
	};

	const addReminderRule = () => {
		const newRule: ReminderRule = {
			id: Date.now().toString(),
			value: "24",
			unit: "hours",
			sms: true,
			email: true,
		};
		setSettings((prev) => ({
			...prev,
			reminderRules: [...prev.reminderRules, newRule],
		}));
	};

	const removeReminderRule = (id: string) => {
		setSettings((prev) => ({
			...prev,
			reminderRules: prev.reminderRules.filter((rule) => rule.id !== id),
		}));
	};

	return (
		<div className="space-y-4">
			{/* Reminder Rules */}
			<div className="space-y-3 py-4">
				<div className="space-y-1">
					<div className="flex w-full flex-col gap-2">
						<div className="flex items-center justify-between">
							<h3 className="text-lg font-semibold">
								Set Custom Appointment Reminder Frequency
							</h3>
							<div className="flex items-center gap-1.5">
								<Switch
									checked={settings.scheduleVisibility}
									onCheckedChange={(checked) =>
										updateSettings({
											scheduleVisibility: checked,
										})
									}
								/>
								<span className="text-muted">
									{settings.scheduleVisibility ? "On" : "Off"}
								</span>
							</div>
						</div>
						<p className="text-sm text-gray-600">
							Determine how many and when appointment reminders
							are sent. If this is turned off, only one
							appointment reminder will be sent 24 hours before
							the appointment. All times are relative to the
							appointment start time.
						</p>
					</div>
				</div>
			</div>

			{/* Reminder Rules */}
			<div className="space-y-4 border-b border-gray-200 pb-4">
				<div className="space-y-0">
					{settings.reminderRules.map((rule, index) => (
						<div
							key={rule.id}
							className="flex items-center justify-between gap-12 border-b border-gray-200 px-2 py-3 last:border-b-0"
						>
							{/* Left section - Rule configuration */}
							<div className="flex items-center gap-3">
								<div className="flex min-w-40 items-center gap-3">
									<span className="text-sm font-semibold text-gray-700">
										{index + 1}
									</span>
									<TimeInput
										value={rule.value}
										onValueChange={(value) =>
											updateReminderRule(rule.id, {
												value: value,
											})
										}
										unit={rule.unit}
										onUnitChange={(unit) =>
											updateReminderRule(rule.id, {
												unit: unit,
											})
										}
										containerWidth="w-[145px]"
										units={[
											{ value: "weeks", label: "Weeks" },
											{ value: "hours", label: "Hours" },
										]}
										showAlternativeOption={false}
										inputType="number"
										min={1}
									/>
									<span className="text-sm font-medium text-gray-700">
										Before appointment
									</span>
								</div>
							</div>

							{/* Middle section - Send via options */}
							<div className="flex items-center gap-4">
								<span className="text-sm font-medium text-gray-600">
									Send via
								</span>
								<div className="flex items-center gap-3">
									<div className="flex items-center gap-2">
										<Checkbox
											id={`sms-${rule.id}`}
											checked={rule.sms}
											onCheckedChange={(checked) =>
												updateReminderRule(rule.id, {
													sms: checked as boolean,
												})
											}
											className="size-3"
										/>
										<Label
											htmlFor={`sms-${rule.id}`}
											className="text-sm font-medium"
										>
											SMS
										</Label>
									</div>
									<div className="flex items-center gap-2">
										<Checkbox
											id={`email-${rule.id}`}
											checked={rule.email}
											onCheckedChange={(checked) =>
												updateReminderRule(rule.id, {
													email: checked as boolean,
												})
											}
											className="size-3"
										/>
										<Label
											htmlFor={`email-${rule.id}`}
											className="text-sm font-medium"
										>
											Email
										</Label>
									</div>
								</div>
							</div>

							{/* Right section - Action buttons */}
							<div className="flex items-center gap-2">
								{settings.reminderRules.length > 1 && (
									<Button
										variant="ghost"
										size="icon"
										onClick={() =>
											removeReminderRule(rule.id)
										}
										className="flex h-auto w-auto cursor-pointer p-0"
									>
										<Trash2 className="size-4 text-gray-500" />
									</Button>
								)}
								<Button
									variant="ghost"
									size="icon"
									onClick={addReminderRule}
									className="flex h-auto w-auto cursor-pointer p-0"
								>
									<Plus className="size-4 text-gray-500" />
								</Button>
							</div>
						</div>
					))}
				</div>
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end gap-3">
				<Button variant="outline">Cancel</Button>
				<Button>Save</Button>
			</div>
		</div>
	);
};
