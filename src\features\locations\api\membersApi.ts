import { apiClient } from "@/lib/api/clients";
import type { Url } from "url";

// Member data interfaces
export interface MemberRole {
	role:
		| "BUSINESS_MANAGER"
		| "LOCATION_MANAGER"
		| "STATION_MANAGER"
		| "SERVICE_MANAGER"
		| "TEAM_MEMBER";
	station_ids?: number[];
	location_ids?: number[];
}

export interface CreateMemberRequest {
	first_name: string;
	last_name: string;
	email: string;
	phone_number: string;
	roles: MemberRole[];
}

export interface MemberStation {
	id: number;
	name: string;
}

export interface MemberLocation {
	id: number;
	name: string;
}

export interface MemberRoleResponse {
	role: string;
	stations?: MemberStation[];
	locations?: MemberLocation[];
}

export interface MemberData {
	id: number;
	name: string;
	email: string;
	phone_number: string;
	roles: MemberRoleResponse[];
	has_accepted: boolean;
}

export interface CreateMemberResponse {
	success: boolean;
	message: string;
	data: MemberData;
}

export interface GetMembersResponse {
	success: boolean;
	message: string;
	data: MemberData[];
}

// API functions
export const membersApi = {
	// Create a new team member
	async createMember(
		data: CreateMemberRequest,
		organizationId: string
	): Promise<CreateMemberResponse> {
		const response = await apiClient.post<CreateMemberResponse>(
			`/api/v1/team-members`,
			data,
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Get all team members
	async getMembers(
		organizationId: string,
		locationId?: number,
		stationId?: number,
		params: URLSearchParams | Url = new URLSearchParams()
	): Promise<GetMembersResponse> {
		const response = await apiClient.get<GetMembersResponse>(
			`/api/v1/team-members?${params.toString()}`,
			{
				headers: {
					"X-organizationId": organizationId,
					"X-locationId": locationId,
					"X-stationId": stationId,
				},
			}
		);
		return response.data;
	},

	// Get a specific team member
	async getMember(
		organizationId: string,
		memberId: number
	): Promise<{ success: boolean; data: MemberData }> {
		const response = await apiClient.get<{
			success: boolean;
			data: MemberData;
		}>(`/api/v1/team-members/${memberId}`, {
			headers: {
				"X-organizationId": organizationId,
			},
		});
		return response.data;
	},

	// Update a team member
	async updateMember(
		organizationId: string,
		memberId: number,
		data: Partial<CreateMemberRequest>
	): Promise<CreateMemberResponse> {
		const response = await apiClient.put<CreateMemberResponse>(
			`/api/v1/team-members/${memberId}`,
			data,
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Delete a team member
	async deleteMember(
		organizationId: string,
		memberId: number
	): Promise<{ success: boolean; message: string }> {
		const response = await apiClient.delete<{
			success: boolean;
			message: string;
		}>(`/api/v1/team-members/${memberId}`, {
			headers: {
				"X-organizationId": organizationId,
			},
		});
		return response.data;
	},
};
