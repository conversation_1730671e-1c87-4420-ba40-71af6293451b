// components/planner/OrganizationPreferencesContainer.tsx
import React, { useState } from 'react';
import { AddOrganizationPreference } from '../AddOrganizationPreference';
import { AddServicePreference } from './AddServicePreference';
import { ServicePreferencesContainer } from './ServicePreferencesContainer';
import { PreferencesManagement } from './PreferenceManagement';

type ViewState = 'list' | 'add-org' | 'edit-org' | 'service-flow' | 'add-service' | 'edit-service' | 'add-category' | 'edit-category';

interface ServicePreference {
  id: string;
  serviceName: string;
  preferenceTypes: string[];
  numberOfRules: number;
}

interface OrganizationPreferencesContainerProps {
  organizationName: string;
  onBack?: () => void;
}

export const OrganizationPreferencesContainer: React.FC<OrganizationPreferencesContainerProps> = ({
  organizationName,
  onBack
}) => {
  const [currentView, setCurrentView] = useState<ViewState>('list');
  const [editingPreferenceId, setEditingPreferenceId] = useState<string | null>(null);
  const [editingPreferenceType, setEditingPreferenceType] = useState<'organization' | 'service' | 'category'>('organization');
  const [savedFormData, setSavedFormData] = useState<any>(null);

  // Handle Organization Preferences
  const handleAddOrganizationPreference = () => {
    setEditingPreferenceId(null);
    setEditingPreferenceType('organization');
    setSavedFormData(null);
    setCurrentView('add-org');
  };

  // Handle Service Preferences
  const handleAddServicePreference = () => {
    setEditingPreferenceId(null);
    setEditingPreferenceType('service');
    setSavedFormData(null);
    setCurrentView('add-service');
  };

  // Handle Category Preferences (placeholder for now)
  const handleAddCategoryPreference = () => {
    console.log('Add category preference - to be implemented');
    // setCurrentView('add-category');
  };

  // Handle editing any type of preference
  const handleEditPreference = (preferenceId: string, type: 'organization' | 'service' | 'category') => {
    setEditingPreferenceId(preferenceId);
    setEditingPreferenceType(type);
    
    // Load preference data based on type
    // For now, we'll use null and let the form handle it
    setSavedFormData(null);
    
    switch (type) {
      case 'organization':
        setCurrentView('edit-org');
        break;
      case 'service':
        setCurrentView('edit-service');
        break;
      case 'category':
        setCurrentView('edit-category');
        break;
    }
  };

  // Handle viewing service rules
  const handleViewServiceRules = (service: ServicePreference) => {
    // Pass the service data to the service flow
    setSavedFormData(service);
    setCurrentView('service-flow');
  };

  const handleSaveOrganizationPreference = (formData: any) => {
    // Save form data before navigating away
    setSavedFormData(formData);
    
    // Handle saving the preference
    console.log('Saving organization preference...', formData);
    // After successful save, go back to list
    setCurrentView('list');
    setEditingPreferenceId(null);
    setSavedFormData(null);
  };

  const handleSaveServicePreference = (formData: any) => {
    // Save form data before navigating away
    setSavedFormData(formData);
    
    // Handle saving the service preference (with conflict detection)
    console.log('Saving service preference...', formData);
    console.log('Checking for conflicts...');
    // This should go through the conflict detection flow
    setCurrentView('service-flow');
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setEditingPreferenceId(null);
    setEditingPreferenceType('organization');
    setSavedFormData(null);
  };

  const handleMainBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Render based on current view
  switch (currentView) {
    case 'add-org':
      return (
        <AddOrganizationPreference
          onBack={handleBackToList}
          onSave={handleSaveOrganizationPreference}
          initialData={savedFormData}
        />
      );
      
    case 'edit-org':
      return (
        <AddOrganizationPreference
          onBack={handleBackToList}
          onSave={handleSaveOrganizationPreference}
          editingId={editingPreferenceId}
          initialData={savedFormData}
        />
      );

    case 'add-service':
      return (
        <AddServicePreference
          onBack={handleBackToList}
          onSave={handleSaveServicePreference}
        />
      );
      
    case 'edit-service':
      return (
        <AddServicePreference
          onBack={handleBackToList}
          onSave={handleSaveServicePreference}
        />
      );

    case 'service-flow':
      return (
        <ServicePreferencesContainer
          onBack={handleBackToList}
          initialServiceData={savedFormData}
          startWithAddForm={editingPreferenceType === 'service' && !savedFormData}
        />
      );

    case 'add-category':
      // Placeholder for category preference form
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-semibold mb-4">Add Category Preference</h1>
            <p className="text-gray-600 mb-4">Category preference form to be implemented</p>
            <button 
              onClick={handleBackToList}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Back to List
            </button>
          </div>
        </div>
      );

    case 'edit-category':
      // Placeholder for editing category preference
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-semibold mb-4">Edit Category Preference</h1>
            <p className="text-gray-600 mb-4">Category preference edit form to be implemented</p>
            <button 
              onClick={handleBackToList}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Back to List
            </button>
          </div>
        </div>
      );
      
    case 'list':
    default:
      return (
        <PreferencesManagement
          level="organization"
          entityName={organizationName}
          onAddOrganizationPreference={handleAddOrganizationPreference}
          onAddServicePreference={handleAddServicePreference}
          onAddCategoryPreference={handleAddCategoryPreference}
          onEditPreference={handleEditPreference}
          onViewServiceRules={handleViewServiceRules}
          onBack={handleMainBack}
        />
      );
  }
};