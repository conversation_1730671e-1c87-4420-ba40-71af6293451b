import { useState } from "react";
import { User<PERSON><PERSON>, X } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	She<PERSON>,
	She<PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>ead<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { MultiAsyncSelect } from "@/components/common/MultiAsyncSelect";
import { ExpirationSettings } from "@/components/common/ExpirationSettings";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

export interface SendFormLinkSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function SendFormLinkSheet({
	open,
	onOpenChange,
}: SendFormLinkSheetProps) {
	const [activeTab, setActiveTab] = useState("organization");
	const [selectedPatients, setSelectedPatients] = useState<string[]>([
		"dr-samuel-johnson",
		"dr-alice-benson",
	]);
	const [selectedServices, setSelectedServices] = useState<string[]>(["all"]);
	const [message, setMessage] = useState("");
	const [sendViaSMS, setSendViaSMS] = useState(true);
	const [sendViaEmail, setSendViaEmail] = useState(true);
	const [expirationDays, setExpirationDays] = useState("5");
	const [expirationUnit, setExpirationUnit] = useState("days");
	const [useSpecificDate, setUseSpecificDate] = useState(false);
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"all",
	]);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([
		"all",
	]);
	const [showAddNewPatient, setShowAddNewPatient] = useState(false);

	// Mock data for patients
	const patientOptions = [
		{ value: "dr-samuel-johnson", label: "Dr. Samuel Johnson" },
		{ value: "dr-alice-benson", label: "Dr. Alice Benson" },
		{ value: "dr-john-doe", label: "Dr. John Doe" },
		{ value: "dr-jane-smith", label: "Dr. Jane Smith" },
	];

	// Mock data for services
	const serviceOptions = [
		{ value: "all", label: "All" },
		{ value: "consultation", label: "Consultation" },
		{ value: "blood-test", label: "Blood Test" },
		{ value: "imaging", label: "Imaging" },
	];

	const tabItems = [
		{ value: "organization", label: "Organization" },
		{ value: "location", label: "Location" },
		{ value: "provider", label: "Provider" },
		{ value: "custom", label: "Custom" },
		{ value: "waitlist-settings", label: "Waitlist Settings" },
	];

	const locationOptions = [
		{ value: "all", label: "All" },
		{ value: "location-1", label: "Location 1" },
		{ value: "location-2", label: "Location 2" },
		{ value: "location-3", label: "Location 3" },
	];

	const providerOptions = [
		{ value: "all", label: "All" },
		{ value: "provider-1", label: "Provider 1" },
		{ value: "provider-2", label: "Provider 2" },
		{ value: "provider-3", label: "Provider 3" },
	];
	const handleSendLink = () => {
		console.log("Sending booking link...", {
			tab: activeTab,
			patients: selectedPatients,
			services: selectedServices,
			message,
			sendViaSMS,
			sendViaEmail,
			expirationDays,
			useSpecificDate,
		});
		onOpenChange(false);
	};

	const handleNewPatient = () => {
		console.log("Adding new patient...");
		setShowAddNewPatient(true);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] flex w-[520px] flex-col p-0 sm:max-w-[520px]">
				<SheetHeader className="p-6">
					<SheetTitle className="text-xl font-semibold tracking-tight text-gray-800">
						Send Booking Link
					</SheetTitle>
				</SheetHeader>

				<div className="flex-1 overflow-y-auto p-6">
					<div className="space-y-3.5">
						<Tabs
							items={tabItems}
							value={activeTab}
							onValueChange={setActiveTab}
							className="w-full"
							triggerClassName="mx-0 text-xs py-2"
						>
							{/* Organization Tab */}
							<TabsContent
								value="organization"
								className="mt-1 space-y-3.5"
							>
								{/* Patients Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Patients
									</label>
									<MultiAsyncSelect
										options={patientOptions}
										onValueChange={setSelectedPatients}
										defaultValue={selectedPatients}
										placeholder="Select patients"
										maxCount={2}
										className="w-full"
									/>
									<Button
										variant="ghost"
										size="sm"
										onClick={handleNewPatient}
										className="text-primary hover:text-primary/80 h-9 justify-start px-0"
									>
										<UserPlus className="mr-2 h-4 w-4" />
										New Patient
									</Button>
								</div>

								{/* Services Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Services
									</label>
									<MultiAsyncSelect
										options={serviceOptions}
										onValueChange={setSelectedServices}
										defaultValue={selectedServices}
										placeholder="Select services"
										className="w-full"
									/>
								</div>

								{/* Message Section */}
								<div className="mt-5 space-y-2">
									<label className="text-sm font-medium tracking-tight text-gray-800">
										Message
									</label>
									<Textarea
										value={message}
										onChange={(e) =>
											setMessage(e.target.value)
										}
										placeholder=""
										className="min-h-[114px] resize-none"
									/>
								</div>

								{/* Send Via Section */}
								<div className="mt-5 space-y-4">
									<div className="flex items-center gap-6">
										<span className="text-sm font-medium tracking-tight text-[#3b5566]">
											Send via
										</span>
										<div className="flex items-center gap-6">
											<div className="flex items-center gap-3">
												<Checkbox
													id="sms"
													checked={sendViaSMS}
													onCheckedChange={(
														checked
													) =>
														setSendViaSMS(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="sms"
													className="text-sm font-medium text-zinc-900"
												>
													SMS
												</label>
											</div>
											<div className="flex items-center gap-3">
												<Checkbox
													id="email"
													checked={sendViaEmail}
													onCheckedChange={(
														checked
													) =>
														setSendViaEmail(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="email"
													className="text-sm font-medium text-zinc-900"
												>
													Email
												</label>
											</div>
										</div>
									</div>

									{/* Expiration Section */}
									<div className="space-y-4">
										<div className="flex items-start justify-between">
											<span className="mt-1.5 text-base font-medium tracking-tight text-gray-800">
												Booking Link will Expire in
											</span>
											<div className="flex w-[199px] flex-col items-start gap-3">
												<div className="flex items-center gap-2 rounded-lg border border-zinc-200">
													<Input
														type="number"
														value={expirationDays}
														onChange={(e) =>
															setExpirationDays(
																e.target.value
															)
														}
														className="h-9 border-none text-xs hover:border-none focus-visible:border-none focus-visible:ring-0"
													/>
													<Select defaultValue="days">
														<SelectTrigger className="h-9 w-auto min-w-[80px] border-none text-xs hover:border-none focus-visible:border-none">
															<SelectValue />
														</SelectTrigger>
														<SelectContent>
															<SelectItem value="days">
																Days
															</SelectItem>
															<SelectItem value="hours">
																Hours
															</SelectItem>
															<SelectItem value="weeks">
																Weeks
															</SelectItem>
														</SelectContent>
													</Select>
												</div>
												<div className="flex items-center gap-2">
													<Checkbox
														id="specific-date"
														checked={
															useSpecificDate
														}
														onCheckedChange={(
															checked
														) =>
															setUseSpecificDate(
																checked ===
																	"indeterminate"
																	? false
																	: checked
															)
														}
													/>
													<label
														htmlFor="specific-date"
														className="text-sm font-medium tracking-tight text-gray-800"
													>
														Select Specific Date
													</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</TabsContent>

							{/* Location Tab */}
							<TabsContent
								value="location"
								className="mt-1 space-y-3.5"
							>
								{/* Patients Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Patients
									</label>
									<MultiAsyncSelect
										options={patientOptions}
										onValueChange={setSelectedPatients}
										defaultValue={selectedPatients}
										placeholder="Select patients"
										maxCount={2}
										className="w-full"
									/>
									<Button
										variant="ghost"
										size="sm"
										onClick={handleNewPatient}
										className="text-primary hover:text-primary/80 h-9 justify-start px-0"
									>
										<UserPlus className="mr-2 h-4 w-4" />
										New Patient
									</Button>
								</div>

								{/* Locations Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Locations
									</label>
									<MultiAsyncSelect
										options={locationOptions}
										onValueChange={setSelectedLocations}
										defaultValue={selectedLocations}
										placeholder="Select locations"
										className="w-full"
									/>
								</div>

								{/* Services Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Services
									</label>
									<MultiAsyncSelect
										options={serviceOptions}
										onValueChange={setSelectedServices}
										defaultValue={selectedServices}
										placeholder="Select services"
										className="w-full"
									/>
								</div>

								{/* Message Section */}
								<div className="mt-5 space-y-2">
									<label className="text-sm font-medium tracking-tight text-gray-800">
										Message
									</label>
									<Textarea
										value={message}
										onChange={(e) =>
											setMessage(e.target.value)
										}
										placeholder=""
										className="min-h-[114px] resize-none"
									/>
								</div>

								{/* Send Via Section */}
								<div className="mt-5 space-y-4">
									<div className="flex items-center gap-6">
										<span className="text-sm font-medium tracking-tight text-[#3b5566]">
											Send via
										</span>
										<div className="flex items-center gap-6">
											<div className="flex items-center gap-3">
												<Checkbox
													id="sms"
													checked={sendViaSMS}
													onCheckedChange={(
														checked
													) =>
														setSendViaSMS(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="sms"
													className="text-sm font-medium text-zinc-900"
												>
													SMS
												</label>
											</div>
											<div className="flex items-center gap-3">
												<Checkbox
													id="email"
													checked={sendViaEmail}
													onCheckedChange={(
														checked
													) =>
														setSendViaEmail(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="email"
													className="text-sm font-medium text-zinc-900"
												>
													Email
												</label>
											</div>
										</div>
									</div>

									{/* Expiration Section */}
									<ExpirationSettings
										value={expirationDays}
										onValueChange={setExpirationDays}
										unit={expirationUnit}
										onUnitChange={setExpirationUnit}
										useSpecificDate={useSpecificDate}
										onUseSpecificDateChange={
											setUseSpecificDate
										}
									/>
								</div>
							</TabsContent>

							{/* Provider Tab */}
							<TabsContent
								value="provider"
								className="mt-1 space-y-3.5"
							>
								{/* Patients Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Patients
									</label>
									<MultiAsyncSelect
										options={patientOptions}
										onValueChange={setSelectedPatients}
										defaultValue={selectedPatients}
										placeholder="Select patients"
										maxCount={2}
										className="w-full"
									/>
									<Button
										variant="ghost"
										size="sm"
										onClick={handleNewPatient}
										className="text-primary hover:text-primary/80 h-9 justify-start px-0"
									>
										<UserPlus className="mr-2 h-4 w-4" />
										New Patient
									</Button>
								</div>

								{/* Providers Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Providers
									</label>
									<MultiAsyncSelect
										options={providerOptions}
										onValueChange={setSelectedProviders}
										defaultValue={selectedProviders}
										placeholder="Select providers"
										className="w-full"
									/>
								</div>

								{/* Services Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Services
									</label>
									<MultiAsyncSelect
										options={serviceOptions}
										onValueChange={setSelectedServices}
										defaultValue={selectedServices}
										placeholder="Select services"
										className="w-full"
									/>
								</div>

								{/* Message Section */}
								<div className="mt-5 space-y-2">
									<label className="text-sm font-medium tracking-tight text-gray-800">
										Message
									</label>
									<Textarea
										value={message}
										onChange={(e) =>
											setMessage(e.target.value)
										}
										placeholder=""
										className="min-h-[114px] resize-none"
									/>
								</div>

								{/* Send Via Section */}
								<div className="mt-5 space-y-4">
									<div className="flex items-center gap-6">
										<span className="text-sm font-medium tracking-tight text-[#3b5566]">
											Send via
										</span>
										<div className="flex items-center gap-6">
											<div className="flex items-center gap-3">
												<Checkbox
													id="sms"
													checked={sendViaSMS}
													onCheckedChange={(
														checked
													) =>
														setSendViaSMS(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="sms"
													className="text-sm font-medium text-zinc-900"
												>
													SMS
												</label>
											</div>
											<div className="flex items-center gap-3">
												<Checkbox
													id="email"
													checked={sendViaEmail}
													onCheckedChange={(
														checked
													) =>
														setSendViaEmail(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="email"
													className="text-sm font-medium text-zinc-900"
												>
													Email
												</label>
											</div>
										</div>
									</div>

									{/* Expiration Section */}
									<ExpirationSettings
										value={expirationDays}
										onValueChange={setExpirationDays}
										unit={expirationUnit}
										onUnitChange={setExpirationUnit}
										useSpecificDate={useSpecificDate}
										onUseSpecificDateChange={
											setUseSpecificDate
										}
									/>
								</div>
							</TabsContent>

							{/* Custom Tab */}
							<TabsContent
								value="custom"
								className="mt-1 space-y-3.5"
							>
								{/* Patients Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Patients
									</label>
									<MultiAsyncSelect
										options={patientOptions}
										onValueChange={setSelectedPatients}
										defaultValue={selectedPatients}
										placeholder="Select patients"
										maxCount={2}
										className="w-full"
									/>
									{!showAddNewPatient && (
										<Button
											variant="ghost"
											size="sm"
											onClick={handleNewPatient}
											className="text-primary hover:text-primary/80 h-9 justify-start px-0"
										>
											<UserPlus className="mr-2 h-4 w-4" />
											New Patient
										</Button>
									)}
								</div>

								{/* Add New Patient Section */}
								{showAddNewPatient && (
									<div className="space-y-2 border-t p-3">
										<div className="flex items-center justify-between gap-2">
											<h2 className="text-sm font-medium tracking-tight text-gray-800">
												New Patient
											</h2>
											<Button
												variant="ghost"
												size="icon"
												onClick={() =>
													setShowAddNewPatient(false)
												}
											>
												<X className="h-4 w-4" />
											</Button>
										</div>
										<div className="mt-3 grid grid-cols-2 gap-3">
											<div className="flex flex-col gap-1">
												<label className="text-xs font-medium text-zinc-900">
													First Name
												</label>
												<Input
													type="text"
													placeholder="First Name"
													className="h-9 text-xs"
												/>
											</div>
											<div className="flex flex-col gap-1">
												<label className="text-xs font-medium text-zinc-900">
													Last Name
												</label>
												<Input
													type="text"
													placeholder="Last Name"
													className="h-9 text-xs"
												/>
											</div>
											<div className="flex flex-col gap-1">
												<label className="text-xs font-medium text-zinc-900">
													Email
												</label>
												<Input
													type="email"
													placeholder="Email"
													className="h-9 text-xs"
												/>
											</div>
											<div className="flex flex-col gap-1">
												<label className="text-xs font-medium text-zinc-900">
													Phone
												</label>
												<Input
													type="text"
													placeholder="Phone"
													className="h-9 text-xs"
												/>
											</div>
										</div>
									</div>
								)}

								{/* Locations Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Locations
									</label>
									<MultiAsyncSelect
										options={locationOptions}
										onValueChange={setSelectedLocations}
										defaultValue={selectedLocations}
										placeholder="Select locations"
										className="w-full"
									/>
								</div>

								{/* Providers Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Providers
									</label>
									<MultiAsyncSelect
										options={providerOptions}
										onValueChange={setSelectedProviders}
										defaultValue={selectedProviders}
										placeholder="Select providers"
										className="w-full"
									/>
								</div>

								{/* Services Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Services
									</label>
									<MultiAsyncSelect
										options={serviceOptions}
										onValueChange={setSelectedServices}
										defaultValue={selectedServices}
										placeholder="Select services"
										className="w-full"
									/>
								</div>

								{/* Message Section */}
								<div className="mt-5 space-y-2">
									<label className="text-sm font-medium tracking-tight text-gray-800">
										Message
									</label>
									<Textarea
										value={message}
										onChange={(e) =>
											setMessage(e.target.value)
										}
										placeholder=""
										className="min-h-[114px] resize-none"
									/>
								</div>

								{/* Send Via Section */}
								<div className="mt-5 space-y-4">
									<div className="flex items-center gap-6">
										<span className="text-sm font-medium tracking-tight text-[#3b5566]">
											Send via
										</span>
										<div className="flex items-center gap-6">
											<div className="flex items-center gap-3">
												<Checkbox
													id="sms"
													checked={sendViaSMS}
													onCheckedChange={(
														checked
													) =>
														setSendViaSMS(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="sms"
													className="text-sm font-medium text-zinc-900"
												>
													SMS
												</label>
											</div>
											<div className="flex items-center gap-3">
												<Checkbox
													id="email"
													checked={sendViaEmail}
													onCheckedChange={(
														checked
													) =>
														setSendViaEmail(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="email"
													className="text-sm font-medium text-zinc-900"
												>
													Email
												</label>
											</div>
										</div>
									</div>

									{/* Expiration Section */}
									<ExpirationSettings
										value={expirationDays}
										onValueChange={setExpirationDays}
										unit={expirationUnit}
										onUnitChange={setExpirationUnit}
										useSpecificDate={useSpecificDate}
										onUseSpecificDateChange={
											setUseSpecificDate
										}
									/>
								</div>
							</TabsContent>

							{/* Waitlist Settings Tab */}
							<TabsContent
								value="waitlist-settings"
								className="mt-1 space-y-3.5"
							>
								{/* Patients Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Patients
									</label>
									<MultiAsyncSelect
										options={patientOptions}
										onValueChange={setSelectedPatients}
										defaultValue={selectedPatients}
										placeholder="Select patients"
										maxCount={2}
										className="w-full"
									/>
									<Button
										variant="ghost"
										size="sm"
										onClick={handleNewPatient}
										className="text-primary hover:text-primary/80 h-9 justify-start px-0"
									>
										<UserPlus className="mr-2 h-4 w-4" />
										New Patient
									</Button>
								</div>

								{/* Locations Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Locations
									</label>
									<MultiAsyncSelect
										options={locationOptions}
										onValueChange={setSelectedLocations}
										defaultValue={selectedLocations}
										placeholder="Select locations"
										className="w-full"
									/>
								</div>

								{/* Providers Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Providers
									</label>
									<MultiAsyncSelect
										options={providerOptions}
										onValueChange={setSelectedProviders}
										defaultValue={selectedProviders}
										placeholder="Select providers"
										className="w-full"
									/>
								</div>

								{/* Services Section */}
								<div className="space-y-2">
									<label className="text-xs font-medium text-zinc-900">
										Services
									</label>
									<MultiAsyncSelect
										options={serviceOptions}
										onValueChange={setSelectedServices}
										defaultValue={selectedServices}
										placeholder="Select services"
										className="w-full"
									/>
								</div>

								{/* Message Section */}
								<div className="mt-5 space-y-2">
									<label className="text-sm font-medium tracking-tight text-gray-800">
										Message
									</label>
									<Textarea
										value={message}
										onChange={(e) =>
											setMessage(e.target.value)
										}
										placeholder=""
										className="min-h-[114px] resize-none"
									/>
								</div>

								{/* Send Via Section */}
								<div className="mt-5 space-y-4">
									<div className="flex items-center gap-6">
										<span className="text-sm font-medium tracking-tight text-[#3b5566]">
											Send via
										</span>
										<div className="flex items-center gap-6">
											<div className="flex items-center gap-3">
												<Checkbox
													id="sms"
													checked={sendViaSMS}
													onCheckedChange={(
														checked
													) =>
														setSendViaSMS(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="sms"
													className="text-sm font-medium text-zinc-900"
												>
													SMS
												</label>
											</div>
											<div className="flex items-center gap-3">
												<Checkbox
													id="email"
													checked={sendViaEmail}
													onCheckedChange={(
														checked
													) =>
														setSendViaEmail(
															checked ===
																"indeterminate"
																? false
																: checked
														)
													}
													className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
												/>
												<label
													htmlFor="email"
													className="text-sm font-medium text-zinc-900"
												>
													Email
												</label>
											</div>
										</div>
									</div>

									{/* Expiration Section */}
									<div className="space-y-4">
										<div className="flex items-start justify-between">
											<span className="mt-1.5 text-base font-medium tracking-tight text-gray-800">
												Booking Link will Expire in
											</span>
											<div className="flex w-[199px] flex-col items-start gap-3">
												<div className="flex items-center gap-2 rounded-lg border border-zinc-200">
													<Input
														type="number"
														value={expirationDays}
														onChange={(e) =>
															setExpirationDays(
																e.target.value
															)
														}
														className="h-9 border-none text-xs hover:border-none focus-visible:border-none focus-visible:ring-0"
													/>
													<Select defaultValue="days">
														<SelectTrigger className="h-9 w-auto min-w-[80px] border-none text-xs hover:border-none focus-visible:border-none">
															<SelectValue />
														</SelectTrigger>
														<SelectContent>
															<SelectItem value="days">
																Days
															</SelectItem>
															<SelectItem value="hours">
																Hours
															</SelectItem>
															<SelectItem value="weeks">
																Weeks
															</SelectItem>
														</SelectContent>
													</Select>
												</div>
												<div className="flex items-center gap-2">
													<Checkbox
														id="specific-date"
														checked={
															useSpecificDate
														}
														onCheckedChange={(
															checked
														) =>
															setUseSpecificDate(
																checked ===
																	"indeterminate"
																	? false
																	: checked
															)
														}
													/>
													<label
														htmlFor="specific-date"
														className="text-sm font-medium tracking-tight text-gray-800"
													>
														Select Specific Date
													</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</TabsContent>
						</Tabs>
					</div>
				</div>

				{/* Sheet Actions */}
				<SheetFooter className="p-6">
					<div className="flex w-full cursor-pointer justify-end gap-3">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
							className="px-5 py-3"
						>
							Cancel
						</Button>
						<Button
							onClick={handleSendLink}
							className="bg-primary hover:bg-primary/90 cursor-pointer px-8 py-3"
						>
							Send Link
						</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
