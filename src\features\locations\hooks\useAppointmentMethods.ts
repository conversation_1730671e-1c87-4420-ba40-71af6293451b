import { useQuery } from "@tanstack/react-query";
import { appointmentMethodsApi } from "../api/appointmentMethodsApi";
import type { AppointmentMethod } from "../types";

export const useAppointmentMethods = (organizationId: number | null) => {
	return useQuery<AppointmentMethod[], Error>({
		queryKey: ["appointment-methods", organizationId],
		queryFn: () => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return appointmentMethodsApi.getAppointmentMethods(organizationId);
		},
		enabled: !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		retry: 1,
	});
};
