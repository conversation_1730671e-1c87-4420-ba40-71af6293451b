import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { InputPhone } from "./InputPhone";
import { phoneSchema } from "./utils";
import React from "react";

const meta = {
	title: "Components/InputPhone",
	component: InputPhone,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"A unified phone input component with international support, country detection, and form integration.",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		variant: {
			control: "select",
			options: ["default", "with-country-dropdown", "form"],
		},
		format: {
			control: "select",
			options: ["international", "national", "e164"],
		},
		defaultCountry: {
			control: "text",
		},
		showFlag: {
			control: "boolean",
		},
	},
} satisfies Meta<typeof InputPhone>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		placeholder: "Enter phone number",
		defaultCountry: "US",
	},
};

export const AllVariants: Story = {
	render: () => {
		const [phone1, setPhone1] = React.useState("");
		const [phone2, setPhone2] = React.useState("");
		const [selectedCountry, setSelectedCountry] = React.useState<any>(null);

		return (
			<div className="grid max-w-2xl gap-8 p-6">
				<div className="space-y-4">
					<h3 className="text-lg font-semibold">
						Default Phone Input
					</h3>
					<p className="text-muted-foreground text-sm">
						Basic phone input with automatic country detection and
						flag display
					</p>
					<InputPhone
						value={phone1}
						onChange={setPhone1}
						placeholder="Enter phone number"
						defaultCountry="US"
					/>
					{phone1 && (
						<p className="text-muted-foreground text-sm">
							Value: {phone1}
						</p>
					)}
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-semibold">
						With Country Dropdown
					</h3>
					<p className="text-muted-foreground text-sm">
						Phone input with integrated country selector dropdown
					</p>
					<InputPhone
						variant="with-country-dropdown"
						value={phone2}
						onChange={setPhone2}
						onCountryChange={setSelectedCountry}
						placeholder="Enter phone number"
						defaultCountry="GB"
					/>
					{phone2 && (
						<div className="text-muted-foreground space-y-1 text-sm">
							<p>Phone: {phone2}</p>
							{selectedCountry && (
								<p>
									Country: {selectedCountry.name} (
									{selectedCountry.alpha2})
								</p>
							)}
						</div>
					)}
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Different Formats</h3>
					<div className="grid gap-4">
						<div>
							<label className="text-sm font-medium">
								International Format
							</label>
							<InputPhone
								format="international"
								defaultCountry="DE"
								placeholder="+49 xxx xxx xxxx"
							/>
						</div>
						<div>
							<label className="text-sm font-medium">
								National Format
							</label>
							<InputPhone
								format="national"
								defaultCountry="FR"
								placeholder="0x xx xx xx xx"
							/>
						</div>
						<div>
							<label className="text-sm font-medium">
								E.164 Format
							</label>
							<InputPhone
								format="e164"
								defaultCountry="CA"
								placeholder="+1xxxxxxxxxx"
							/>
						</div>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Without Flag</h3>
					<p className="text-muted-foreground text-sm">
						Phone input without flag display using showFlag prop
					</p>
					<InputPhone
						showFlag={false}
						placeholder="Enter phone number"
						defaultCountry="JP"
					/>
				</div>
			</div>
		);
	},
};

// Form Integration Example
const FormExample = () => {
	const form = useForm({
		defaultValues: {
			phone: "",
			workPhone: "",
			emergencyContact: "",
		},
	});

	const onSubmit = (data: any) => {
		console.log("Form submitted:", data);
		alert("Form submitted! Check console for data.");
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="max-w-md space-y-6"
			>
				<InputPhone
					variant="form"
					name="phone"
					control={form.control}
					label="Phone Number"
					description="Your primary phone number for contact"
					defaultCountry="US"
					rules={{
						required: "Phone number is required",
						validate: (value) => {
							try {
								phoneSchema.parse(value);
								return true;
							} catch {
								return "Please enter a valid phone number";
							}
						},
					}}
					placeholder="Enter your phone number"
				/>

				<InputPhone
					variant="form"
					name="workPhone"
					control={form.control}
					label="Work Phone"
					description="Your work phone number (optional)"
					defaultCountry="US"
					showFlag={false}
					format="national"
					placeholder="Enter work phone"
				/>

				<InputPhone
					variant="form"
					name="emergencyContact"
					control={form.control}
					label="Emergency Contact"
					description="Emergency contact phone number"
					defaultCountry="US"
					rules={{
						required: "Emergency contact is required",
						validate: (value) => {
							try {
								phoneSchema.parse(value);
								return true;
							} catch {
								return "Please enter a valid phone number";
							}
						},
					}}
					placeholder="Enter emergency contact"
				/>

				<Button type="submit" className="w-full">
					Submit
				</Button>
			</form>
		</Form>
	);
};

export const FormIntegration: Story = {
	render: () => <FormExample />,
	parameters: {
		docs: {
			description: {
				story: "Example of using InputPhone with React Hook Form, including validation using libphonenumber-js.",
			},
		},
	},
};

export const CountrySelection: Story = {
	render: () => {
		const [phone, setPhone] = React.useState("+1");
		const [country, setCountry] = React.useState<any>(null);

		return (
			<div className="grid max-w-lg gap-6 p-6">
				<div className="space-y-4">
					<h3 className="text-lg font-semibold">
						Interactive Country Selection
					</h3>

					<InputPhone
						variant="with-country-dropdown"
						value={phone}
						onChange={setPhone}
						onCountryChange={setCountry}
						defaultCountry="US"
						placeholder="Select country and enter number"
					/>

					{country && (
						<div className="bg-foreground-muted space-y-2 rounded-md p-4">
							<h4 className="font-medium">Selected Country:</h4>
							<div className="space-y-1 text-sm">
								<p>
									<strong>Name:</strong> {country.name}
								</p>
								<p>
									<strong>Code:</strong> {country.alpha2}
								</p>
								<p>
									<strong>Calling Code:</strong>{" "}
									{country.countryCallingCodes?.[0]}
								</p>
								<p>
									<strong>Currency:</strong>{" "}
									{country.currencies?.join(", ")}
								</p>
							</div>
						</div>
					)}

					<div className="space-y-2">
						<h4 className="font-medium">Try these numbers:</h4>
						<div className="space-y-1 text-sm">
							<button
								className="block text-blue-600 hover:underline"
								onClick={() => setPhone("****** 123 4567")}
							>
								US: ****** 123 4567
							</button>
							<button
								className="block text-blue-600 hover:underline"
								onClick={() => setPhone("+44 20 7946 0958")}
							>
								UK: +44 20 7946 0958
							</button>
							<button
								className="block text-blue-600 hover:underline"
								onClick={() => setPhone("+49 30 12345678")}
							>
								Germany: +49 30 12345678
							</button>
							<button
								className="block text-blue-600 hover:underline"
								onClick={() => setPhone("+81 3 1234 5678")}
							>
								Japan: +81 3 1234 5678
							</button>
						</div>
					</div>
				</div>
			</div>
		);
	},
};

export const Validation: Story = {
	render: () => {
		const [phones, setPhones] = React.useState({
			valid: "****** 123 4567",
			invalid: "******",
			empty: "",
		});

		const validatePhone = (value: string) => {
			try {
				phoneSchema.parse(value);
				return { isValid: true, message: "Valid phone number" };
			} catch {
				return { isValid: false, message: "Invalid phone number" };
			}
		};

		return (
			<div className="grid max-w-lg gap-6 p-6">
				<div className="space-y-4">
					<h3 className="text-lg font-semibold">
						Phone Number Validation
					</h3>

					<div className="space-y-4">
						<div>
							<label className="text-sm font-medium text-green-600">
								Valid Number
							</label>
							<InputPhone
								value={phones.valid}
								onChange={(value) =>
									setPhones((prev) => ({
										...prev,
										valid: value,
									}))
								}
								placeholder="Valid phone number"
								className="border-green-500"
							/>
							<p className="mt-1 text-xs text-green-600">
								✓ {validatePhone(phones.valid).message}
							</p>
						</div>

						<div>
							<label className="text-sm font-medium text-red-600">
								Invalid Number
							</label>
							<InputPhone
								value={phones.invalid}
								onChange={(value) =>
									setPhones((prev) => ({
										...prev,
										invalid: value,
									}))
								}
								placeholder="Invalid phone number"
								className="border-red-500"
							/>
							<p className="mt-1 text-xs text-red-600">
								✗ {validatePhone(phones.invalid).message}
							</p>
						</div>

						<div>
							<label className="text-sm font-medium">
								Test Input
							</label>
							<InputPhone
								value={phones.empty}
								onChange={(value) =>
									setPhones((prev) => ({
										...prev,
										empty: value,
									}))
								}
								placeholder="Type a phone number to validate"
							/>
							{phones.empty && (
								<p
									className={`mt-1 text-xs ${validatePhone(phones.empty).isValid ? "text-green-600" : "text-red-600"}`}
								>
									{validatePhone(phones.empty).isValid
										? "✓"
										: "✗"}{" "}
									{validatePhone(phones.empty).message}
								</p>
							)}
						</div>
					</div>
				</div>
			</div>
		);
	},
};

export const Accessibility: Story = {
	render: () => (
		<div className="grid max-w-lg gap-6 p-6">
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">
					Accessibility Features
				</h3>
				<div className="space-y-4">
					<div>
						<label
							htmlFor="accessible-phone"
							className="text-sm font-medium"
						>
							Phone Number (Accessible)
						</label>
						<InputPhone
							id="accessible-phone"
							placeholder="Enter phone number"
							aria-describedby="phone-help"
							defaultCountry="US"
						/>
						<p
							id="phone-help"
							className="text-muted-foreground mt-1 text-sm"
						>
							Include country code or select from dropdown
						</p>
					</div>

					<div>
						<label
							htmlFor="dropdown-phone"
							className="text-sm font-medium"
						>
							Phone with Country Selector
						</label>
						<InputPhone
							variant="with-country-dropdown"
							id="dropdown-phone"
							placeholder="Enter phone number"
							aria-label="Phone number with country selector"
							defaultCountry="CA"
						/>
					</div>

					<div className="text-muted-foreground text-sm">
						<h4 className="mb-2 font-medium">
							Keyboard Navigation:
						</h4>
						<ul className="space-y-1">
							<li>• Tab to navigate between inputs</li>
							<li>• Space/Enter to open country dropdown</li>
							<li>• Arrow keys to navigate dropdown options</li>
							<li>• Type to search countries in dropdown</li>
							<li>• Escape to close dropdown</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	),
	parameters: {
		docs: {
			description: {
				story: "Demonstrates accessibility features including ARIA labels, keyboard navigation, and screen reader support.",
			},
		},
	},
};
