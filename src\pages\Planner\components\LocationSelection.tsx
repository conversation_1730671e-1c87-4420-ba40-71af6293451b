import React, { useState } from 'react';
import { ChevronLeft, Search, SlidersHorizontal, ChevronRight, Users, Wrench, MapPin } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';

interface Location {
  id: string;
  name: string;
  address: string;
  initials: string;
  providers: number;
  services: number;
}

interface LocationSelectionProps {
  onBack: () => void;
  onSelectLocation: (location: Location) => void;
}

export const LocationSelection: React.FC<LocationSelectionProps> = ({ onBack, onSelectLocation }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);

  const locations: Location[] = [
    { id: '1', name: 'University of Waterloo, Ontario Laboratory', address: '5 Park Home Ave #130, Toronto.', initials: 'AB', providers: 0, services: 0 },
    { id: '2', name: 'University of Toronto, Ontario Research Centre', address: '123 College St, Toronto.', initials: 'AB', providers: 3, services: 5 },
    { id: '3', name: 'McMaster University, Hamilton Research Facility', address: '1280 Main St W, Hamilton.', initials: 'AB', providers: 2, services: 4 },
    { id: '4', name: 'University of Calgary, Alberta Lab', address: '2500 University Dr NW, Calgary.', initials: 'AB', providers: 1, services: 2 },
    { id: '5', name: 'University of Calgary, Alberta Lab', address: '2500 University Dr NW, Calgary.', initials: 'AB', providers: 1, services: 2 },
    { id: '6', name: 'University of Calgary, Alberta Lab', address: '2500 University Dr NW, Calgary.', initials: 'AB', providers: 1, services: 2 },
    { id: '7', name: 'McMaster University, Hamilton Research Facility', address: '1280 Main St W, Hamilton.', initials: 'AB', providers: 2, services: 4 },
  ];

  const handleLocationToggle = (locationId: string) => {
    setSelectedLocations(prev =>
      prev.includes(locationId)
        ? prev.filter(id => id !== locationId)
        : [...prev, locationId]
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={onBack} className="p-2">
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Select a Location</h1>
              <p className="text-gray-600 mt-1">Select a Location that you would like to set preferences for below.</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" size="sm">
              <Search className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <SlidersHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Search bar */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search locations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Locations List */}
        <div className="space-y-0 bg-white rounded-lg border border-gray-200 overflow-hidden">
          {/* Header row */}
          <div className="border-b border-gray-200 px-6 py-3 bg-gray-50">
            <div className="flex items-center">
              <Checkbox className="mr-4" />
              <div className="text-sm font-medium text-gray-700">Select All</div>
            </div>
          </div>

          {/* Location rows */}
          {locations.map((location, index) => (
            <div
              key={location.id}
              className={`border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer ${
                selectedLocations.includes(location.id) ? 'bg-blue-50' : ''
              }`}
              onClick={() => onSelectLocation(location)}
            >
              <div className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Checkbox
                    checked={selectedLocations.includes(location.id)}
                    onCheckedChange={() => handleLocationToggle(location.id)}
                    onClick={(e) => e.stopPropagation()}
                  />
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium text-gray-600">
                      {location.initials}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{location.name}</h3>
                      <p className="text-sm text-gray-500 flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {location.address}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Users className="h-4 w-4" />
                    <span>Providers</span>
                    <span className="font-medium">{location.providers}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Wrench className="h-4 w-4" />
                    <span>Services</span>
                    <span className="font-medium">{location.services}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-gray-100 rounded border flex items-center justify-center">
                      <div className="text-xs text-gray-600">5/8</div>
                    </div>
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <Button variant="outline" disabled>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="bg-blue-600 text-white">1</Button>
            <Button variant="outline" size="sm">2</Button>
            <Button variant="outline" size="sm">3</Button>
            <span className="text-gray-500">...</span>
          </div>
          <Button variant="outline">
            Next
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
};