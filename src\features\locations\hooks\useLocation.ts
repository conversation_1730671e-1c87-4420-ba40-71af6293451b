import { useQuery } from "@tanstack/react-query";
import { locationsApi } from "../api";
import { queryKeys } from "@/lib/query/keys";
import { useOrganizationContext } from "@/features/organizations/context";

export const useLocation = (id: string, enabled = true) => {
	const { organizationId } = useOrganizationContext();

	return useQuery({
		queryKey: queryKeys.locations.detail(id),
		queryFn: () => locationsApi.getLocation(id, organizationId!),
		enabled: enabled && !!id && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};
