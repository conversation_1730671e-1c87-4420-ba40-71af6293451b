import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import {
	enable2FA,
	confirm2FA,
	verify2FA,
	send2FAEmailOTP,
	disable2FA,
	get2FAStatus,
	skip2FA,
	reconfigure2FA,
} from "@/lib/api/auth";
import type {
	TwoFAEnableResponse,
	TwoFAConfirmRequest,
	TwoFAConfirmResponse,
	TwoFAVerifyRequest,
	TwoFAVerifyResponse,
	TwoFAEmailOTPRequest,
	TwoFAEmailOTPResponse,
	TwoFADisableResponse,
	TwoFAStatusResponse,
	TwoFASkipResponse,
	TwoFAReconfigureResponse,
} from "@/types/api/auth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

// 2FA Enable (GET)
export const useEnable2FA = (enabled?: boolean) => {
	return useQuery(
		["enable-2fa"],
		() => enable2FA(),
		{
			enabled,
			retry: 0,
			refetchOnMount: false,
			refetchOnWindowFocus: false,
			refetchOnReconnect: false,
			staleTime: Infinity,
		}
	);
};

export const useConfirm2FA = () => {
	return useMutation<TwoFAConfirmResponse, AxiosError, TwoFAConfirmRequest>({
		mutationFn: confirm2FA,
	});
};

export const useVerify2FA = (onSuccess?: (data: TwoFAVerifyResponse) => void, onError?: (error: AxiosError) => void) => {
	const handleLoginSuccess = useHandleLoginSuccess();
	return useMutation<TwoFAVerifyResponse, AxiosError, TwoFAVerifyRequest>({
		mutationFn: verify2FA,
		onSuccess: (data) => {
			onSuccess?.(data);
			handleLoginSuccess(data as any);
		},
		onError: (error) => {
			onError?.(error);
			console.error(error);
		},
	});
};

// 2FA Email OTP
export const useSend2FAEmailOTP = (onSuccess?: (data: TwoFAEmailOTPResponse) => void, onError?: (error: AxiosError) => void) => {
	return useMutation<TwoFAEmailOTPResponse, AxiosError, TwoFAEmailOTPRequest>({
		mutationFn: send2FAEmailOTP,
		onSuccess: (data) => {
			onSuccess?.(data);
		},
		onError: (error) => {
			onError?.(error);
		},
	});
};

// 2FA Disable
export const useDisable2FA = () => {
	return useMutation<TwoFADisableResponse, AxiosError>({
		mutationFn: disable2FA,
	});
};

// 2FA Status
export const use2FAStatus = () => {
	return useQuery(
		["2fa-status"],
		() => get2FAStatus(),
		{
			staleTime: Infinity,
		}
	);
};

// 2FA Skip
export const useSkip2FA = () => {
	const handleLoginSuccess = useHandleLoginSuccess();
	return useMutation<TwoFASkipResponse, AxiosError>({
		mutationFn: skip2FA,
		onSuccess: (data) => { handleLoginSuccess(data as any);},
		onError: (error) => {
			console.error(error);
		},
	});
};

// 2FA Reconfigure
export const useReconfigure2FA = () => {
	return useMutation<TwoFAReconfigureResponse, AxiosError>({
		mutationFn: reconfigure2FA,
	});
};
