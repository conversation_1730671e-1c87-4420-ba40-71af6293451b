import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { AccountSettingsContent } from "./settingTabsContents/account-settings";
import { NotificationsTabContent } from "./settingTabsContents/notifications";
import { AccessLogsTabContent } from "./settingTabsContents/access-logs";
import { AuditLogsTabContent } from "./settingTabsContents/audit-logs";
// Example icons (Lucide)
import { UserCog, Bell, FileLock2, ShieldCheck } from "lucide-react";

interface SettingsTabItem {
	id: string;
	label: string;
	icon: React.ComponentType<{ className?: string }>;
}

const tabItems: SettingsTabItem[] = [
	{ id: "account", label: "Account Settings", icon: UserCog },
	{ id: "notifications", label: "Notifications", icon: Bell },
	{ id: "access-logs", label: "Access Logs", icon: FileLock2 },
	{ id: "audit-logs", label: "Audit Logs", icon: ShieldCheck },
];

export function SettingsTabs({ className }: { className?: string }) {
	const [activeTab, setActiveTab] = useState(tabItems[0].id);

	return (
		<Tabs
			value={activeTab}
			onValueChange={setActiveTab}
			className={cn(
				"flex flex-nowrap h-screen w-full items-start justify-between bg-transparent ",
				className
			)}
        >
            
			<TabsList className="flex fixed h-full max-sm:w-5 max-sm:overflow-x-hidden max-sm:px-0 w-fit flex-col items-start justify-start border-r rounded-none border-gray-200 bg-white px-4 z-[1000] py-8">
				{tabItems.map((tab) => {
					const Icon = tab.icon;
					return (
						<TabsTrigger
							key={tab.id}
							value={tab.id}
							className={cn(
								"group flex max-h-10 w-full cursor-pointer items-center justify-start gap-2 rounded-md px-4 py-2 text-sm transition-colors max-sm:px-0",
								"bg-transparent hover:bg-gray-50",
								"data-[state=active]:bg-gray-100 data-[state=active]:text-gray-900",
								"text-gray-700"
							)}
						>
							<Icon
								className={cn(
									"h-4 w-4 opacity-70 transition-colors",
									"group-hover:text-gray-900"
								)}
							/>
							<span>{tab.label}</span>
						</TabsTrigger>
					);
				})}
			</TabsList>
			<div className="pl-8 pr-3 sm:ml-[13rem] py-4 w-full max-w-3xl">
				<TabsContent value="account">
					<AccountSettingsContent />
				</TabsContent>

				<TabsContent value="notifications">
					<NotificationsTabContent />
				</TabsContent>

				<TabsContent value="access-logs">
					<AccessLogsTabContent />
				</TabsContent>

				<TabsContent value="audit-logs">
					<AuditLogsTabContent />
				</TabsContent>
			</div>
		</Tabs>
	);
}
