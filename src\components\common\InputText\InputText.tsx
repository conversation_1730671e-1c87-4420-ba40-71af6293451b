import React from "react";
import { Controller } from "react-hook-form";
import type { FieldValues } from "react-hook-form";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	FormControl,
	FormDescription,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import type {
	InputTextProps,
	WithLabelProps,
	WithButtonProps,
	WithTextProps,
	WithIconProps,
	FileInputProps,
	DisabledInputProps,
	FormInputProps,
	InputTextSize,
} from "./types";

const sizeClasses: Record<InputTextSize, string> = {
	sm: "h-8 px-2 text-sm",
	md: "h-10 px-3",
	lg: "h-12 px-4 text-lg",
};

// Default Input Implementation
const DefaultInput = React.forwardRef<HTMLInputElement, InputTextProps>(
	({ size = "md", className, error, ...props }, ref) => (
		<Input
			ref={ref}
			className={cn(
				sizeClasses[size],
				error && "border-destructive focus-visible:ring-destructive",
				className
			)}
			{...props}
		/>
	)
);

// With Label Implementation
const WithLabelInput = React.forwardRef<HTMLInputElement, WithLabelProps>(
	({ label, htmlFor, size = "md", className, error, ...props }, ref) => (
		<div className="grid w-full flex-col items-center gap-1.5">
			<Label htmlFor={htmlFor || props.id}>{label}</Label>
			<Input
				ref={ref}
				className={cn(
					sizeClasses[size],
					error &&
						"border-destructive focus-visible:ring-destructive w-full",
					className
				)}
				{...props}
			/>
		</div>
	)
);

// With Button Implementation
const WithButtonInput = React.forwardRef<HTMLInputElement, WithButtonProps>(
	(
		{
			buttonText,
			onButtonClick,
			buttonDisabled,
			size = "md",
			className,
			error,
			...props
		},
		ref
	) => (
		<div className="flex w-full max-w-sm items-center space-x-2">
			<Input
				ref={ref}
				className={cn(
					sizeClasses[size],
					error &&
						"border-destructive focus-visible:ring-destructive",
					className
				)}
				{...props}
			/>
			<Button
				type="button"
				onClick={onButtonClick}
				disabled={buttonDisabled}
				size={size === "sm" ? "sm" : size === "lg" ? "lg" : "default"}
			>
				{buttonText}
			</Button>
		</div>
	)
);

// With Text Implementation
const WithTextInput = React.forwardRef<HTMLInputElement, WithTextProps>(
	(
		{ leadingText, trailingText, size = "md", className, error, ...props },
		ref
	) => (
		<div className="flex">
			{leadingText && (
				<span
					className={cn(
						"border-input bg-muted text-muted-foreground inline-flex items-center rounded-l-md border border-r-0 px-3 text-sm",
						size === "sm" && "px-2 text-xs",
						size === "lg" && "px-4 text-base"
					)}
				>
					{leadingText}
				</span>
			)}
			<Input
				ref={ref}
				className={cn(
					sizeClasses[size],
					leadingText && "rounded-l-none",
					trailingText && "rounded-r-none",
					error &&
						"border-destructive focus-visible:ring-destructive",
					className
				)}
				{...props}
			/>
			{trailingText && (
				<span
					className={cn(
						"border-input bg-muted text-muted-foreground inline-flex items-center rounded-r-md border border-l-0 px-3 text-sm",
						size === "sm" && "px-2 text-xs",
						size === "lg" && "px-4 text-base"
					)}
				>
					{trailingText}
				</span>
			)}
		</div>
	)
);

// With Icon Implementation
const WithIconInput = React.forwardRef<HTMLInputElement, WithIconProps>(
	(
		{
			icon,
			iconPosition = "left",
			size = "md",
			className,
			error,
			...props
		},
		ref
	) => (
		<div className="relative">
			{iconPosition === "left" && (
				<div
					className={cn(
						"text-muted-foreground absolute top-1/2 left-3 -translate-y-1/2 transform",
						size === "sm" && "left-2",
						size === "lg" && "left-4"
					)}
				>
					{icon}
				</div>
			)}
			<Input
				ref={ref}
				className={cn(
					sizeClasses[size],
					iconPosition === "left" &&
						(size === "sm"
							? "pl-8"
							: size === "lg"
								? "pl-12"
								: "pl-10"),
					iconPosition === "right" &&
						(size === "sm"
							? "pr-8"
							: size === "lg"
								? "pr-12"
								: "pr-10"),
					error &&
						"border-destructive focus-visible:ring-destructive",
					className
				)}
				{...props}
			/>
			{iconPosition === "right" && (
				<div
					className={cn(
						"text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 transform",
						size === "sm" && "right-2",
						size === "lg" && "right-4"
					)}
				>
					{icon}
				</div>
			)}
		</div>
	)
);

// File Input Implementation
const FileInput = React.forwardRef<HTMLInputElement, FileInputProps>(
	({ size = "md", className, error, ...props }, ref) => (
		<Input
			ref={ref}
			type="file"
			className={cn(
				sizeClasses[size],
				"file:border-0 file:bg-transparent file:text-sm file:font-medium",
				error && "border-destructive focus-visible:ring-destructive",
				className
			)}
			{...props}
		/>
	)
);

// Disabled Input Implementation
const DisabledInput = React.forwardRef<HTMLInputElement, DisabledInputProps>(
	({ size = "md", className, ...props }, ref) => (
		<Input
			ref={ref}
			// disabled
			className={cn(
				sizeClasses[size],
				"cursor-not-allowed opacity-50",
				className
			)}
			{...props}
		/>
	)
);

// Form Input Implementation
function FormInput({
	name,
	control,
	label,
	description,
	rules,
	size = "md",
	className,
	...props
}: FormInputProps) {
	return (
		<Controller
			name={name}
			control={control}
			rules={rules}
			render={({ field, fieldState }) => (
				<FormItem>
					{label && <FormLabel>{label}</FormLabel>}
					<FormControl>
						<Input
							{...field}
							className={cn(
								sizeClasses[size],
								fieldState.error &&
									"border-destructive focus-visible:ring-destructive",
								className
							)}
							{...props}
						/>
					</FormControl>
					{description && (
						<FormDescription>{description}</FormDescription>
					)}
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}

// Main InputText Component
export const InputText = React.forwardRef<HTMLInputElement, InputTextProps>(
	(props, ref) => {
		const { variant = "default", ...otherProps } = props;

		switch (variant) {
			case "with-label":
				return (
					<WithLabelInput
						ref={ref}
						{...(otherProps as WithLabelProps)}
					/>
				);
			case "with-button":
				return (
					<WithButtonInput
						ref={ref}
						{...(otherProps as WithButtonProps)}
					/>
				);
			case "with-text":
				return (
					<WithTextInput
						ref={ref}
						{...(otherProps as WithTextProps)}
					/>
				);
			case "with-icon":
				return (
					<WithIconInput
						ref={ref}
						{...(otherProps as WithIconProps)}
					/>
				);
			case "file":
				return (
					<FileInput ref={ref} {...(otherProps as FileInputProps)} />
				);
			case "disabled":
				return (
					<DisabledInput
						ref={ref}
						{...(otherProps as DisabledInputProps)}
					/>
				);
			case "form":
				return <FormInput {...(otherProps as FormInputProps)} />;
			default:
				return <DefaultInput ref={ref} {...otherProps} />;
		}
	}
);

InputText.displayName = "InputText";
