import React, { useState } from 'react';
import { ChevronLeft, X, ChevronRight, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface Provider {
  id: string;
  name: string;
  avatar: string;
  services: string[];
}

interface Location {
  id: string;
  name: string;
  description: string;
}

interface FrequencyApplicationSheetProps {
  isOpen: boolean;
  onClose: () => void;
  totalMaxFrequency: string;
  frequencyPeriod: string;
  onComplete: (data: {
    applicationMode: 'cumulative' | 'individual';
    providerSelection: 'all' | 'selected';
    selectedProviders: string[];
    selectedLocations: string[];
  }) => void;
}

export const FrequencyApplicationSheet: React.FC<FrequencyApplicationSheetProps> = ({
  isOpen,
  onClose,
  totalMaxFrequency,
  frequencyPeriod,
  onComplete
}) => {
  const [step, setStep] = useState(1);
  const [applicationMode, setApplicationMode] = useState<'cumulative' | 'individual'>('cumulative');
  const [providerSelection, setProviderSelection] = useState<'all' | 'selected'>('all');
  const [selectedProviders, setSelectedProviders] = useState<string[]>([]);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);

  // Sample data - replace with actual data
  const providers: Provider[] = [
    { id: '1', name: 'Dr. Abraham Lincoln', avatar: '/api/placeholder/40/40', services: ['Service 1', 'Service 2', 'Service 3'] },
    { id: '2', name: 'Dr. Sarah Wilson', avatar: '/api/placeholder/40/40', services: ['Service 1', 'Service 2', 'Service 3'] },
    { id: '3', name: 'Dr. Michael Johnson', avatar: '/api/placeholder/40/40', services: ['Service 1', 'Service 2', 'Service 3'] },
  ];

  const locations: Location[] = [
    { id: '1', name: 'Location Name I', description: 'Main facility downtown' },
    { id: '2', name: 'Location Name II', description: 'Satellite clinic uptown' },
  ];

  const handleComplete = () => {
    const result = {
      applicationMode,
      providerSelection,
      selectedProviders,
      selectedLocations
    };
    onComplete(result);
    handleClose();
  };

  const handleClose = () => {
    onClose();
    setStep(1);
  };

  if (!isOpen) return null;

  // Step 1: Select Application Mode
  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-2">Select How Frequency is Applied</h2>
        <p className="text-gray-600">Choose how the maximum frequency should be applied across your organization</p>
      </div>
      
      {/* Frequency Display */}
      <div className="text-center py-8 bg-gray-50 rounded-lg">
        <div className="text-6xl font-bold text-gray-900 mb-2">{totalMaxFrequency}</div>
        <div className="text-lg text-gray-600 mb-1">/ {frequencyPeriod.toLowerCase()}</div>
        <div className="text-sm font-medium text-primary uppercase tracking-wide">Maximum Frequency</div>
      </div>

      <div className="space-y-4">
        {/* Cumulative Option */}
        <div 
          className={`cursor-pointer border-2 rounded-lg p-6 transition-all ${
            applicationMode === 'cumulative' 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-200 hover:border-gray-300'
          }`}
          onClick={() => setApplicationMode('cumulative')}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="font-semibold text-lg text-gray-900 mb-3">CUMULATIVE</div>
              <div className="text-gray-600 leading-relaxed">
                Apply this rule to the Organization cumulatively across all Locations and providers. 
                Ex. There can only be {totalMaxFrequency} appointments a {frequencyPeriod.toLowerCase()} 
                across the entire organization.
              </div>
            </div>
            <Button 
              className={`ml-4 px-6 py-3 rounded-lg font-medium transition-colors ${
                applicationMode === 'cumulative' 
                  ? 'bg-primary text-white' 
                  : 'bg-gray-400 text-white'
              }`}
              onClick={(e) => {
                e.stopPropagation();
                setApplicationMode('cumulative');
              }}
            >
              Apply Cumulatively
            </Button>
          </div>
        </div>

        {/* Individual Option */}
        <div 
          className={`cursor-pointer border-2 rounded-lg p-6 transition-all ${
            applicationMode === 'individual' 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-200 hover:border-gray-300'
          }`}
          onClick={() => setApplicationMode('individual')}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="font-semibold text-lg text-gray-900 mb-3">INDIVIDUALLY</div>
              <div className="text-gray-600 leading-relaxed">
                Apply this rule to each location or provider individually. 
                Ex. Each provider can be booked {totalMaxFrequency} times this {frequencyPeriod.toLowerCase()}.
              </div>
            </div>
            <Button 
              className={`ml-4 px-6 py-3 rounded-lg font-medium transition-colors ${
                applicationMode === 'individual' 
                  ? 'bg-primary text-white' 
                  : 'bg-gray-400 text-white'
              }`}
              onClick={(e) => {
                e.stopPropagation();
                setApplicationMode('individual');
              }}
            >
              Apply Individually
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  // Step 2: Provider Selection
  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" onClick={() => setStep(1)}>
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div>
          <h2 className="text-2xl font-semibold">Provider Application</h2>
          <p className="text-gray-600">Select which providers this preference will apply to</p>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="font-semibold text-lg">Apply Custom Preference to</h3>
        
        <div className="space-y-3">
          {/* All Locations Option */}
          <div 
            className={`cursor-pointer border rounded-lg p-4 transition-all ${
              providerSelection === 'all' 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setProviderSelection('all')}
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900 text-lg">All Locations in this Organization</div>
                <div className="text-gray-500 mt-1">
                  This selection will apply the new rule to all the Providers in this Organization
                </div>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </div>
          </div>

          {/* Selected Locations Option */}
          <div 
            className={`cursor-pointer border rounded-lg p-4 transition-all ${
              providerSelection === 'selected' 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setProviderSelection('selected')}
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900 text-lg">Selected Locations and Providers only</div>
                <div className="text-gray-500 mt-1">
                  This selection will apply the new rule to selected Providers in this Organization
                </div>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Step 3: Specific Provider Selection
  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" onClick={() => setStep(2)}>
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div>
          <h2 className="text-2xl font-semibold">Select Providers</h2>
          <p className="text-gray-600">Choose specific locations and providers for this preference</p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
          <div className="text-primary">👈</div>
          <span className="font-medium text-blue-900">Selected Locations and Providers only</span>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input 
            placeholder="Search locations and providers..." 
            className="pl-10"
          />
        </div>

        {/* Locations and Providers List */}
        <div className="border rounded-lg max-h-96 overflow-y-auto">
          {locations.map((location, locationIndex) => (
            <div key={location.id} className={`${locationIndex > 0 ? 'border-t' : ''}`}>
              {/* Location Header */}
              <div className="flex items-center space-x-3 p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                <Checkbox 
                  checked={selectedLocations.includes(location.id)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedLocations(prev => [...prev, location.id]);
                    } else {
                      setSelectedLocations(prev => prev.filter(id => id !== location.id));
                    }
                  }}
                />
                <div className="flex-1">
                  <div className="font-semibold text-gray-900">{location.name}</div>
                  <div className="text-sm text-gray-500">{location.description}</div>
                </div>
              </div>
              
              {/* Providers under this location */}
              <div className="bg-white">
                {providers.map((provider) => (
                  <div key={`${location.id}-${provider.id}`} className="flex items-center space-x-3 p-4 pl-10 border-t border-gray-100 hover:bg-gray-50 transition-colors">
                    <Checkbox 
                      checked={selectedProviders.includes(`${location.id}-${provider.id}`)}
                      onCheckedChange={(checked) => {
                        const providerId = `${location.id}-${provider.id}`;
                        if (checked) {
                          setSelectedProviders(prev => [...prev, providerId]);
                        } else {
                          setSelectedProviders(prev => prev.filter(id => id !== providerId));
                        }
                      }}
                    />
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={provider.avatar} />
                      <AvatarFallback>
                        {provider.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{provider.name}</div>
                      <div className="text-sm text-gray-500">
                        Top Services: {provider.services.join(' | ')}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 z-50">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-opacity-20" onClick={handleClose} />
      
      {/* Sheet */}
      <div className="fixed right-0 top-0 h-full w-full max-w-2xl bg-white shadow-2xl overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b bg-white">
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-500">
              Step {step} of {providerSelection === 'selected' ? '3' : '2'}
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={handleClose}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
        </div>

        {/* Footer */}
        <div className="border-t p-6 bg-white">
          <div className="flex justify-between">
            <Button 
              variant="outline"
              onClick={step === 1 ? handleClose : () => setStep(step - 1)}
            >
              {step === 1 ? 'Cancel' : 'Back'}
            </Button>
            <Button 
              onClick={() => {
                if (step === 1) {
                  setStep(2);
                } else if (step === 2) {
                  if (providerSelection === 'selected') {
                    setStep(3);
                  } else {
                    handleComplete();
                  }
                } else {
                  handleComplete();
                }
              }}
            >
              {(step === 2 && providerSelection === 'all') || step === 3 ? 'Complete' : 'Continue'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};