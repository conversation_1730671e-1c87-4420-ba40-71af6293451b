import React, { memo, useCallback } from "react";
import {
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ConfirmationModalProps {
	data: {
		title: string;
		message: string;
		confirmText?: string;
		cancelText?: string;
		variant?: "default" | "destructive";
		onConfirm: () => void;
	};
	onClose: () => void;
}

export const ConfirmationModal = memo(
	({ data, onClose }: ConfirmationModalProps) => {
		const {
			title,
			message,
			confirmText = "Confirm",
			cancelText = "Cancel",
			variant = "default",
			onConfirm,
		} = data;

		const handleConfirm = useCallback(() => {
			onConfirm();
			onClose();
		}, [onConfirm, onClose]);

		return (
			<>
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{message}</DialogDescription>
				</DialogHeader>

				<DialogFooter>
					<Button variant="outline" onClick={onClose}>
						{cancelText}
					</Button>
					<Button variant={variant} onClick={handleConfirm}>
						{confirmText}
					</Button>
				</DialogFooter>
			</>
		);
	}
);

ConfirmationModal.displayName = "ConfirmationModal";
