import * as React from "react";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

export interface PatientListCardProps
	extends React.HTMLAttributes<HTMLDivElement> {
	patientName: string;
	email: string;
	uid: string;
	initials: string;
	isSelected?: boolean;
	onSelect?: () => void;
}

const PatientListCard = React.forwardRef<HTMLDivElement, PatientListCardProps>(
	(
		{
			className,
			patientName,
			email,
			uid,
			initials,
			isSelected = false,
			onSelect,
			...props
		},
		ref
	) => {
		return (
			<div
				ref={ref}
				className={cn(
					"inline-flex w-[679px] cursor-pointer items-center justify-start rounded-lg border border-[#E4E4E7] transition-colors",
					isSelected && "border-[#005893]",
					className
				)}
				onClick={onSelect}
				{...props}
			>
				<div className="flex min-w-20 flex-1 items-center justify-start gap-3 p-3">
					{isSelected && <Check className="h-4 w-4 text-[#005893]" />}
					<div className="relative flex h-8 w-8 items-center justify-center overflow-hidden rounded-full bg-[#E4E4E7]">
						<div className="font-['Inter'] text-xs font-medium">
							{initials}
						</div>
					</div>
					<div className="inline-flex flex-1 flex-col items-start justify-start gap-0.5">
						<div className="text-Text-Base justify-center font-['Inter'] text-sm leading-tight font-medium">
							{patientName}
						</div>
						<div className="text-Text-Muted justify-center font-['Inter'] text-[10px] leading-3 font-normal">
							{email}
						</div>
					</div>
				</div>
				<div className="text-Text-Base justify-center p-3 font-['Inter'] text-sm leading-tight font-medium">
					{uid}
				</div>
			</div>
		);
	}
);

PatientListCard.displayName = "PatientListCard";

export { PatientListCard };
