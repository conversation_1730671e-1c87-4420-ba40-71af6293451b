import React, { useState } from 'react';
import { <PERSON><PERSON>ronLeft, AlertTriangle, Calendar, Clock, User, Trash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { GoTag } from 'react-icons/go';
import { IoIosInformationCircleOutline } from 'react-icons/io';
import { LuLayoutDashboard, LuCalendar, LuClock, LuCalendarSync } from 'react-icons/lu';
import { FiUser } from 'react-icons/fi';
import { FaRegCommentDots, FaRegTrashAlt } from 'react-icons/fa';
import { MdOutlineNotificationsActive } from 'react-icons/md';

interface AppointmentCard {
  id: string;
  patientName: string;
  providerName: string;
  appointmentType: string;
  date: string;
  time: string;
}

interface AppointmentConflictsScreenProps {
  newRule: {
    title: string;
    frequency: string;
    occurrence: string;
    availability: string;
    timePeriod: string;
  };
  impactedAppointments: AppointmentCard[];
  impactCount: number;
  onBack: () => void;
  onLetThemBe: () => void;
  onCancelAll: () => void;
  onRescheduleAll: () => void;
  onViewAllAppointments: () => void;
}

export const AppointmentConflictsScreen: React.FC<AppointmentConflictsScreenProps> = ({
  newRule,
  impactedAppointments,
  impactCount,
  onBack,
  onLetThemBe,
  onCancelAll,
  onRescheduleAll,
  onViewAllAppointments
}) => {
  const [view, setView] = useState<'summary' | 'detailed'>('summary');

  const renderSummaryView = () => (
    <div className="text-center py-12">
      <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <Calendar className="h-12 w-12 text-primary" />
      </div>
      
      <h3 className="text-2xl font-semibold text-gray-900 mb-2">
        [{impactCount}] Existing Appointments Impacted
      </h3>
      
      <p className="text-gray-600 max-w-md mx-auto mb-6">
        Preexisting appointments do now follow the rules of the custom preference you just set. 
        Resolve conflict of existing appointments from the options on the right.
      </p>
      
      <Button variant="link" onClick={() => setView('detailed')} className="text-primary">
        View all Appointments
      </Button>
    </div>
  );

  const renderDetailedView = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <Button variant="link" onClick={() => setView('summary')} className="text-primary">
          Back to Summary
        </Button>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        {impactedAppointments.map((appointment, index) => (
          <div key={appointment.id} className="border border-[#E4E4E7] p-3 bg-white rounded-xl space-y-3 transition-all duration-300 hover:border-[#128576] hover:shadow-[0px_-1px_6px_#128576]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-x-2">
                <Checkbox />
                <div>
                  <h3 className="text-[#18181B] text-sm font-regular">{appointment.patientName}</h3>
                </div>
                <GoTag color="#8CA3B2" className="text-sm" />
              </div>
              <IoIosInformationCircleOutline color="#8CA3B2" className="text-base" />
            </div>
            <p className="text-[#52525B] text-sm mt-2 flex items-center gap-x-1">
              <LuLayoutDashboard color="#52525B" className="text-sm" />
              <span className="text-[#56758A]">
                {appointment.providerName}
              </span>
            </p>
            <h1 className="flex items-center gap-x-2">
              <FiUser color="#042C4D" />
              <span className="text-[#042C4D]">
                {appointment.appointmentType}
              </span>
            </h1>
            <div className="flex items-center gap-x-4 text-[#27272A]">
              <div className="flex items-center gap-x-1">
                <LuCalendar className="opacity-50" />
                <p className="opacity-80">{appointment.date}</p>
              </div>
              <div className="flex items-center gap-x-1">
                <LuClock className="opacity-50" />
                <p className="opacity-80">
                  {appointment.time}
                </p>
              </div>
            </div>
            <div className="pt-1 pb-2 flex items-center justify-between">
              <div className="flex items-center gap-x-3 text-[#27272A] opacity-50">
                <FaRegCommentDots size={18} />
                <LuCalendarSync size={18} />
                <FaRegTrashAlt size={18} />
              </div>
              <Button variant="outline" className="py-3 px-4">
                <MdOutlineNotificationsActive color="#005893" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen ">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div className="bg-primary text-white px-3 py-1 rounded text-sm font-medium">
            NEW RULE
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* New Rule Summary */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                 <div className="flex items-center justify-between border-b pb-4">
                   <div className="flex items-center space-x-4">
                     <Badge variant="default" className='bg-primary/20 text-primary'>01</Badge>
                     <h3 className="text-lg font-medium">{newRule.title}</h3>
                   </div>
                   <div className="text-sm text-gray-500">Created: 07 June 2025</div>
                 </div>
                 
          
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Badge variant="outline" className="bg-gray-100">Frequency</Badge>
                <span className="text-sm">{newRule.frequency}</span>
              </div>
              <div className="flex space-x-2">
                <Badge variant="secondary" className="bg-green-100 text-green-800">Availability</Badge>
                <span className="text-sm">{newRule.availability}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Badge variant="outline" className="bg-gray-100">Occurrence</Badge>
                <span className="text-sm">{newRule.occurrence}</span>
              </div>
              <div className="flex space-x-2">
                <Badge variant="outline" className="bg-gray-100">Time Period</Badge>
                <span className="text-sm">{newRule.timePeriod}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6">
          {/* Existing Appointments */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Existing Appointments</h2>
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <Badge variant="destructive" className="bg-red-100 text-red-800">
                    Impact {impactCount}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="p-6">
              {view === 'summary' ? renderSummaryView() : renderDetailedView()}
            </div>
          </div>

          {/* Resolve Conflicts */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold">Resolve Conflicts</h2>
              <p className="text-sm text-gray-600 mt-2">
                Select one of the following from below:
              </p>
            </div>

            <div className="p-6 space-y-4">
              {/* Option 1: Leave as is */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                   <Badge className='bg-primary/30 text-black mb-1'>OPTION 1</Badge>
                  <div className="font-medium">Leave all appointments as is</div>
                </div>
                <Button onClick={onLetThemBe} >
                  Let them Be
                </Button>
              </div>

              {/* Option 2: Cancel all */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Badge className='bg-primary/30 text-black mb-1'>OPTION 2</Badge>
                  <div className="font-medium">Cancel all future appointments</div>
                </div>
                <Button onClick={onCancelAll} >
                  Cancel All
                </Button>
              </div>

              {/* Option 3: Reschedule all */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                   <Badge className='bg-primary/30 text-black mb-1'>OPTION 3</Badge>
                  <div className="font-medium">Cancel and request to reschedule all appointments</div>
                </div>
                <Button onClick={onRescheduleAll} >
                  Reschedule All
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};