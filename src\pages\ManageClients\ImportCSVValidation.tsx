import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, Trash2, MinusCircle } from "lucide-react";
import { SelectSheetInfoCard } from "@/components/common/SelectSheetInfoCard";
import { ImportSuccessCard } from "@/components/common/ImportSuccessCard";
import { parseFile } from "@/utils/fileParser";
import { InputText } from "@/components/common/InputText/InputText";
import { apiClient } from "@/lib/api/clients";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import type { BusinessAttribute } from "@/types/businessAttributes";
import { parseAndValidatePhone } from "@/components/common/InputPhone/utils";

interface MappingRow {
	id: number;
	excel: string;
	migranium: string;
	type: string;
	isCreatingNew?: boolean;
	newFieldLabel?: string;
}

interface MappingData {
	mappingRows: MappingRow[];
	selectedSheet: string;
	availableSheets: string[];
	clearStoredCustomFields?: () => void;
}

interface ValidationRow {
	id: number;
	excelCell: string;
	[key: string]: any;
	errors: string[];
	visualErrors: string[];
	wasProblematic: boolean;
}

interface ValidationData {
	readyToImport: number;
	missingDataConflicts: number;
	duplicateDataConflicts: number;
	showMissingData: boolean;
	showDuplicateData: boolean;
	validationRows: ValidationRow[];
	discardedMissingDataRows: Set<number>; // Track which rows are discarded
	discardedDuplicateDataRows: Set<number>; // Track which rows are discarded
}

interface ImportState {
	isImporting: boolean;
	isSuccess: boolean;
	importedCount: number;
}

interface ImportCSVValidationProps {
	uploadedFile: File | null;
	mappingData: MappingData | null;
	onBack: () => void;
	onTest: () => void;
	onImport: () => void;
	onPopulateData?: () => void;
	onImportAgain?: () => void;
	onDone?: () => void;
	onImportSuccess?: (isSuccessful: boolean) => void;
}

export default function ImportCSVValidation({
	uploadedFile,
	mappingData,
	onBack,
	onTest,
	onImport,
	onPopulateData,
	onImportAgain,
	onDone,
	onImportSuccess,
}: ImportCSVValidationProps) {
	const { organizationId } = useOrganizationContext();
	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useBusinessAttributesForForm();

	// Deduplicate and memoize business attributes
	const businessAttributes: BusinessAttribute[] = useMemo(() => {
		const attributes = (businessAttributesData as any)?.data || [];

		// Deduplicate attributes by key - prefer system fields over custom fields
		const deduplicatedAttributes = attributes.reduce(
			(acc: BusinessAttribute[], current: BusinessAttribute) => {
				const existingIndex = acc.findIndex(
					(attr) => attr.key === current.key
				);

				if (existingIndex === -1) {
					acc.push(current);
				} else {
					const existing = acc[existingIndex];
					if (current.is_system_field && !existing.is_system_field) {
						acc[existingIndex] = current;
					}
				}

				return acc;
			},
			[]
		);

		return deduplicatedAttributes;
	}, [businessAttributesData]);
	const [validationData, setValidationData] = useState<ValidationData>({
		readyToImport: 0,
		missingDataConflicts: 0,
		duplicateDataConflicts: 0,
		showMissingData: true,
		showDuplicateData: false,
		validationRows: [],
		discardedMissingDataRows: new Set(),
		discardedDuplicateDataRows: new Set(),
	});

	const [isValidating, setIsValidating] = useState(false);

	const [importState, setImportState] = useState<ImportState>({
		isImporting: false,
		isSuccess: false,
		importedCount: 0,
	});

	// Validation utility functions
	const validateEmail = (email: string): boolean => {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email.trim());
	};

	const validatePhoneNumber = (phone: string): boolean => {
		try {
			// Handle common phone number formats from Excel/CSV
			let normalizedPhone = phone.trim();
			console.log(
				"Validating phone number:",
				phone,
				"-> normalized:",
				normalizedPhone
			);

			// First, try to validate the phone number as-is
			let phoneData = parseAndValidatePhone(normalizedPhone);
			console.log("Initial validation result:", {
				original: phone,
				normalized: normalizedPhone,
				isValid: phoneData.isValid,
				isPossible: phoneData.isPossible,
				country: phoneData.country,
				formattedNumber: phoneData.formattedNumber,
			});

			// If it's already valid, return true
			if (phoneData.isValid) {
				return true;
			}

			// If not valid and doesn't start with +, try adding + prefix
			// This handles cases where users enter numbers without the international prefix
			if (!normalizedPhone.startsWith("+")) {
				const withPlus = "+" + normalizedPhone;
				const phoneDataWithPlus = parseAndValidatePhone(withPlus);
				console.log("Retry with + prefix:", {
					withPlus,
					isValid: phoneDataWithPlus.isValid,
					isPossible: phoneDataWithPlus.isPossible,
					country: phoneDataWithPlus.country,
				});

				// If adding + makes it valid, use that
				if (phoneDataWithPlus.isValid) {
					return true;
				}

				// If still not valid but is "possible", we might accept it
				// This handles edge cases where libphonenumber is strict
				if (phoneDataWithPlus.isPossible) {
					console.log(
						"Phone is possible but not strictly valid, accepting it"
					);
					return true;
				}
			}

			// If the original number (without +) is at least "possible", accept it
			// This handles local numbers that might be valid in their country context
			if (phoneData.isPossible) {
				console.log(
					"Original phone is possible but not strictly valid, accepting it"
				);
				return true;
			}

			return false;
		} catch (error) {
			console.warn("Phone validation error:", error);
			// Fallback to basic validation if libphonenumber fails
			const cleanPhone = phone.replace(/[^\d+]/g, "");
			const phoneRegex = /^(\+?[1-9]\d{6,14})$/;
			const fallbackResult = phoneRegex.test(cleanPhone);
			console.log("Using fallback validation:", {
				phone,
				cleanPhone,
				result: fallbackResult,
			});
			return fallbackResult;
		}
	};

	const validateDate = (dateString: string): boolean => {
		if (!dateString || dateString.trim() === "") return true; // Empty dates are valid unless required

		const date = new Date(dateString);
		return (
			!isNaN(date.getTime()) &&
			date.getFullYear() > 1900 &&
			date.getFullYear() < 2100
		);
	};

	const validateNumber = (numberString: string): boolean => {
		if (!numberString || numberString.trim() === "") return true; // Empty numbers are valid unless required

		const num = parseFloat(numberString);
		return !isNaN(num) && isFinite(num);
	};

	const validateBoolean = (boolString: string): boolean => {
		if (!boolString || boolString.trim() === "") return true; // Empty booleans are valid unless required

		const lowerValue = boolString.toLowerCase().trim();
		return ["true", "false", "1", "0", "yes", "no", "y", "n"].includes(
			lowerValue
		);
	};

	// Get required fields based on business attributes
	const getRequiredFields = (): string[] => {
		return businessAttributes
			.filter((attr) => attr.is_required)
			.map((attr) => attr.key);
	};

	// Get field validation type based on business attributes
	const getFieldValidationType = (fieldKey: string): string => {
		const attribute = businessAttributes.find(
			(attr) => attr.key === fieldKey
		);
		return attribute?.type || "text";
	};

	const prepareImportData = () => {
		if (!mappingData || !uploadedFile) {
			return null;
		}

		// Include rows that have no errors (valid data)
		// Exclude rows that have errors (conflicted data) - whether discarded or not
		// The key change: when user clicks "Discard conflicted data", we allow import to proceed
		// even if there are conflicted rows, because we're only importing the valid ones
		const validRowsToImport = validationData.validationRows.filter(
			(row) => row.errors.length === 0
		);

		const mappedFields = mappingData.mappingRows.filter(
			(row) => row.migranium && row.migranium.trim() !== ""
		);

		// Format data according to bulk import API specification
		// Each patient record should have key-value pairs of "Column Title on Excel/CSV" and cell value
		const patients = validRowsToImport.map((row) => {
			const patientData: Record<string, any> = {};

			mappedFields.forEach((mappingRow) => {
				// Use the original Excel column name as the key (not the migranium field name)
				let fieldValue = row[mappingRow.migranium];
				if (
					fieldValue !== undefined &&
					fieldValue !== null &&
					fieldValue !== ""
				) {
					// Normalize phone numbers to E.164 format for backend
					const fieldType = getFieldValidationType(
						mappingRow.migranium
					);
					if (fieldType === "phone") {
						try {
							let normalizedPhone = fieldValue.toString().trim();
							console.log("Normalizing phone for backend:", {
								original: fieldValue,
								normalized: normalizedPhone,
								fieldKey: mappingRow.migranium,
							});

							// First, try to parse the phone number as-is
							let phoneData =
								parseAndValidatePhone(normalizedPhone);

							// If not valid and doesn't start with +, try adding + prefix
							if (
								!phoneData.isValid &&
								!normalizedPhone.startsWith("+")
							) {
								const withPlus = "+" + normalizedPhone;
								const phoneDataWithPlus =
									parseAndValidatePhone(withPlus);

								// Use the version with + if it's more valid
								if (
									phoneDataWithPlus.isValid ||
									(!phoneData.isValid &&
										phoneDataWithPlus.isPossible)
								) {
									phoneData = phoneDataWithPlus;
									normalizedPhone = withPlus;
								}
							}
							if (
								phoneData.isValid &&
								phoneData.formattedNumber
							) {
								console.log("Phone normalized successfully:", {
									original: fieldValue,
									normalized: phoneData.formattedNumber,
									country: phoneData.country,
								});
								fieldValue = phoneData.formattedNumber;
							} else if (
								phoneData.isPossible &&
								phoneData.formattedNumber
							) {
								fieldValue = phoneData.formattedNumber;
							} else {
								console.warn(
									"Phone normalization failed, keeping original:",
									{
										original: fieldValue,
										normalized: normalizedPhone,
										phoneData,
									}
								);
							}
						} catch (error) {
							console.warn(
								"Error normalizing phone number:",
								error
							);
						}
					}

					patientData[mappingRow.excel] = fieldValue;
				}
			});

			return patientData;
		});

		return patients;
	};

	const handleImport = async () => {
		setImportState((prev) => ({ ...prev, isImporting: true }));
		const importData = prepareImportData();

		if (!importData) {
			setImportState((prev) => ({ ...prev, isImporting: false }));
			alert("No data to import");
			return;
		}
		console.log(JSON.stringify(importData, null, 2));

		try {
			const response = await apiClient.post(
				"/api/v1/clients/bulk-import",
				importData,
				{
					headers: {
						"X-organizationId": organizationId?.toString() || "",
					},
				}
			);
			if (mappingData?.clearStoredCustomFields) {
				mappingData.clearStoredCustomFields();
			}

			setImportState({
				isImporting: false,
				isSuccess: true,
				importedCount: importData.length,
			});
			onImportSuccess?.(true);
		} catch (error: any) {
			setImportState((prev) => ({ ...prev, isImporting: false }));
			const errorMessage =
				error.response?.data?.message ||
				error.message ||
				"Failed to import data. Please try again.";
			alert(`Import failed: ${errorMessage}`);
		}
	};

	useEffect(() => {
		const validateData = async () => {
			if (
				!uploadedFile ||
				!mappingData ||
				mappingData.mappingRows.length === 0 ||
				isLoadingAttributes
			) {
				return;
			}

			setIsValidating(true);
			try {
				const fileResult = await parseFile(
					uploadedFile,
					mappingData.selectedSheet || undefined
				);
				if (fileResult.error || fileResult.data.length === 0) {
					return;
				}

				const validationRows: ValidationRow[] = [];
				let missingDataCount = 0;
				let readyToImportCount = 0;

				const mappedFields = mappingData.mappingRows.filter(
					(row) => row.migranium && row.migranium.trim() !== ""
				);
				const requiredFields = getRequiredFields();
				fileResult.data.forEach((dataRow, index) => {
					const validationRow: ValidationRow = {
						id: index + 1,
						excelCell: `B${index + 2}`,
						errors: [],
						visualErrors: [],
						wasProblematic: false,
					};

					let hasErrors = false;

					mappedFields.forEach((mappingRow) => {
						const excelColumnIndex = mappingRow.id - 1;
						const cellValue = dataRow[excelColumnIndex];
						const fieldKey = mappingRow.migranium;
						const fieldType = getFieldValidationType(fieldKey);

						validationRow[fieldKey] = cellValue || "";

						if (
							requiredFields.includes(fieldKey) &&
							(!cellValue || cellValue.toString().trim() === "")
						) {
							validationRow.errors.push(fieldKey);
							validationRow.visualErrors.push(fieldKey);
							hasErrors = true;
						}

						if (cellValue && cellValue.toString().trim() !== "") {
							let isValidFormat = true;
							const cellValueStr = cellValue.toString().trim();

							switch (fieldType) {
								case "email":
									isValidFormat = validateEmail(cellValueStr);
									break;
								case "phone":
									isValidFormat =
										validatePhoneNumber(cellValueStr);
									break;
								case "date":
									isValidFormat = validateDate(cellValueStr);
									break;
								case "number":
									isValidFormat =
										validateNumber(cellValueStr);
									break;
								case "boolean":
									isValidFormat =
										validateBoolean(cellValueStr);
									break;
								default:
									isValidFormat = true;
									break;
							}

							if (!isValidFormat) {
								validationRow.errors.push(fieldKey);
								validationRow.visualErrors.push(fieldKey);
								hasErrors = true;
							}
						}
					});

					if (hasErrors) {
						validationRow.wasProblematic = true;
					}

					validationRows.push(validationRow);

					if (hasErrors) {
						missingDataCount++;
					} else {
						readyToImportCount++;
					}
				});

				setValidationData({
					readyToImport: readyToImportCount,
					missingDataConflicts: missingDataCount,
					duplicateDataConflicts: 0,
					showMissingData: missingDataCount > 0,
					showDuplicateData: false,
					validationRows: validationRows,
					discardedMissingDataRows: new Set(),
					discardedDuplicateDataRows: new Set(),
				});
			} catch (error) {
				console.error("Error validating data:", error);
			} finally {
				setIsValidating(false);
			}
		};

		validateData();
	}, [uploadedFile, mappingData, businessAttributes, isLoadingAttributes]);

	useEffect(() => {
		const problematicRows = validationData.validationRows.filter(
			(row) => row.wasProblematic
		);
		const rowsReady = validationData.validationRows.filter(
			(row) => row.errors.length === 0
		);

		setValidationData((prev) => ({
			...prev,
			missingDataConflicts: problematicRows.length,
			readyToImport: rowsReady.length,
			showMissingData:
				problematicRows.length > 0 ? prev.showMissingData : false,
		}));
	}, [validationData.validationRows]);

	const validRowsCount = validationData.validationRows.filter(
		(row) => row.errors.length === 0
	).length;

	const conflictedRowsAreHidden =
		(validationData.missingDataConflicts > 0 &&
			!validationData.showMissingData) ||
		(validationData.duplicateDataConflicts > 0 &&
			!validationData.showDuplicateData);

	const allErrorsResolved =
		validationData.validationRows.every((row) => row.errors.length === 0) ||
		(validRowsCount > 0 && conflictedRowsAreHidden);

	const missingDataRows = validationData.validationRows.filter(
		(row) => row.wasProblematic
	);

	const getMappedFields = () => {
		if (!mappingData) return [];
		return mappingData.mappingRows.filter(
			(row) => row.migranium && row.migranium.trim() !== ""
		);
	};

	const mappedFields = getMappedFields();

	const handleFieldUpdate = (
		rowId: number,
		fieldKey: string,
		newValue: string
	) => {
		setValidationData((prev) => ({
			...prev,
			validationRows: prev.validationRows.map((row) => {
				if (row.id === rowId) {
					const updatedRow = { ...row, [fieldKey]: newValue };
					const requiredFields = getRequiredFields();
					let newVisualErrors = [...row.visualErrors];

					newVisualErrors = newVisualErrors.filter(
						(error) => error !== fieldKey
					);

					let newErrors: string[] = [];

					const mappedFields =
						mappingData?.mappingRows.filter(
							(mappingRow) =>
								mappingRow.migranium &&
								mappingRow.migranium.trim() !== ""
						) || [];

					mappedFields.forEach((mappingRow) => {
						const fieldName = mappingRow.migranium;
						const fieldType = getFieldValidationType(fieldName);
						const fieldValue =
							fieldName === fieldKey
								? newValue
								: updatedRow[fieldName] || "";
						if (
							requiredFields.includes(fieldName) &&
							(!fieldValue || fieldValue.toString().trim() === "")
						) {
							newErrors.push(fieldName);
							if (fieldName !== fieldKey) {
								newVisualErrors.push(fieldName);
							}
						}
						if (fieldValue && fieldValue.toString().trim() !== "") {
							let isValidFormat = true;
							const fieldValueStr = fieldValue.toString().trim();

							switch (fieldType) {
								case "email":
									isValidFormat =
										validateEmail(fieldValueStr);
									break;
								case "phone":
									isValidFormat =
										validatePhoneNumber(fieldValueStr);
									break;
								case "date":
									isValidFormat = validateDate(fieldValueStr);
									break;
								case "number":
									isValidFormat =
										validateNumber(fieldValueStr);
									break;
								case "boolean":
									isValidFormat =
										validateBoolean(fieldValueStr);
									break;
								default:
									isValidFormat = true;
									break;
							}

							if (!isValidFormat) {
								newErrors.push(fieldName);
								newVisualErrors.push(fieldName);
							}
						}
					});

					updatedRow.errors = newErrors;
					updatedRow.visualErrors = newVisualErrors;
					return updatedRow;
				}
				return row;
			}),
		}));
	};

	const handleDeleteRow = (rowId: number) => {
		setValidationData((prev) => ({
			...prev,
			validationRows: prev.validationRows.filter(
				(row) => row.id !== rowId
			),
		}));
	};

	if (importState.isSuccess) {
		return (
			<div className="flex w-full flex-col items-center justify-center gap-4 px-6 py-8">
				<ImportSuccessCard
					patientsCount={importState.importedCount}
					onImportAgain={onImportAgain || (() => {})}
					onDone={onDone || (() => {})}
				/>
			</div>
		);
	}

	return (
		<div className="flex min-h-[calc(100vh-200px)] w-full flex-col">
			<div className="flex flex-1 flex-col items-start justify-start gap-4 px-6 py-4">
				<SelectSheetInfoCard
					onNext={onPopulateData || (() => {})}
					selectedSheet={
						mappingData?.selectedSheet || "No sheet selected"
					}
					sheets={mappingData?.availableSheets || []}
					buttonText="Populate data"
				/>

				<div className="flex w-full flex-col items-start justify-start gap-4">
					{isValidating ? (
						<div className="flex w-full items-center justify-start gap-3 rounded-lg border-b border-gray-200 bg-white p-4">
							<div className="flex flex-1 flex-col items-start justify-start gap-1">
								<div className="text-sm font-medium text-blue-600">
									Validating data...
								</div>
								<div className="text-xs text-gray-500">
									Please wait while we validate your data.
								</div>
							</div>
						</div>
					) : validationData.readyToImport > 0 ? (
						<div className="flex w-full items-center justify-start gap-3 rounded-lg border-b border-gray-200 bg-white p-4">
							<div className="flex flex-1 flex-col items-start justify-start gap-1">
								<div className="text-sm font-medium text-green-600">
									{validationData.readyToImport} data is ready
									to import
								</div>
								<div className="text-xs text-gray-500">
									These data is perfectly fine to import in
									system.
								</div>
							</div>
						</div>
					) : null}
					{validationData.missingDataConflicts > 0 && (
						<div className="flex w-full flex-col items-start justify-start gap-3 rounded-lg bg-white p-4">
							<div className="flex w-full items-center justify-between border-b border-gray-200 pb-3">
								<div className="flex flex-1 flex-col items-start justify-start gap-1">
									<div className="text-sm font-medium text-red-600">
										{validationData.missingDataConflicts}{" "}
										Conflicts: Missing Data
									</div>
									<div className="text-xs text-gray-500">
										Need to resolve before importing these
										data to system.
									</div>
								</div>
								<div className="flex items-center gap-2">
									<button
										onClick={() => {
											const missingDataRowIds =
												validationData.validationRows
													.filter(
														(row) =>
															row.errors.length >
															0
													)
													.map((row) => row.id);

											setValidationData((prev) => ({
												...prev,
												showMissingData: false,
												discardedMissingDataRows:
													new Set(missingDataRowIds),
											}));
										}}
										className="flex h-9 items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-medium text-gray-900 hover:bg-gray-50"
									>
										<Trash2 className="h-3 w-3" />
										Discard conflicted data
									</button>
									<button
										onClick={() =>
											setValidationData((prev) => ({
												...prev,
												showMissingData:
													!prev.showMissingData,

												discardedMissingDataRows:
													prev.showMissingData
														? prev.discardedMissingDataRows
														: new Set(),
											}))
										}
										className={`flex h-9 w-9 items-center justify-center rounded-md transition-transform hover:bg-gray-100 ${
											validationData.showMissingData
												? "rotate-180"
												: ""
										}`}
									>
										<ChevronDown className="h-3 w-3" />
									</button>
								</div>
							</div>

							{validationData.showMissingData && (
								<div className="w-full overflow-x-auto">
									<table className="w-full border-collapse">
										<thead>
											<tr className="border-b border-gray-200">
												<th className="w-12 px-2 py-3 text-left">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
														#
													</div>
												</th>
												<th className="w-20 px-2 py-3 text-left">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
														Excel Cell
													</div>
												</th>
												{mappedFields.map((field) => {
													const isRequired =
														getRequiredFields().includes(
															field.migranium
														);
													return (
														<th
															key={field.id}
															className="min-w-[150px] px-2 py-3 text-left"
														>
															<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
																{field.excel}{" "}
																{isRequired
																	? "*"
																	: ""}
															</div>
														</th>
													);
												})}
												<th className="w-12 px-2 py-3 text-center">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500"></div>
												</th>
											</tr>
										</thead>
										<tbody>
											{missingDataRows.map((row) => (
												<tr
													key={row.id}
													className="border-t border-gray-200"
												>
													<td className="w-12 px-2 py-3">
														<div className="font-['Inter'] text-sm leading-tight font-medium text-gray-900">
															{row.id}
														</div>
													</td>
													<td className="w-20 px-2 py-3">
														<div className="font-['Inter'] text-sm leading-tight font-normal text-gray-500">
															{row.excelCell}
														</div>
													</td>
													{mappedFields.map(
														(field) => {
															const hasVisualError =
																row.visualErrors.includes(
																	field.migranium
																);
															const fieldValue =
																row[
																	field
																		.migranium
																] || "";

															return (
																<td
																	key={
																		field.id
																	}
																	className="min-w-[150px] px-2 py-3"
																>
																	<div className="relative">
																		<input
																			type="text"
																			value={
																				fieldValue
																			}
																			onChange={(
																				e
																			) =>
																				handleFieldUpdate(
																					row.id,
																					field.migranium,
																					e
																						.target
																						.value
																				)
																			}
																			className={`h-9 w-full rounded-md border px-3 py-2 text-xs focus:ring-2 focus:ring-blue-500 focus:outline-none ${
																				hasVisualError
																					? "border-red-500 bg-red-50 text-red-500"
																					: "border-gray-300 text-gray-900"
																			}`}
																			placeholder={`Enter ${field.excel.toLowerCase()}`}
																		/>
																	</div>
																</td>
															);
														}
													)}
													<td className="w-12 px-2 py-3 text-center">
														<button
															onClick={() =>
																handleDeleteRow(
																	row.id
																)
															}
															className="flex h-6 w-6 items-center justify-center text-gray-500 transition-colors hover:text-red-500"
														>
															<MinusCircle className="h-4 w-4" />
														</button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							)}
						</div>
					)}
					{validationData.duplicateDataConflicts > 0 && (
						<div className="inline-flex items-center justify-start gap-3 self-stretch border-t border-gray-200 pb-3">
							<div className="inline-flex flex-1 flex-col items-start justify-start gap-1">
								<div className="justify-center self-stretch font-['Inter'] text-sm leading-tight font-medium text-red-600">
									{validationData.duplicateDataConflicts}{" "}
									Conflicts: Duplicate Data
								</div>
								<div className="justify-start self-stretch font-['Inter'] text-xs leading-none font-normal text-gray-500">
									Need to resolve before importing these data
									to system.
								</div>
							</div>
							<button
								onClick={() => {
									const duplicateDataRowIds =
										validationData.validationRows
											.filter(
												(row) => row.errors.length > 0
											)
											.map((row) => row.id);

									setValidationData((prev) => ({
										...prev,
										showDuplicateData: false,
										discardedDuplicateDataRows: new Set(
											duplicateDataRowIds
										),
									}));
								}}
								className="flex h-9 w-32 items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2"
							>
								<Trash2 className="h-3 w-3" />
								<div className="justify-center font-['Inter'] text-xs leading-none font-medium text-gray-900">
									Discard conflicted data
								</div>
							</button>
							<button
								onClick={() =>
									setValidationData((prev) => ({
										...prev,
										showDuplicateData:
											!prev.showDuplicateData,
										discardedDuplicateDataRows:
											prev.showDuplicateData
												? prev.discardedDuplicateDataRows
												: new Set(),
									}))
								}
								className={`flex h-9 w-9 items-center justify-center gap-2 rounded-md px-4 py-2 transition-transform ${
									validationData.showDuplicateData
										? "rotate-180"
										: ""
								}`}
							>
								<ChevronDown className="h-3 w-3" />
							</button>
						</div>
					)}
				</div>

				<div className="mt-auto flex w-full items-center justify-between gap-3 bg-white px-6 py-4">
					<button
						onClick={onBack}
						className="flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2 text-xs font-medium text-gray-900 hover:bg-gray-100"
					>
						Back
					</button>
					<div className="flex items-center gap-3">
						<button
							onClick={onTest}
							className="flex h-9 items-center justify-center gap-2 rounded-md border border-[#005893] bg-white px-4 py-2 text-xs font-medium text-[#005893] hover:bg-blue-50"
						>
							Test
						</button>
						<button
							onClick={handleImport}
							disabled={
								importState.isImporting || !allErrorsResolved
							}
							className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 text-xs font-medium text-white hover:bg-[#004a7a] disabled:cursor-not-allowed disabled:opacity-50"
						>
							{importState.isImporting
								? "Importing..."
								: validRowsCount > 0 &&
									  validRowsCount <
											validationData.validationRows.length
									? `Import ${validRowsCount} ${validRowsCount === 1 ? "Record" : "Records"}`
									: "Import"}
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
