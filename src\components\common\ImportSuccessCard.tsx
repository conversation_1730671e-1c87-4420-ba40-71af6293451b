import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface ImportSuccessCardProps {
	patientsCount: number;
	onImportAgain: () => void;
	onDone: () => void;
}

export function ImportSuccessCard({ 
	patientsCount, 
	onImportAgain, 
	onDone 
}: ImportSuccessCardProps) {
	return (
		<div className="self-stretch inline-flex flex-col justify-center items-center gap-8">
			<div className="self-stretch flex flex-col justify-start items-center gap-11">
				{/* Success Icon */}
				<div className="p-8 bg-blue-100 rounded-full inline-flex justify-start items-center gap-2.5 overflow-hidden">
					<div className="w-12 h-12 relative overflow-hidden flex items-center justify-center">
						<Check className="w-8 h-8 text-[#005893] stroke-[3]" />
					</div>
				</div>
				
				{/* Success Message */}
				<div className="w-full max-w-72 flex flex-col justify-start items-center gap-3">
					<div className="text-center justify-start text-gray-900 text-xl font-semibold font-['Inter'] leading-loose">
						{patientsCount} Patients Added
					</div>
					<div className="self-stretch text-center justify-start text-gray-600 text-sm font-normal font-['Inter'] leading-tight">
						Multiple patients has been added successfully.
					</div>
				</div>
			</div>
			
			{/* Action Buttons */}
			<div className="self-stretch inline-flex justify-center items-start gap-3">
				<Button
					onClick={onImportAgain}
					variant="outline"
					className="h-9 px-4 py-2 bg-gray-100 rounded-md flex justify-center items-center gap-2 border-gray-300 hover:bg-gray-200"
				>
					<span className="justify-center text-gray-900 text-xs font-medium font-['Inter'] leading-none">
						Import Patients Again
					</span>
				</Button>
				<Button
					onClick={onDone}
					className="w-20 h-9 px-4 py-2 bg-[#005893] rounded-md flex justify-center items-center gap-2 hover:bg-[#004a7a]"
				>
					<span className="justify-center text-white text-xs font-medium font-['Inter'] leading-none">
						Done
					</span>
				</Button>
			</div>
		</div>
	);
}
