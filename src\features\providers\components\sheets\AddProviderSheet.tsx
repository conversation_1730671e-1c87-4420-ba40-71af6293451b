import { useState } from "react";
import { X, Upload } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { Textarea } from "@/components/ui/textarea";
import { Uploader } from "@/components/common/Uploader";
import type { CreateProviderStationRequest } from "@/features/locations/types";
import type { UploadedFile } from "@/components/common/Uploader/types";

interface AddProviderSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: CreateProviderStationRequest & { imageFile?: File }) => Promise<void>;
}

export function AddProviderSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddProviderSheetProps) {
	const [formData, setFormData] = useState<CreateProviderStationRequest>({
		name: "",
		image: "",
		description: "",
		service_provider_name: "",
		service_provider_email: "",
		service_provider_phone: "",
	});
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

	const handleInputChange = (field: keyof CreateProviderStationRequest, value: string) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleImageChange = (files: File[]) => {
		if (files.length > 0) {
			const file = files[0];
			setSelectedFile(file);
			
			// Create UploadedFile object for display
			const uploadedFile: UploadedFile = {
				id: `file-${Date.now()}`,
				name: file.name,
				size: file.size,
				type: file.type,
				preview: URL.createObjectURL(file),
			};
			setUploadedFiles([uploadedFile]);
			
			setFormData((prev) => ({ ...prev, image: "" })); // Clear image URL until upload
		} else {
			setSelectedFile(null);
			setUploadedFiles([]);
			setFormData((prev) => ({ ...prev, image: "" }));
		}
	};

	const handleFileRemove = (fileId: string) => {
		// Clean up the object URL to prevent memory leaks
		const fileToRemove = uploadedFiles.find((file) => file.id === fileId);
		if (fileToRemove?.preview) {
			URL.revokeObjectURL(fileToRemove.preview);
		}
		
		setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
		setSelectedFile(null);
		setFormData((prev) => ({ ...prev, image: "" }));
	};

	const handleFileEdit = (fileId: string, newName: string) => {
		setUploadedFiles((prev) =>
			prev.map((file) =>
				file.id === fileId ? { ...file, name: newName } : file
			)
		);
	};

	const isFormValid = () => {
		return (
			formData.name.trim() &&
			formData.service_provider_name.trim() &&
			formData.service_provider_email.trim() &&
			formData.service_provider_phone.trim()
		);
	};

	const resetForm = () => {
		// Clean up object URLs
		uploadedFiles.forEach((file) => {
			if (file.preview) {
				URL.revokeObjectURL(file.preview);
			}
		});
		
		setFormData({
			name: "",
			image: "",
			description: "",
			service_provider_name: "",
			service_provider_email: "",
			service_provider_phone: "",
		});
		setSelectedFile(null);
		setUploadedFiles([]);
	};

	const handleSubmit = async () => {
		if (!isFormValid()) {
			return;
		}

		setIsSubmitting(true);
		try {
			const submitData = {
				...formData,
				...(selectedFile && { imageFile: selectedFile }),
			};
			
			await onSubmit?.(submitData);
			
			resetForm();
			onOpenChange(false);
		} catch (error) {
			console.error("Error submitting provider:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleCancel = () => {
		resetForm();
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-8 py-9 sm:max-w-[832px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-lg font-semibold">
							Add New Provider
						</SheetTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6 rounded-sm"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
				</SheetHeader>

				<div className="mt-8 space-y-6">
					{/* Station Details Section */}
					<div className="space-y-4">
						<h3 className="text-base font-semibold">Station Details</h3>
						
						{/* Station Name */}
						<div className="space-y-2">
							<label className="text-sm font-medium">
								Station Name <span className="text-red-500">*</span>
							</label>
							<InputText
								placeholder="Enter station name"
								value={formData.name}
								onChange={(e) => handleInputChange("name", e.target.value)}
								className="w-full"
							/>
						</div>

						{/* Description */}
						<div className="space-y-2">
							<label className="text-sm font-medium">Description</label>
							<Textarea
								placeholder="Enter station description (optional)"
								value={formData.description}
								onChange={(e) => handleInputChange("description", e.target.value)}
								className="w-full"
								rows={3}
							/>
						</div>

						{/* Image Upload */}
						<div className="space-y-2">
							<label className="text-sm font-medium">Station Image</label>
							<div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
								<Uploader
									onFilesChange={handleImageChange}
									onFileRemove={handleFileRemove}
									onFileEdit={handleFileEdit}
									maxFiles={1}
									accept=".png,.jpg,.jpeg,.gif,.webp"
									files={uploadedFiles}
									className="w-full"
								>
									<div className="text-center">
										<Upload className="mx-auto h-12 w-12 text-gray-400" />
										<div className="mt-4">
											<Button
												type="button"
												variant="outline"
												className="cursor-pointer"
											>
												Upload Image
											</Button>
										</div>
										<p className="mt-2 text-sm text-gray-500">
											PNG, JPG, GIF up to 10MB
										</p>
									</div>
								</Uploader>
							</div>
						</div>
					</div>

					{/* Service Provider Details Section */}
					<div className="space-y-4">
						<h3 className="text-base font-semibold">Service Provider Details</h3>
						
						{/* Provider Name */}
						<div className="space-y-2">
							<label className="text-sm font-medium">
								Provider Name <span className="text-red-500">*</span>
							</label>
							<InputText
								placeholder="Enter provider name"
								value={formData.service_provider_name}
								onChange={(e) => handleInputChange("service_provider_name", e.target.value)}
								className="w-full"
							/>
						</div>

						{/* Provider Email */}
						<div className="space-y-2">
							<label className="text-sm font-medium">
								Email Address <span className="text-red-500">*</span>
							</label>
							<InputText
								type="email"
								placeholder="Enter email address"
								value={formData.service_provider_email}
								onChange={(e) => handleInputChange("service_provider_email", e.target.value)}
								className="w-full"
							/>
						</div>

						{/* Provider Phone */}
						<div className="space-y-2">
							<label className="text-sm font-medium">
								Phone Number <span className="text-red-500">*</span>
							</label>
							<InputText
								type="tel"
								placeholder="Enter phone number"
								value={formData.service_provider_phone}
								onChange={(e) => handleInputChange("service_provider_phone", e.target.value)}
								className="w-full"
							/>
						</div>
					</div>
				</div>

				<SheetFooter className="mt-8 gap-3 pt-6">
					<Button
						variant="outline"
						onClick={handleCancel}
						disabled={isSubmitting}
						className="flex-1"
					>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						disabled={!isFormValid() || isSubmitting}
						className="flex-1"
					>
						{isSubmitting ? "Creating..." : "Create Provider"}
					</Button>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
