import { useState,type FormEvent, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
// import SigninWIthGoogle from "@/components/Signup/google/SigninWIthGoogle";
// import SignInWithMicrosoft from "@/components/Signup/azure/SignInWithMicrosoft";
import { Key } from "lucide-react";
import { LuEye, LuEyeOff } from "react-icons/lu";
import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import { InputText } from "@/components/common/InputText";
import { Checkbox } from "@/components/common/Checkbox";
import { Link } from "react-router";
import { Button } from "@/components/ui/button";
import VerifyEmail from "@/components/VerifyEmail";
import type { TenantConfig } from "@/types/tenant";
import useUserStore from "@/stores/useUserStore";
import { useMutation } from "@tanstack/react-query";
import { loginUser } from "@/lib/api/auth";
import { AxiosError } from "axios";
import { useSearchParams } from "react-router";

interface TenantSpecificLoginFormProps {
	tenantConfig: TenantConfig;
	onSwitchToStandard: () => void;
}

export default function TenantSpecificLoginForm({
	tenantConfig,
	onSwitchToStandard,
}: TenantSpecificLoginFormProps) {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [passwordType, setPasswordType] = useState("password");
	const [error, setError] = useState<string | null>(null);
	const [showVerifyEmailModal, setShowVerifyEmailModal] = useState(false);
	// const [showCreateAccountOnSSO, setShowCreateAccountOnSSO] = useState(false);
	// const [SSOToken, setSSOToken] = useState<{
	// 	type: "microsoft" | "google";
	// 	token: string | null;
	// } | null>(null);

	// Get URL search parameters
	const [searchParams] = useSearchParams();
	useEffect(() => {
		const shouldShowVerifyEmail = searchParams.get('showVerifyEmail');
		const emailFromParams = searchParams.get('email');
		
		if (shouldShowVerifyEmail === 'true' && emailFromParams) {
			setEmail(emailFromParams);
			setShowVerifyEmailModal(true);
			// Clean up URL parameters
			const newUrl = new URL(window.location.href);
			newUrl.searchParams.delete('showVerifyEmail');
			newUrl.searchParams.delete('email');
			window.history.replaceState({}, '', newUrl.toString());
		}
	}, [searchParams]);

	const loginUserMutation = useMutation({
		mutationFn: loginUser,
		onSuccess: (data) => {
			handleLoginSuccess(data);
			setShowVerifyEmailModal(
				Boolean(
					!("twoFactor" in data.data) &&
						!data.data.is_email_verified
				)
			);
		},
		onError: (error: AxiosError) => {
			if (error.response?.status === 422) {
				const errorData = error.response.data as any;
				if (errorData?.errors && Array.isArray(errorData.errors)) {
					// Display the first error message
					setError(errorData.errors[0]);
				} else if (errorData?.message) {
					setError(errorData.message);
				} else {
					setError("Your login details don't match our records. Please double-check and try again.");
				}
			} else {
				// Handle other errors
				const errorMessage = (error.response?.data as any)?.message || "An error occurred. Please try again later.";
				setError(errorMessage);
			}
		},
	});
	const rememberAuth = useUserStore((s: any) => s.rememberAuth);
	const setRememberAuth = useUserStore((s: any) => s.setRememberAuth);
	const setMfaUser = useUserStore((s: any) => s.setMfaUser);
	const setUser = useUserStore((s: any) => s.setUser);
	const handleLoginSuccess = useHandleLoginSuccess();

	const handleSubmit = async (e: FormEvent) => {
		e.preventDefault();
		setError(null);

		setMfaUser(null);
		setUser(null);
		loginUserMutation.mutate({
			email: email,
			password: password,
		});
	};

	const handleSSOLogin = async () => {
		if (!tenantConfig.ssoLoginUrl) {
			setError("SSO login URL not configured");
			return;
		}
		window.location.href = tenantConfig.ssoLoginUrl;
	};

	return (
		<div className="flex flex-col items-center justify-center gap-y-6">
			<h3 className="text-[32px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
				Welcome to {tenantConfig.name}&apos;s Admin Portal
			</h3>
			<div className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]">
				{/* <div className="flex flex-col space-y-2 px-8">
					{tenantConfig.customLoginMessage ? (
						<p className="font-normal tracking-[-1%] text-[#858C95]">
							{tenantConfig.customLoginMessage}
						</p>
					) : (
						<a
							href={LANDING_ENVIRONMENT_LINK + "/sign-up"}
							className="font-normal tracking-[-1%] text-[#858C95]"
						>
							Don&apos;t have an account?{" "}
							<span className="text-[#195388]">Sign up</span>
						</a>
					)}
				</div> */}

				<div className="flex flex-col items-center gap-y-2">
					{tenantConfig.logo && (
						<div className="flex justify-center px-8">
							<img
								src={tenantConfig.logo}
								alt={`${tenantConfig.name} logo`}
								width={150}
								height={50}
							/>
						</div>
					)}
					<p className="text-lg font-medium text-main-1">
						Log in using {tenantConfig.name}&apos;s credentials.
					</p>
				</div>

				<form
					onSubmit={handleSubmit}
					className="flex flex-col space-y-6"
				>
					{tenantConfig.showPasswordLogin && (
						<div className="flex flex-col space-y-6 px-8">
							<div className="space-y-1.5">
								<Label
									htmlFor="email"
									className="text-[#323539]"
								>
									Email Address{" "}
									<span className="text-[#c9312c]">*</span>
								</Label>
								<Input
									id="email"
									type="email"
									value={email}
									onChange={(e) => setEmail(e.target.value)}
									max={254}
									placeholder="Enter your email address"
									required
								/>
							</div>

							<div className="space-y-1.5">
								<Label
									htmlFor="password"
									className="text-[#323539]"
								>
									Password{" "}
									<span className="text-[#c9312c]">*</span>
								</Label>
								<InputText
									id="password"
									type={passwordType}
									value={password}
									onChange={(e) =>
										setPassword(e.target.value)
									}
									variant="with-icon"
									icon={passwordType === "password" ? (
										<LuEyeOff
											onClick={() =>
												setPasswordType(
													passwordType ===
														"password"
														? "text"
														: "password"
												)
											}
											className="cursor-pointer text-main-1"
										/>
									) : (
										<LuEye
											onClick={() =>
												setPasswordType(
													passwordType ===
														"password"
														? "text"
														: "password"
												)
											}
											className="cursor-pointer text-main-1"
										/>
									)}
									placeholder="Enter your password"
									required
								/>
							</div>
						</div>
					)}

					{error && (
						<div className="px-8">
							<div className="rounded-md bg-red-50 border border-red-200 p-3">
								<div className="flex">
									<div className="flex-shrink-0">
										<svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
											<path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
										</svg>
									</div>
									<div className="ml-3">
										<p className="text-sm text-red-800">
											{error}
										</p>
									</div>
								</div>
							</div>
						</div>
					)}

					{tenantConfig.showPasswordLogin && (
						<div className="flex items-center justify-between space-x-3 px-8">
							<div
								className="flex items-center space-x-1.5"
								onClick={() =>
									setRememberAuth({
										...rememberAuth,
										rememberMe: !rememberAuth?.rememberMe,
									})
								}
							>
								<Checkbox
									onCheckedChange={() =>
										setRememberAuth({
											...rememberAuth,
											rememberMe:
												!rememberAuth?.rememberMe,
										})
									}
									checked={rememberAuth?.rememberMe}
									id="remember-me"
									className="h-4 w-4 rounded-sm border-[#323539]"
								/>
								<Label>Remember me for the next 30 days</Label>
							</div>
							<Link
								to={"/forgot-password"}
								className="font-normal tracking-[-1%] underline"
							>
								Forgot Password?
							</Link>
						</div>
					)}

					<div className="flex flex-col items-stretch space-y-4 bg-[#FAFBFC] px-8 pb-4 pt-[18px]">
						{tenantConfig.showPasswordLogin && (
							<Button
								disabled={loginUserMutation.isPending}
								loading={loginUserMutation.isPending}
								// loaderSize={20}
								className="h-10 w-full text-white"
								type="submit"
							>
								Sign in
							</Button>
						)}

						<p className="text-center text-sm text-[#858C95]">
							{tenantConfig.showPasswordLogin
								? "Or Sign in With"
								: "Sign in With"}
						</p>

						<div className="flex sm:space-x-2 msm:flex-col msm:space-y-2">
							{/* {!tenantConfig.showGoogleLogin && (
								<SigninWIthGoogle
									type="sign-in"
									setSSOToken={setSSOToken}
									setShowCreateAccountOnSSO={
										setShowCreateAccountOnSSO
									}
								/>
							)} */}

							{/* {!tenantConfig.showMicrosoftLogin && (
								<SignInWithMicrosoft
									type="sign-in"
									setSSOToken={setSSOToken}
									setShowCreateAccountOnSSO={
										setShowCreateAccountOnSSO
									}
								/>
							)} */}

							{tenantConfig.showSSOLogin && (
								<button
									type="button"
									onClick={handleSSOLogin}
									className="flex h-10 w-full items-center justify-center gap-x-2 rounded-md border border-[#E9EAEB] bg-white px-3 text-sm font-medium text-[#323539] hover:bg-gray-50 md:w-fit"
								>
									<Key
										size={14}
										className="rotate-[-270] scale-x-[-1]"
									/>
									{tenantConfig.ssoButtonText || "SSO"}
								</button>
							)}
						</div>

						<div className="mt-4 text-center">
							<p className="text-[#858C95]">
								Not a {tenantConfig.name} member?{" "}
								<button
									type="button"
									className="font-normal tracking-[-1%] text-[#195388]"
									onClick={onSwitchToStandard}
								>
									Sign in with email
								</button>
							</p>
						</div>
					</div>
				</form>

				{/* <RequestIsLoading isWhite size={20} isLoading={isLoading} /> */}

				<VerifyEmail
					email={email}
					showVerifyEmailModal={showVerifyEmailModal}
					setShowVerifyEmailModal={setShowVerifyEmailModal}
				/>

			</div>
		</div>
	);
}
