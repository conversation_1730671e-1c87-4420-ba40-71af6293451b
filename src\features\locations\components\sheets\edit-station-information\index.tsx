import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { X, Edit, Trash2, UserPlus2, <PERSON>ci<PERSON> } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>nt,
	<PERSON>etHeader,
	<PERSON>et<PERSON><PERSON>le,
	<PERSON>et<PERSON>ooter,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { MultiAsyncSelect } from "@/components/common/MultiAsyncSelect";
import { Uploader } from "@/components/common/Uploader";
import { cn } from "@/lib/utils";
import type { UploadedFile } from "@/components/common/Uploader/types";
import { RefactorMultiSelect } from "@/pages/schedules/components/custom-select";

// Schema for edit station form
const editStationSchema = z.object({
	stationName: z
		.string()
		.min(1, "Station name is required")
		.max(100, "Station name must be less than 100 characters"),
	description: z
		.string()
		.max(500, "Description must be less than 500 characters")
		.optional()
		.or(z.literal("")),
	categories: z.array(z.string()),
	logo: z.array(z.any()).optional(),
});

export type EditStationFormData = z.infer<typeof editStationSchema>;

interface StationProvider {
	id: string;
	name: string;
	email: string;
	status: "active" | "pending" | "inactive";
	avatar?: string;
}

interface StationCategory {
	value: string;
	label: string;
}

interface StationData {
	id: string;
	stationName: string;
	description?: string;
	logoUrl?: string;
	categories: string[];
	providers: StationProvider[];
}

interface EditStationInformationSheetProps {
	open: boolean;
	onClose: () => void;
	stationId?: string;
	stationData?: StationData;
	onSave?: (data: EditStationFormData) => Promise<void>;
	onProviderEdit?: (providerId: string) => void;
	onProviderDelete?: (providerId: string) => void;
	onAddProvider?: () => void;
	isLoading?: boolean;
}

// Mock categories for demo - replace with actual API call
const mockCategories: StationCategory[] = [
	{ value: "mental-health", label: "Mental Health Assessment" },
	{ value: "cancer-screening", label: "Cancer Screening" },
	{ value: "referral", label: "Referral Appointment" },
	{ value: "assessments", label: "Assessments" },
	{ value: "report-review", label: "Report Review" },
	{ value: "category-1", label: "Category Name" },
	{ value: "category-2", label: "Category Name" },
	{ value: "category-3", label: "Category Name" },
];

export const EditStationInformationSheet: React.FC<
	EditStationInformationSheetProps
> = ({
	open,
	onClose,
	stationId,
	stationData,
	onSave,
	onProviderEdit,
	onProviderDelete,
	onAddProvider,
	isLoading = false,
}) => {
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
	const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

	const form = useForm<EditStationFormData>({
		resolver: zodResolver(editStationSchema),
		defaultValues: {
			stationName: "",
			description: "",
			categories: [],
			logo: [],
		},
	});

	const {
		handleSubmit,
		setValue,
		watch,
		reset,
		formState: { errors, isSubmitting },
	} = form;

	// Load station data when component mounts or stationData changes
	useEffect(() => {
		if (stationData) {
			reset({
				stationName: stationData.stationName || "",
				description: stationData.description || "",
				categories: stationData.categories || [],
				logo: [],
			});
			setSelectedCategories(stationData.categories || []);

			// Set logo if exists
			if (stationData.logoUrl) {
				setUploadedFiles([
					{
						id: "current-logo",
						name: "Current Logo",
						size: 0,
						type: "image/*",
						url: stationData.logoUrl,
					},
				]);
			}
		}
	}, [stationData, reset]);

	// Handle form submission
	const onSubmit = async (data: EditStationFormData) => {
		try {
			await onSave?.(data);
			onClose();
		} catch (error) {
			console.error("Error saving station information:", error);
		}
	};

	// Handle category selection
	const handleCategoryChange = (selectedValues: string[]) => {
		setSelectedCategories(selectedValues);
		setValue("categories", selectedValues, { shouldValidate: true });
	};

	// Handle file upload
	const handleFilesChange = (files: File[]) => {
		const uploadedFiles: UploadedFile[] = files.map((file, index) => ({
			id: `file-${index}`,
			name: file.name,
			size: file.size,
			type: file.type,
			url: URL.createObjectURL(file),
		}));
		setUploadedFiles(uploadedFiles);
		setValue("logo", uploadedFiles, { shouldValidate: true });
	};

	// Handle provider actions
	const handleProviderEdit = (providerId: string) => {
		onProviderEdit?.(providerId);
	};

	const handleProviderDelete = (providerId: string) => {
		onProviderDelete?.(providerId);
	};

	const handleAddProvider = () => {
		onAddProvider?.();
	};

	// Mock provider data if none provided
	const providers: StationProvider[] = stationData?.providers || [
		{
			id: "1",
			name: "[Provider Name]",
			email: "<EMAIL>",
			status: "pending",
		},
	];

	return (
		<Sheet open={open} onOpenChange={onClose}>
			<SheetContent className="z-[1003] w-full !max-w-[525px] overflow-y-auto [&>button]:hidden">
				{/* Header */}
				<SheetHeader className="flex-row items-center justify-between space-y-0 px-6 pb-6">
					<SheetTitle className="text-2xl font-semibold text-zinc-950">
						Edit Station Information
					</SheetTitle>
					<Button
						variant="ghost"
						size="sm"
						className="h-6 w-6 p-0"
						onClick={onClose}
					>
						<X className="h-4 w-4" />
					</Button>
				</SheetHeader>

				{/* Form Content */}
				<div className="flex flex-1 flex-col gap-6 px-6">
					<Form {...form}>
						<form
							onSubmit={handleSubmit(onSubmit)}
							className="space-y-6"
						>
							{/* Station Name */}
							<FormField
								control={form.control}
								name="stationName"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium text-[#323539]">
											Station name *
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="University of Waterloo, Ontario Laboratory"
												className="h-10"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Location Description */}
							<FormField
								control={form.control}
								name="description"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium text-[#323539]">
											Location description
										</FormLabel>
										<FormControl>
											<Textarea
												{...field}
												placeholder="Lorem Ipsum this will be the expanded description of Ipsum this will be the expanded description of will be the expanded description of the land."
												className="min-h-[114px] resize-none"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Logo Upload */}
							<div className="space-y-2">
								<div className="rounded-lg border border-[rgba(248,248,248,0.74)] p-2">
									<div className="flex items-center gap-6">
										<div className="flex-1">
											<Uploader
												files={[]}
												onFilesChange={
													handleFilesChange
												}
												accept=".svg,.png,.jpg,.jpeg"
												maxFileSize={10 * 1024 * 1024} // 10MB
												uploadText="Drag file here or Upload logo"
												descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
												size="default"
												variant="compact"
												className="border-none"
											/>
										</div>
									</div>
								</div>
							</div>

							{/* Provider Section */}
							<div className="space-y-3">
								{providers.map((provider) => (
									<div
										key={provider.id}
										className="flex items-center gap-4 rounded-lg border border-zinc-200 bg-white p-4"
									>
										<div className="flex h-9 w-9 items-center justify-center rounded-lg bg-[#f8f8f8]">
											<UserPlus2 className="h-[18px] w-[18px] text-gray-600" />
										</div>
										<div className="flex-1">
											<div className="flex items-center gap-2">
												<h4 className="text-[15px] font-bold text-[#323539]">
													{provider.name}
												</h4>
											</div>
											<p className="text-sm text-zinc-500">
												{provider.status === "pending"
													? "Invitation Pending"
													: provider.email}
											</p>
										</div>
										<div className="flex items-center gap-2">
											<Button
												variant="ghost"
												size="sm"
												className="h-8 w-8 p-0"
												onClick={() =>
													handleProviderEdit(
														provider.id
													)
												}
											>
												<Pencil className="h-4 w-4" />
											</Button>
											<Button
												variant="ghost"
												size="sm"
												className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
												onClick={() =>
													handleProviderDelete(
														provider.id
													)
												}
											>
												<Trash2 className="h-5 w-5" />
											</Button>
										</div>
									</div>
								))}
							</div>

							{/* Categories */}
							<div className="space-y-2">
								<Label className="text-xs font-medium text-zinc-900">
									Categories
								</Label>
								<RefactorMultiSelect
									options={mockCategories.map(
										(category) => category.label
									)}
									value={selectedCategories}
									setValue={(value) =>
										handleCategoryChange(value as string[])
									}
									placeholder="Select categories..."
									label="Categories"
									id="categories"
								/>
							</div>
						</form>
					</Form>
				</div>

				{/* Footer */}
				<SheetFooter className="mt-6 flex-row justify-end gap-3 px-6">
					<Button
						variant="outline"
						className="bg-secondary cursor-pointer"
						onClick={onClose}
						disabled={isSubmitting}
					>
						Close
					</Button>
					<Button
						className="cursor-pointer"
						onClick={handleSubmit(onSubmit)}
						disabled={isSubmitting || isLoading}
					>
						{isSubmitting ? "Saving..." : "Save"}
					</Button>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
};

export default EditStationInformationSheet;
