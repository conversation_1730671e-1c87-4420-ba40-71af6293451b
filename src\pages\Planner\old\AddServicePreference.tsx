// components/planner/AddServicePreference.tsx
import React, { useState } from 'react';
import { ChevronLeft, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Import shared components
import { CustomParameterSidebar } from './CustomParameterSidebar';
import { DayScheduleTable } from './DayScheduleTable';
import { FrequencyApplicationModal } from './FrequencyApplicationModal';
import { OccurrenceSelector } from './OccurenceSelector';
import { RuleDurationSelector } from './RuleDurationSelector';

interface CustomParameter {
  id: string;
  label: string;
  enabled: boolean;
}

interface TimeValue {
  hour: string;
  minute: string;
  period: 'AM' | 'PM';
}

interface TimeSlot {
  id: string;
  startTime: TimeValue;
  endTime: TimeValue;
}

interface DaySchedule {
  day: string;
  enabled: boolean;
  timeSlots: TimeSlot[];
  maxFrequency: string;
}

interface Service {
  id: string;
  name: string;
}

interface AddServicePreferenceProps {
  onBack: () => void;
  onSave: (preferenceData: any) => void; // This will trigger conflict check
}

export const AddServicePreference: React.FC<AddServicePreferenceProps> = ({ 
  onBack, 
  onSave 
}) => {
  // Form state
  const [selectedService, setSelectedService] = useState('');
  const [title, setTitle] = useState('');
  const [preferenceType, setPreferenceType] = useState<'availability' | 'restriction'>('availability');
  const [totalMaxFrequency, setTotalMaxFrequency] = useState('25');
  const [frequencyPeriod, setFrequencyPeriod] = useState('Monthly');
  const [occurrence, setOccurrence] = useState<'Daily' | 'Weekly' | 'Monthly' | 'Yearly'>('Daily');
  const [timePeriodType, setTimePeriodType] = useState<'rolling' | 'specific'>('rolling');
  const [duration, setDuration] = useState('20');
  const [durationUnit, setDurationUnit] = useState('Days');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Sample services - replace with actual API data
  const services: Service[] = [
    { id: '1', name: 'General Medical Test' },
    { id: '2', name: 'Blood Work' },
    { id: '3', name: 'Immunization' },
    { id: '4', name: 'Report Reading' },
    { id: '5', name: 'Video Consultation' },
    { id: '6', name: 'General Consultation' },
    { id: '7', name: 'X-Ray' },
    { id: '8', name: 'MRI Scan' },
    { id: '9', name: 'Physical Therapy' },
    { id: '10', name: 'Dental Cleaning' },
  ];

  // Custom parameters state
  const [customParameters, setCustomParameters] = useState<CustomParameter[]>([
    { id: 'selectDays', label: 'Select Days', enabled: true },
    { id: 'setTimes', label: 'Set Times', enabled: true },
    { id: 'setFrequency', label: 'Set Frequency', enabled: true },
    { id: 'setOccurrence', label: 'Set Occurrence', enabled: false },
    { id: 'setTimePeriod', label: 'Set Time Period', enabled: true },
  ]);

  // Days schedule state
  const [daysSchedule, setDaysSchedule] = useState<DaySchedule[]>([
    { 
      day: 'Monday', 
      enabled: true, 
      timeSlots: [{ 
        id: 'mon-1', 
        startTime: { hour: '08', minute: '00', period: 'AM' }, 
        endTime: { hour: '05', minute: '00', period: 'PM' } 
      }], 
      maxFrequency: '' 
    },
    { 
      day: 'Tuesday', 
      enabled: false, 
      timeSlots: [{ 
        id: 'tue-1', 
        startTime: { hour: '08', minute: '00', period: 'AM' }, 
        endTime: { hour: '05', minute: '00', period: 'PM' } 
      }], 
      maxFrequency: '' 
    },
    { 
      day: 'Wednesday', 
      enabled: false, 
      timeSlots: [{ 
        id: 'wed-1', 
        startTime: { hour: '08', minute: '00', period: 'AM' }, 
        endTime: { hour: '05', minute: '00', period: 'PM' } 
      }], 
      maxFrequency: '' 
    },
    { 
      day: 'Thursday', 
      enabled: false, 
      timeSlots: [{ 
        id: 'thu-1', 
        startTime: { hour: '08', minute: '00', period: 'AM' }, 
        endTime: { hour: '05', minute: '00', period: 'PM' } 
      }], 
      maxFrequency: '' 
    },
    { 
      day: 'Friday', 
      enabled: false, 
      timeSlots: [{ 
        id: 'fri-1', 
        startTime: { hour: '08', minute: '00', period: 'AM' }, 
        endTime: { hour: '05', minute: '00', period: 'PM' } 
      }], 
      maxFrequency: '' 
    },
    { 
      day: 'Saturday', 
      enabled: false, 
      timeSlots: [{ 
        id: 'sat-1', 
        startTime: { hour: '08', minute: '00', period: 'AM' }, 
        endTime: { hour: '05', minute: '00', period: 'PM' } 
      }], 
      maxFrequency: '' 
    },
    { 
      day: 'Sunday', 
      enabled: false, 
      timeSlots: [{ 
        id: 'sun-1', 
        startTime: { hour: '08', minute: '00', period: 'AM' }, 
        endTime: { hour: '05', minute: '00', period: 'PM' } 
      }], 
      maxFrequency: '' 
    },
  ]);

  // Occurrence selections
  const [selectedOccurrenceItems, setSelectedOccurrenceItems] = useState<string[]>([]);

  // Modal state
  const [showFrequencyModal, setShowFrequencyModal] = useState(false);

  // Helper functions
  const toggleParameter = (id: string) => {
    setCustomParameters(prev =>
      prev.map(param =>
        param.id === id ? { ...param, enabled: !param.enabled } : param
      )
    );
  };

  const toggleDay = (index: number) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === index ? { ...day, enabled: !day.enabled } : day
      )
    );
  };

  const updateDayTime = (dayIndex: number, slotIndex: number, field: 'startTime' | 'endTime', time: TimeValue) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === dayIndex ? {
          ...day,
          timeSlots: day.timeSlots.map((slot, j) =>
            j === slotIndex ? { ...slot, [field]: time } : slot
          )
        } : day
      )
    );
  };

  const addTimeSlot = (dayIndex: number) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === dayIndex ? {
          ...day,
          timeSlots: [
            ...day.timeSlots,
            {
              id: `${day.day.toLowerCase()}-${day.timeSlots.length + 1}`,
              startTime: { hour: '08', minute: '00', period: 'AM' },
              endTime: { hour: '05', minute: '00', period: 'PM' }
            }
          ]
        } : day
      )
    );
  };

  const removeTimeSlot = (dayIndex: number, slotIndex: number) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === dayIndex ? {
          ...day,
          timeSlots: day.timeSlots.filter((_, j) => j !== slotIndex)
        } : day
      )
    );
  };

  const updateDayFrequency = (index: number, frequency: string) => {
    setDaysSchedule(prev =>
      prev.map((day, i) =>
        i === index ? { ...day, maxFrequency: frequency } : day
      )
    );
  };

  const getEnabledParameter = (id: string) => {
    return customParameters.find(param => param.id === id)?.enabled || false;
  };

  const getCharacterCount = () => title.length;

  const toggleOccurrenceItem = (item: string) => {
    if (selectedOccurrenceItems.includes(item)) {
      setSelectedOccurrenceItems(prev => prev.filter(i => i !== item));
    } else {
      setSelectedOccurrenceItems(prev => [...prev, item]);
    }
  };

  const handleFrequencyModalSave = (data: any) => {
    console.log('Frequency application saved:', data);
  };

  const handleSavePreference = () => {
    // Validation
    if (!selectedService) {
      alert('Please select a service');
      return;
    }

    if (!title.trim()) {
      alert('Please enter a title');
      return;
    }

    // Prepare preference data
    const preferenceData = {
      serviceId: selectedService,
      serviceName: services.find(s => s.id === selectedService)?.name,
      title: title.trim(),
      preferenceType,
      customParameters,
      daysSchedule,
      totalMaxFrequency: getEnabledParameter('setFrequency') ? totalMaxFrequency : null,
      frequencyPeriod: getEnabledParameter('setFrequency') ? frequencyPeriod : null,
      occurrence: getEnabledParameter('setOccurrence') ? occurrence : null,
      selectedOccurrenceItems: getEnabledParameter('setOccurrence') ? selectedOccurrenceItems : null,
      timePeriodType: getEnabledParameter('setTimePeriod') ? timePeriodType : null,
      duration: getEnabledParameter('setTimePeriod') && timePeriodType === 'rolling' ? duration : null,
      durationUnit: getEnabledParameter('setTimePeriod') && timePeriodType === 'rolling' ? durationUnit : null,
      startDate: getEnabledParameter('setTimePeriod') && timePeriodType === 'specific' ? startDate : null,
      endDate: getEnabledParameter('setTimePeriod') && timePeriodType === 'specific' ? endDate : null,
    };

    // Trigger save which will handle conflict detection
    onSave(preferenceData);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-semibold">Add Preference</h1>
            </div>
          </div>
          <Button variant="outline" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>View Schedule</span>
          </Button>
        </div>
      </div>

      <div className="flex h-screen">
        {/* Left Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 p-6">
          <div className="space-y-6">
            {/* Select Service */}
            <div>
              <h3 className="font-medium mb-3">Select Service</h3>
              <Select value={selectedService} onValueChange={setSelectedService as any}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a service" />
                </SelectTrigger>
                <SelectContent>
                  {services.map((service) => (
                    <SelectItem key={service.id} value={service.id}>
                      {service.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Custom Parameter Sidebar */}
            <div>
              <h3 className="font-medium mb-3">Select Preferences for</h3>
              <div className="space-y-2">
                <Button 
                  variant={preferenceType === 'availability' ? 'default' : 'outline'}
                  className="w-full justify-start"
                  onClick={() => setPreferenceType('availability')}
                >
                  Availability
                </Button>
                <Button 
                  variant={preferenceType === 'restriction' ? 'default' : 'outline'}
                  className="w-full justify-start"
                  onClick={() => setPreferenceType('restriction')}
                >
                  Restriction
                </Button>
              </div>
            </div>

            {/* Select Custom Parameters */}
            <div>
              <h3 className="font-medium mb-3">Select Custom Parameters</h3>
              <div className="space-y-2">
                {customParameters.map((param) => (
                  <div 
                    key={param.id} 
                    className={`flex items-center justify-between p-3 border rounded cursor-pointer transition-colors ${
                      param.enabled ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => toggleParameter(param.id)}
                  >
                    <span className="text-sm">{param.label}</span>
                    <div className={`w-4 h-4 rounded-full flex items-center justify-center transition-colors ${
                      param.enabled ? 'bg-blue-600' : 'bg-gray-400'
                    }`}>
                      {param.enabled && <div className="w-2 h-2 bg-white rounded-full" />}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Title */}
            <div>
              <label className="block text-sm font-medium mb-2">Title</label>
              <Input 
                placeholder="Enter Title Here"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="max-w-md"
                maxLength={120}
              />
              <p className="text-sm text-gray-500 mt-1">{getCharacterCount()}/120 characters</p>
            </div>

            {/* Days and Time Configuration */}
            {getEnabledParameter('selectDays') && (
              <DayScheduleTable
                daysSchedule={daysSchedule}
                onDayToggle={toggleDay}
                onTimeSlotChange={updateDayTime}
                onAddTimeSlot={addTimeSlot}
                onRemoveTimeSlot={removeTimeSlot}
                onFrequencyChange={updateDayFrequency}
                showTimes={getEnabledParameter('setTimes')}
                showFrequency={getEnabledParameter('setFrequency')}
              />
            )}

            {/* Total Maximum Frequency */}
            {getEnabledParameter('setFrequency') && (
              <div className="grid grid-cols-3 gap-4 items-end">
                <div>
                  <label className="block text-sm font-medium mb-2">Total Maximum Frequency</label>
                  <Input 
                    value={totalMaxFrequency}
                    onChange={(e) => setTotalMaxFrequency(e.target.value)}
                  />
                </div>
                <div>
                  <Select value={frequencyPeriod} onValueChange={setFrequencyPeriod as any}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Monthly">Monthly</SelectItem>
                      <SelectItem value="Weekly">Weekly</SelectItem>
                      <SelectItem value="Daily">Daily</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button 
                  onClick={() => setShowFrequencyModal(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Add a Preference
                </Button>
              </div>
            )}

            {/* Select Occurrence */}
            {getEnabledParameter('setOccurrence') && (
              <OccurrenceSelector
                occurrence={occurrence}
                onOccurrenceChange={setOccurrence}
                selectedItems={selectedOccurrenceItems}
                onItemToggle={toggleOccurrenceItem}
              />
            )}

            {/* Set Rule Duration */}
            {getEnabledParameter('setTimePeriod') && (
              <RuleDurationSelector
                timePeriodType={timePeriodType}
                onTimePeriodTypeChange={setTimePeriodType}
                duration={duration}
                onDurationChange={setDuration}
                durationUnit={durationUnit}
                onDurationUnitChange={setDurationUnit}
                startDate={startDate}
                onStartDateChange={setStartDate}
                endDate={endDate}
                onEndDateChange={setEndDate}
              />
            )}

            {/* Action Button */}
            <div className="flex justify-end">
              <Button 
                onClick={handleSavePreference} 
                className="bg-blue-600 hover:bg-blue-700"
                disabled={!selectedService || !title.trim()}
              >
                Add
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Frequency Modal */}
      <FrequencyApplicationModal
        isOpen={showFrequencyModal}
        onClose={() => setShowFrequencyModal(false)}
        totalMaxFrequency={totalMaxFrequency}
        frequencyPeriod={frequencyPeriod}
        onSave={handleFrequencyModalSave}
      />

    </div>
  );
};

