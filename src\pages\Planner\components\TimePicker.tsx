import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface TimeValue {
  hour: string;
  minute: string;
  period: 'AM' | 'PM';
}

interface TimePickerProps {
  value: TimeValue;
  onChange: (time: TimeValue) => void;
  disabled?: boolean;
  placeholder?: string;
  width?: string; // New prop for dynamic width
}

export const TimePicker: React.FC<TimePickerProps> = ({ 
  value, 
  onChange, 
  disabled = false,
  placeholder = "Select time",
  width = "w-full" // Default to full width for backward compatibility
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const hours = Array.from({ length: 12 }, (_, i) => (i + 1).toString().padStart(2, '0'));
  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));

  const handleTimeChange = (field: 'hour' | 'minute' | 'period', newValue: string) => {
    onChange({
      ...value,
      [field]: newValue
    });
  };

  const displayTime = value.hour && value.minute && value.period 
    ? `${value.hour}:${value.minute} ${value.period}`
    : placeholder;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className={`justify-start text-left font-normal ${width}`}
          disabled={disabled}
        >
          {displayTime}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-4" align="start">
        <div className="space-y-4">
          {/* Time Display */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold">{value.hour || '00'}</div>
              <div className="text-sm text-gray-500">Hour</div>
            </div>
            <div>
              <div className="text-2xl font-bold">:</div>
            </div>
            <div>
              <div className="text-2xl font-bold">{value.minute || '00'}</div>
              <div className="text-sm text-gray-500">Minute</div>
            </div>
            <div>
              <div className="text-2xl font-bold">{value.period || 'AM'}</div>
            </div>
          </div>
          
          {/* Time Selectors */}
          <div className="grid grid-cols-3 gap-3">
            {/* Hours */}
            <div className="space-y-1 max-h-32 overflow-y-auto">
              <div className="text-xs font-medium text-gray-500 text-center mb-2">Hour</div>
              {hours.map((hour) => (
                <Button
                  key={hour}
                  variant={value.hour === hour ? "default" : "ghost"}
                  size="sm"
                  className="w-full h-8 text-sm"
                  onClick={() => handleTimeChange('hour', hour)}
                >
                  {hour}
                </Button>
              ))}
            </div>
            
            {/* Minutes (every 5 minutes) */}
            <div className="space-y-1 max-h-32 overflow-y-auto">
              <div className="text-xs font-medium text-gray-500 text-center mb-2">Min</div>
              {minutes.filter((_, i) => i % 5 === 0).map((minute) => (
                <Button
                  key={minute}
                  variant={value.minute === minute ? "default" : "ghost"}
                  size="sm"
                  className="w-full h-8 text-sm"
                  onClick={() => handleTimeChange('minute', minute)}
                >
                  {minute}
                </Button>
              ))}
            </div>
            
            {/* AM/PM */}
            <div className="space-y-1">
              <div className="text-xs font-medium text-gray-500 text-center mb-2">Period</div>
              <Button
                variant={value.period === 'AM' ? "default" : "ghost"}
                size="sm"
                className="w-full h-8 text-sm"
                onClick={() => handleTimeChange('period', 'AM')}
              >
                AM
              </Button>
              <Button
                variant={value.period === 'PM' ? "default" : "ghost"}
                size="sm"
                className="w-full h-8 text-sm"
                onClick={() => handleTimeChange('period', 'PM')}
              >
                PM
              </Button>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setIsOpen(false)}>
              Apply
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};