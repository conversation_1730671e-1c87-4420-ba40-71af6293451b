import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Search, SlidersHorizontal } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';


interface Provider {
  id: string;
  name: string;
  location: string;
  locationDetail: string;
  avatar: string;
  status: string;
  isSchedule: boolean;
  isWalkIn: boolean;
}

interface ProviderSelectionProps {
  onBack: () => void;
  onSelectProvider: (provider: Provider) => void;
}

export const ProviderSelection: React.FC<ProviderSelectionProps> = ({ onBack, onSelectProvider }) => {
  const [selectedProviders, setSelectedProviders] = useState<string[]>([]);

  const providers: Provider[] = [
    { id: '1', name: 'Dr. <PERSON>', location: 'Location Name', locationDetail: '[Provider Station Name at Location]', avatar: '/api/placeholder/40/40', status: 'Available Today', isSchedule: true, isWalkIn: false },
    { id: '2', name: 'Dr. Abraham Lincoln', location: 'Location Name', locationDetail: '[Provider Station Name at Location]', avatar: '/api/placeholder/40/40', status: 'Available Today', isSchedule: true, isWalkIn: false },
    { id: '3', name: 'Dr. Abraham Lincoln', location: 'Location Name', locationDetail: '[Provider Station Name at Location]', avatar: '/api/placeholder/40/40', status: 'Available Today', isSchedule: true, isWalkIn: false },
    { id: '4', name: 'Dr. Abraham Lincoln', location: 'Location Name', locationDetail: '[Provider Station Name at Location]', avatar: '/api/placeholder/40/40', status: 'Available Today', isSchedule: true, isWalkIn: false },
    { id: '5', name: 'Dr. Abraham Lincoln', location: 'Location Name', locationDetail: '[Provider Station Name at Location]', avatar: '/api/placeholder/40/40', status: 'Available Today', isSchedule: true, isWalkIn: false },
    { id: '6', name: 'Dr. Abraham Lincoln', location: 'Location Name', locationDetail: '[Provider Station Name at Location]', avatar: '/api/placeholder/40/40', status: 'Available Today', isSchedule: true, isWalkIn: false },
    { id: '7', name: 'Dr. Abraham Lincoln', location: 'Location Name', locationDetail: '[Provider Station Name at Location]', avatar: '/api/placeholder/40/40', status: 'Available Today', isSchedule: true, isWalkIn: false },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={onBack} className="p-2">
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Select a Provider</h1>
              <p className="text-gray-600 mt-1">Select a Provider that you would like to set preferences for below.</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" size="sm">
              <Search className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <SlidersHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Provider List */}
        <div className="space-y-0 bg-white rounded-lg border border-gray-200 overflow-hidden">
          {/* Header row */}
          <div className="border-b border-gray-200 px-6 py-3 bg-gray-50">
            <div className="flex items-center">
              <Checkbox className="mr-4" />
              <div className="text-sm font-medium text-gray-700">Select All</div>
            </div>
          </div>

          {/* Provider rows */}
          {providers.map((provider) => (
            <div
              key={provider.id}
              className="border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer"
              onClick={() => onSelectProvider(provider)}
            >
              <div className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Checkbox />
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={provider.avatar} alt={provider.name} />
                      <AvatarFallback>AL</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium text-gray-900">{provider.name}</h3>
                      <p className="text-sm text-green-600">{provider.status}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-6">
                  <div>
                    <h4 className="font-medium text-gray-900">{provider.location}</h4>
                    <p className="text-sm text-gray-500">{provider.locationDetail}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      ✓ Schedule
                    </Badge>
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                      ✕ Walk-in
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-full">
                      <span className="sr-only">Info</span>
                      <div className="w-4 h-4 rounded-full border border-gray-300 flex items-center justify-center text-xs">?</div>
                    </Button>
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <Button variant="outline" disabled>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="bg-blue-600 text-white">1</Button>
            <Button variant="outline" size="sm">2</Button>
            <Button variant="outline" size="sm">3</Button>
            <span className="text-gray-500">...</span>
          </div>
          <Button variant="outline">
            Next
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
};