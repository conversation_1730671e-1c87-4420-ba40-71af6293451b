import React, { useState } from 'react';
import { Chevron<PERSON><PERSON>t, AlertTriangle, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';

interface ConflictingRule {
  id: string;
  title: string;
  frequency: string;
  occurrence: string;
  availability: string;
  timePeriod: string;
  hasTimeConflict?: boolean;
}

interface RuleConflictsScreenProps {
  newRule: {
    title: string;
    frequency: string;
    occurrence: string;
    availability: string;
    timePeriod: string;
  };
  conflictingRules: ConflictingRule[];
  conflictCount: number;
  onBack: () => void;
  onEdit: () => void;
  onReplace: (selectedRuleIds: string[]) => void;
  onOverride: (selectedRuleIds: string[]) => void;
}

export const RuleConflictsScreen: React.FC<RuleConflictsScreenProps> = ({
  newRule,
  conflictingRules,
  conflictCount,
  onBack,
  onEdit,
  onReplace,
  onOverride
}) => {
  const [selectedRules, setSelectedRules] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showReplaceModal, setShowReplaceModal] = useState(false);
  const [showOverrideModal, setShowOverrideModal] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedRules(conflictingRules.map(rule => rule.id));
    } else {
      setSelectedRules([]);
    }
  };

  const handleSelectRule = (ruleId: string, checked: boolean) => {
    if (checked) {
      setSelectedRules(prev => [...prev, ruleId]);
    } else {
      setSelectedRules(prev => prev.filter(id => id !== ruleId));
      setSelectAll(false);
    }
  };

  const handleDeleteSelected = () => {
    // Handle bulk delete of selected rules
    console.log('Deleting selected rules:', selectedRules);
  };

  const handleReplace = () => {
    if (selectedRules.length === 0) {
      alert('Please select at least one rule to replace');
      return;
    }
    setShowReplaceModal(true);
  };

  const handleOverride = () => {
    if (selectedRules.length === 0) {
      alert('Please select at least one rule to override');
      return;
    }
    setShowOverrideModal(true);
  };

  const confirmReplace = () => {
    setShowReplaceModal(false);
    onReplace(selectedRules);
  };

  const confirmOverride = () => {
    setShowOverrideModal(false);
    onOverride(selectedRules);
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white px-6 py-4">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div className="bg-primary text-white px-3 py-1 rounded text-sm font-medium">
            NEW RULE
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* New Rule Summary */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between border-b pb-4">
            <div className="flex items-center space-x-4">
              <Badge variant="default" className='bg-primary/20 text-primary'>01</Badge>
              <h3 className="text-lg font-medium">{newRule.title}</h3>
            </div>
            <div className="text-sm text-gray-500">Created: 07 June 2025</div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Badge variant="outline" className="bg-primary/20">Frequency</Badge>
                <span className="text-sm">{newRule.frequency}</span>
              </div>
              <div className="flex space-x-2">
                <Badge variant="secondary" className="bg-green-100 text-green-800">Availability</Badge>
                <span className="text-sm">{newRule.availability}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Badge variant="outline" className="bg-gray-100">Occurrence</Badge>
                <span className="text-sm">{newRule.occurrence}</span>
              </div>
              <div className="flex space-x-2">
                <Badge variant="outline" className="bg-gray-100">Time Period</Badge>
                <span className="text-sm">{newRule.timePeriod}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6">
          {/* Detected Conflicts */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6  pt-4">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-semibold">Detected Conflicts</h2>
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <Badge variant="destructive" className="bg-red-100 text-red-800">
                    Conflicts {conflictCount}
                  </Badge>
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                The following conflict(s) have been detected with the new rule. Select conflicts from below to resolve them individually or in groups/bulk.
              </p>
            </div>

            <div className="px-6 pb-4 pt-2">
              {/* Select All */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Checkbox 
                    checked={selectAll}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="font-medium">Select All</span>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={handleDeleteSelected}
                  disabled={selectedRules.length === 0}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              {/* Conflicting Rules */}
              <div className="space-y-4">
                {conflictingRules.map((rule) => (
                  <div key={rule.id} className="border rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <Checkbox 
                        checked={selectedRules.includes(rule.id)}
                        onCheckedChange={(checked) => handleSelectRule(rule.id, checked as boolean)}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-primary mb-2">{rule.title}</h4>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="space-y-1">
                            <div className="flex space-x-2">
                              <Badge variant="outline" className="bg-gray-100">Frequency</Badge>
                              <span>{rule.frequency}</span>
                            </div>
                            <div className="flex space-x-2 items-center">
                              <Badge 
                                variant="secondary" 
                                className={rule.hasTimeConflict ? 'border-red-600 border-2 bg-green-100' : 'bg-green-100 text-green-800'}
                              >
                                Availability
                              </Badge>
                              <span className={rule.hasTimeConflict ? 'text-red-600' : ''}>{rule.availability}</span>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex space-x-2">
                              <Badge variant="outline" className="bg-gray-100">Occurrence</Badge>
                              <span>{rule.occurrence}</span>
                            </div>
                            <div className="flex space-x-2">
                              <Badge variant="outline" className="bg-gray-100">Time Period</Badge>
                              <span>{rule.timePeriod}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Resolve Conflicts */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4">
              <h2 className="text-2xl font-semibold">Resolve Conflicts</h2>
              <p className="text-sm text-gray-600 mt-2">
                Select one of the following from below:
              </p>
            </div>

            <div className="p-6 space-y-4">
              {/* Option 1: Edit */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Badge className='bg-primary/30 text-black mb-1'>OPTION 1</Badge>
                  <div className="font-medium">Update current rule to avoid conflicts</div>
                </div>
                <Button onClick={onEdit}>
                  Edit
                </Button>
              </div>

              {/* Option 2: Replace */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Badge className='bg-primary/30 text-black mb-1'>OPTION 2</Badge>
                  <div className="font-medium">Replace conflicting rules with this rule</div>
                </div>
                <Button 
                  onClick={handleReplace}
                  disabled={selectedRules.length === 0}
                >
                  Replace
                </Button>
              </div>

              {/* Option 3: Override */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Badge className='bg-primary/30 text-black mb-1'>OPTION 3</Badge>
                  <div className="font-medium">Override Current Rules Temporarily</div>
                </div>
                <Button 
                  onClick={handleOverride}
                  disabled={selectedRules.length === 0}
                >
                  Override
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Replace Confirmation Modal */}
      <Dialog open={showReplaceModal} onOpenChange={setShowReplaceModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Are you sure you want to replace?</DialogTitle>
          </DialogHeader>
          <p className="text-sm text-gray-600">
            This is an alert dialog description.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReplaceModal(false)}>
              Cancel
            </Button>
            <Button onClick={confirmReplace} >
              Replace
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Override Confirmation Modal */}
      <Dialog open={showOverrideModal} onOpenChange={setShowOverrideModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Are you sure you want to override?</DialogTitle>
          </DialogHeader>
          <p className="text-sm text-gray-600">
            This is an alert dialog description.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowOverrideModal(false)}>
              Cancel
            </Button>
            <Button onClick={confirmOverride}>
              Override
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};