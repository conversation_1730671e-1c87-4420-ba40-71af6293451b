import { useEffect, useState } from "react";
import {
	Search,
	Plus,
	Users,
	Upload,
	Settings2,
	Phone,
	Mail,
	MoreHorizontal,
	Edit,
	Eye,
	Trash2,
	Pen,
	Info,
	Funnel,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AddMemberSheet } from "./sheets/AddMemberSheet";
import { ViewMemberSheet } from "./sheets/ViewMemberSheet";
import { EditMemberSheet } from "./sheets/EditMemberSheet";
import { TeamMembersFilters } from "./sheets/TeamMembersFilters";
import { useMembers } from "../hooks/useMembers";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import type { MemberData } from "../api/membersApi";
import { useQueryClient } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useDebounce } from "@/hooks/useDebounce";

interface TeamMember {
	id: number;
	name: string;
	email: string;
	phone_number: string;
	roles: Array<{
		role: string;
		stations?: Array<{ id: number; name: string }>;
		locations?: Array<{ id: number; name: string }>;
	}>;
	has_accepted: boolean;
	// Additional computed fields for UI
	displayRole?: string;
	status?: "Active" | "Unverified" | "Pending";
	dateOnboarded?: string;
	avatar?: string;
}

interface TeamMembersTabProps {
	className?: string;
	locationId: number;
}

interface TeamMembersFilters {
	page: number;
	limit: number;
	search?: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	status?: string;
	role?: string | string[];
}

interface TeamMembersResponse {
	data: TeamMember[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

export function TeamMembersTab({ className, locationId }: TeamMembersTabProps) {
	const { organizationId } = useOrganizationContext();
	const queryClient = useQueryClient();

	const [filters, setFilters] = useState<TeamMembersFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedMembers, setSelectedMembers] = useState<number[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [appliedFilters, setAppliedFilters] = useState<Record<string, any>>(
		{}
	);
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddMemberForm, setShowAddMemberForm] = useState(false);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showMemberDetails, setShowMemberDetails] = useState(false);
	const [showEditMember, setShowEditMember] = useState(false);
	const [selectedMember, setSelectedMember] = useState<TeamMember | null>(
		null
	);

	// Debounce search term to avoid too many API calls
	const debouncedSearchTerm = useDebounce(searchTerm, 300);

	// Combine search and filters for API call
	const apiFilters = {
		...appliedFilters,
		...(debouncedSearchTerm && { search: debouncedSearchTerm }),
	};

	// Fetch members using the real API with search and filters
	const {
		data: membersResponse,
		isLoading,
		error,
	} = useMembers({
		organizationId: organizationId ? String(organizationId) : "",
		enabled: !!organizationId,
		locationId: locationId,
		filters: apiFilters,
	});

	// Transform API data to match UI expectations
	const transformMemberData = (apiMembers: MemberData[]): TeamMember[] => {
		return apiMembers.map((member) => {
			// Get primary role for display
			const primaryRole = member.roles[0]?.role || "Team Member";
			const displayRole = primaryRole
				.toLowerCase()
				.replace(/_/g, " ")
				.replace(/\b\w/g, (l) => l.toUpperCase());

			return {
				id: member.id,
				name: member.name,
				email: member.email,
				phone_number: member.phone_number,
				roles: member.roles,
				has_accepted: member.has_accepted,
				displayRole,
				status: member.has_accepted ? "Active" : "Unverified",
				dateOnboarded: member.has_accepted
					? new Date().toLocaleDateString("en-US", {
							year: "numeric",
							month: "short",
							day: "numeric",
							hour: "2-digit",
							minute: "2-digit",
						})
					: "-",
				avatar: undefined, // API doesn't provide avatar yet
			};
		});
	};

	const teamMembersData: TeamMembersResponse = {
		data: membersResponse?.data
			? transformMemberData(membersResponse.data)
			: [],
		pagination: {
			page: 1,
			limit: 12,
			total: membersResponse?.data?.length || 0,
			totalPages: 1,
		},
	};

	const handleFilterChange = (newFilters: Partial<TeamMembersFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && teamMembersData?.data) {
			setSelectedMembers(teamMembersData.data.map((member) => member.id));
		} else {
			setSelectedMembers([]);
		}
	};

	const handleMemberSelection = (memberId: number, selected: boolean) => {
		if (selected) {
			setSelectedMembers((prev) => [...prev, memberId]);
		} else {
			setSelectedMembers((prev) => prev.filter((id) => id !== memberId));
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleAddMember = async (data: any) => {
		console.log("Adding new member:", data);
		// Invalidate and refetch members data
		if (organizationId) {
			await queryClient.invalidateQueries({
				queryKey: ["members", String(organizationId)],
			});
		}
		setShowAddMemberForm(false);
	};

	const handleViewMember = (member: TeamMember) => {
		setSelectedMember(member);
		setShowMemberDetails(true);
	};

	const handleEditMember = (member: TeamMember) => {
		setSelectedMember(member);
		setShowEditMember(true);
	};

	const handleEditFromView = (member: TeamMember) => {
		setShowMemberDetails(false);
		setSelectedMember(member);
		setShowEditMember(true);
	};

	const handleUpdateMember = async (data: any) => {
		console.log("Updating member:", data);
		setShowEditMember(false);
		// You would typically make an API call here to update the member
	};

	const handleApplyFilters = (filterData: any) => {
		console.log("Applying filters:", filterData);

		// Transform filter data to API query parameters
		const apiFilters: Record<string, any> = {};

		// Add status filters
		if (filterData.status && filterData.status.length > 0) {
			apiFilters.status = filterData.status;
		}

		// Add role filters
		if (filterData.roles && filterData.roles.length > 0) {
			apiFilters.roles = filterData.roles;
		}

		// Add sorting (default to name)
		apiFilters.sort_by = "name";

		console.log("Transformed API filters:", apiFilters);
		setAppliedFilters(apiFilters);
	};

	const handleClearFilters = () => {
		setAppliedFilters({});
		setSearchTerm("");
	};

	// Check if any filters or search are applied
	const hasActiveFilters =
		Object.keys(appliedFilters).length > 0 || debouncedSearchTerm;

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case "Active":
				return "default";
			case "Unverified":
				return "secondary";
			case "Pending":
				return "outline";
			default:
				return "default";
		}
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-3 pl-4">
				<h1 className="text-2xl font-bold">Team Members</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className={`cursor-pointer ${
							hasActiveFilters
								? "border-primary bg-primary/10 text-primary"
								: ""
						}`}
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Funnel className="h-4 w-4" />
					</Button>
					{hasActiveFilters && (
						<Button
							variant="ghost"
							size="sm"
							onClick={handleClearFilters}
							className="text-gray-500 hover:text-gray-700"
						>
							Clear Filters
						</Button>
					)}
					<Button
						variant="outline"
						className="hover:bg-primary/90 cursor-pointer text-black hover:text-white"
						onClick={() => setShowAddMemberForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add a Member
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="flex w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
				<div className="text-muted flex h-12 items-center justify-between border-b bg-gray-50 py-1 pl-4">
					<div className="flex w-5 items-center pr-4">
						<Checkbox
							label=""
							checked={
								selectedMembers.length ===
									teamMembersData?.data?.length &&
								teamMembersData?.data?.length > 0
							}
							className="cursor-pointer"
							onCheckedChange={handleSelectAll}
							disabled={isLoading}
						/>
					</div>
					<div className="flex w-64 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Team Member
							</p>
						</div>
					</div>
					<div className="flex w-28 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Phone
							</p>
						</div>
					</div>
					<div className="flex w-72 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Role
							</p>
						</div>
					</div>
					<div className="flex w-20 items-center px-3 text-left">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Status
							</p>
						</div>
					</div>
					<div className="flex w-44 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Date Onboarded
							</p>
						</div>
					</div>
					<div className="flex w-24 items-center px-3">
						<div className="flex items-center gap-3">
							<p></p>
						</div>
					</div>
				</div>

				{/* Team Members Content */}
				{isLoading ? (
					<div className="flex flex-col">
						{Array.from({ length: 5 }).map((_, index) => (
							<div
								key={index}
								className="flex h-16 items-center justify-between border-b border-gray-100 px-4"
							>
								<div className="flex w-5 items-center pr-4">
									<Skeleton className="h-4 w-4" />
								</div>
								<div className="flex w-64 items-center px-3">
									<div className="flex items-center gap-3">
										<Skeleton className="h-10 w-10 rounded-full" />
										<div className="space-y-1">
											<Skeleton className="h-4 w-32" />
											<Skeleton className="h-3 w-48" />
										</div>
									</div>
								</div>
								<div className="flex w-28 items-center px-3">
									<Skeleton className="h-4 w-20" />
								</div>
								<div className="flex w-72 items-center px-3">
									<div className="flex w-full flex-col gap-1">
										<Skeleton className="h-6 w-20 rounded-sm" />
										<Skeleton className="h-6 w-16 rounded-sm" />
									</div>
								</div>
								<div className="flex w-20 items-center px-3 text-left">
									<Skeleton className="h-6 w-16 rounded-full" />
								</div>
								<div className="flex w-44 items-center px-3">
									<Skeleton className="h-4 w-32" />
								</div>
								<div className="flex w-24 items-center justify-end px-3">
									<div className="flex items-center gap-2">
										<Skeleton className="h-8 w-8" />
										<Skeleton className="h-8 w-8" />
										<Skeleton className="h-8 w-8" />
									</div>
								</div>
							</div>
						))}
					</div>
				) : error ? (
					<div className="py-12 text-center">
						<Users className="mx-auto h-12 w-12 text-gray-400" />
						<h3 className="mt-2 text-sm font-medium text-gray-900">
							Error loading team members
						</h3>
						<p className="mt-1 text-sm text-gray-500">
							{(error as any)?.message ||
								"Failed to fetch team members"}
						</p>
					</div>
				) : teamMembersData?.data?.length === 0 ? (
					<div className="py-12 text-center">
						<Users className="mx-auto h-12 w-12 text-gray-400" />
						<h3 className="mt-2 text-sm font-medium text-gray-900">
							No team members found
						</h3>
						<p className="mt-1 text-sm text-gray-500">
							Get started by adding your first team member.
						</p>
						<Button
							className="mt-4"
							onClick={() => setShowAddMemberForm(true)}
						>
							<Plus className="mr-2 h-4 w-4" />
							Add Member
						</Button>
					</div>
				) : (
					<>
						<div className="flex flex-col">
							{teamMembersData?.data?.map(
								(member: TeamMember) => (
									<div
										onClick={() => handleViewMember(member)}
										key={member.id}
										className="flex h-16 cursor-pointer items-center justify-between border-b border-gray-100 px-4 hover:bg-gray-50"
									>
										<div className="flex w-5 items-center pr-4">
											<Checkbox
												label=""
												checked={selectedMembers.includes(
													member.id
												)}
												className="cursor-pointer"
												onCheckedChange={(selected) =>
													handleMemberSelection(
														member.id,
														selected
													)
												}
											/>
										</div>
										<div className="flex w-64 items-center px-3">
											<div className="flex items-center gap-3">
												<Avatar className="h-10 w-10">
													<AvatarImage
														src={
															member.avatar ||
															undefined
														}
														alt={member.name}
													/>
													<AvatarFallback className="bg-gray-200 text-sm text-gray-600">
														{member.name
															.split(" ")
															.map((n) => n[0])
															.join("")}
													</AvatarFallback>
												</Avatar>
												<div>
													<p className="text-sm font-medium text-gray-900">
														{member.name}
													</p>
													<p className="text-xs text-gray-500">
														{member.email}
													</p>
												</div>
											</div>
										</div>
										<div className="flex w-28 items-center px-3">
											<p className="truncate text-sm text-gray-600">
												{member.phone_number}
											</p>
										</div>
										<div className="flex w-72 items-center px-3">
											<div className="flex w-full flex-col gap-1">
												{/* Always show at least the first role */}
												<p className="w-fit rounded-sm bg-[#F4F4F5] px-2 py-1 text-xs font-medium text-gray-900">
													{member.displayRole}
												</p>
												{/* Show second role if available */}
												{member.roles.length > 1 && (
													<p className="w-fit rounded-sm bg-[#E5E7EB] px-2 py-1 text-xs font-medium text-gray-700">
														{member.roles[1].role
															.toLowerCase()
															.replace(/_/g, " ")
															.replace(
																/\b\w/g,
																(l) =>
																	l.toUpperCase()
															)}
													</p>
												)}
												{/* Show additional roles count if more than 2 */}
												{member.roles.length > 2 && (
													<p className="text-xs text-gray-500">
														+
														{member.roles.length -
															2}{" "}
														more role
														{member.roles.length > 3
															? "s"
															: ""}
													</p>
												)}
											</div>
										</div>
										<div className="flex w-20 items-center px-3 text-left">
											<Badge
												variant={getStatusBadgeVariant(
													member.status || "Pending"
												)}
												className={
													((member.status ||
														"Pending") === "Active"
														? "border-green-200 bg-green-100 text-green-800 hover:bg-green-100"
														: (member.status ||
																	"Pending") ===
															  "Unverified"
															? "border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
															: "bg-gray-100 text-gray-800 hover:bg-gray-100") +
													" h-6 min-w-[48px] px-2 py-0.5 text-xs"
												}
											>
												{member.status || "Pending"}
											</Badge>
										</div>
										<div className="flex w-44 items-center px-3">
											<p className="truncate text-sm text-gray-600">
												{member.dateOnboarded}
											</p>
										</div>
										<div className="flex w-24 items-center justify-end px-3">
											<div className="flex items-center gap-2">
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 border p-0"
													onClick={(e) => {
														e.stopPropagation();
														handleViewMember(
															member
														);
													}}
												>
													<Trash2 className="h-4 w-4 text-gray-400" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 border p-0"
													onClick={(e) => {
														e.stopPropagation();
														handleEditMember(
															member
														);
													}}
												>
													<Pen className="h-4 w-4 text-gray-400" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 border p-0"
												>
													<Info className="h-4 w-4 text-gray-400" />
												</Button>
											</div>
										</div>
									</div>
								)
							)}
						</div>

						{/* Pagination */}
						{teamMembersData?.pagination?.totalPages > 1 && (
							<div className="mt-8 flex items-center justify-center gap-2">
								<Button
									variant="outline"
									disabled={
										teamMembersData?.pagination?.page === 1
									}
									onClick={() =>
										handlePageChange(
											teamMembersData?.pagination?.page -
												1
										)
									}
								>
									Previous
								</Button>

								<span className="text-sm text-gray-600">
									Page {teamMembersData.pagination.page} of{" "}
									{teamMembersData?.pagination?.totalPages}
								</span>

								<Button
									variant="outline"
									disabled={
										teamMembersData?.pagination?.page ===
										teamMembersData?.pagination?.totalPages
									}
									onClick={() =>
										handlePageChange(
											teamMembersData?.pagination?.page +
												1
										)
									}
								>
									Next
								</Button>
							</div>
						)}
					</>
				)}
			</div>

			{/* Add Member Sheet */}
			<AddMemberSheet
				open={showAddMemberForm}
				onOpenChange={setShowAddMemberForm}
				onSubmit={handleAddMember}
			/>

			{/* View Member Sheet */}
			<ViewMemberSheet
				open={showMemberDetails}
				onOpenChange={setShowMemberDetails}
				member={selectedMember}
				onEdit={handleEditFromView}
			/>

			{/* Edit Member Sheet */}
			<EditMemberSheet
				open={showEditMember}
				onOpenChange={setShowEditMember}
				member={selectedMember}
				onSubmit={handleUpdateMember}
			/>

			{/* Team Members Filters Sheet */}
			<TeamMembersFilters
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
			/>
		</div>
	);
}
