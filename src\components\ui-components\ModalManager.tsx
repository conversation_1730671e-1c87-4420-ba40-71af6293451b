import React, { Suspense, lazy, memo, useMemo } from "react";
import { useUIStore } from "@/stores/uiStore";
import { Dialog, DialogContent } from "@/components/ui/dialog";

// Define base modal component props
interface BaseModalProps {
	data: Record<string, any>;
	onClose: () => void;
	[key: string]: any;
}

// Lazy load modal components for better performance
const ConfirmationModal = lazy(() =>
	import("./ConfirmationModal").then((module) => ({
		default: module.ConfirmationModal,
	}))
);

// Placeholder lazy components - replace with actual components when they exist
const CustomerCreateModal = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseModalProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Create Customer</h3>
				<p>Customer creation modal placeholder</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

const CustomerEditModal = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseModalProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Edit Customer</h3>
				<p>Customer edit modal placeholder</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

const SettingsProfileModal = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseModalProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Profile Settings</h3>
				<p>Profile settings modal placeholder</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

// Modal Registry with lazy components
const MODAL_COMPONENTS = {
	confirmation: ConfirmationModal,
	"customer-create": CustomerCreateModal,
	"customer-edit": CustomerEditModal,
	"customer-delete": ConfirmationModal,
	"settings-profile": SettingsProfileModal,
	"settings-preferences": ConfirmationModal,
} as const;

type ModalId = keyof typeof MODAL_COMPONENTS;

const SIZE_CLASSES = {
	xs: "sm:max-w-xs",
	sm: "sm:max-w-sm",
	md: "sm:max-w-md",
	lg: "sm:max-w-lg",
	xl: "sm:max-w-xl",
	"2xl": "sm:max-w-2xl",
	full: "sm:max-w-full",
};

// Loading fallback component
const ModalLoadingFallback = memo(() => (
	<div className="flex items-center justify-center p-8">
		<div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
		<span className="ml-2">Loading...</span>
	</div>
));
ModalLoadingFallback.displayName = "ModalLoadingFallback";

// Error boundary for modal components
class ModalErrorBoundary extends React.Component<
	{ children: React.ReactNode; onError?: () => void },
	{ hasError: boolean }
> {
	constructor(props: { children: React.ReactNode; onError?: () => void }) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError() {
		return { hasError: true };
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		console.error("Modal Error:", error, errorInfo);
		this.props.onError?.();
	}

	render() {
		if (this.state.hasError) {
			return (
				<div className="p-4 text-center">
					<p className="text-red-600">
						Something went wrong loading this modal.
					</p>
					<button
						onClick={() => this.setState({ hasError: false })}
						className="mt-2 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
					>
						Try again
					</button>
				</div>
			);
		}

		return this.props.children;
	}
}

// Optimized modal content component
const ModalContent = memo(
	({
		modalId,
		modal,
		modalData,
		onClose,
	}: {
		modalId: string;
		modal: any;
		modalData: Record<string, any>;
		onClose: () => void;
	}) => {
		const ModalComponent = MODAL_COMPONENTS[modalId as ModalId];

		if (!ModalComponent) {
			console.warn(`Modal component not found for id: ${modalId}`);
			return (
				<div className="p-4 text-center">
					<p>Modal component not found: {modalId}</p>
				</div>
			);
		}

		return (
			<ModalErrorBoundary onError={onClose}>
				<Suspense fallback={<ModalLoadingFallback />}>
					<ModalComponent
						data={modalData}
						onClose={onClose}
						{...modal.data}
					/>
				</Suspense>
			</ModalErrorBoundary>
		);
	}
);
ModalContent.displayName = "ModalContent";

export const ModalManager = memo(() => {
	const { activeModal, modalData, modals, closeModal } = useUIStore();

	const currentModal = useMemo(
		() => modals.find((m) => m.id === activeModal),
		[modals, activeModal]
	);

	const sizeClass = useMemo(
		() => SIZE_CLASSES[currentModal?.size || "md"],
		[currentModal?.size]
	);

	const handleOpenChange = useMemo(
		() => (open: boolean) => {
			if (!open && currentModal?.dismissible) {
				closeModal();
			}
		},
		[closeModal, currentModal?.dismissible]
	);

	if (!activeModal || !currentModal) return null;

	return (
		<Dialog open={!!activeModal} onOpenChange={handleOpenChange}>
			<DialogContent
				className={sizeClass}
				showCloseButton={currentModal.dismissible}
			>
				<ModalContent
					modalId={activeModal}
					modal={currentModal}
					modalData={modalData}
					onClose={closeModal}
				/>
			</DialogContent>
		</Dialog>
	);
});

ModalManager.displayName = "ModalManager";
