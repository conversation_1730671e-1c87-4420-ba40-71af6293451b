import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

interface SelectSheetInfoCardProps {
	onNext: () => void;
	buttonText?: string;
	selectedSheet?: string;
	sheets?: string[];
	onSheetSelect?: (sheet: string) => void;
}

export function SelectSheetInfoCard({
	onNext,
	buttonText = "Populate data",
	selectedSheet = "Select Item",
	sheets = [],
	onSheetSelect,
}: SelectSheetInfoCardProps) {
	return (
		<div className="flex w-full items-start justify-between gap-3 rounded-lg bg-gray-100 p-3">
			<div className="inline-flex flex-1 flex-col items-start justify-start gap-1">
				<div className="justify-center self-stretch text-sm leading-tight font-medium text-gray-900">
					Select Sheet
				</div>
				<div className="justify-start self-stretch text-xs leading-none font-normal text-gray-500">
					Only one sheet can be uploaded at a time. Please select the
					sheet you could like to upload data from here.
				</div>
			</div>
			<div className="flex w-96 items-center justify-start gap-3">
				<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
					{sheets.length > 0 ? (
						<Select
							value={selectedSheet}
							onValueChange={(value) =>
								onSheetSelect?.(value as string)
							}
						>
							<SelectTrigger className="h-9 w-full text-xs">
								<SelectValue placeholder="Select a sheet" />
							</SelectTrigger>
							<SelectContent>
								{sheets.map((sheet) => (
									<SelectItem key={sheet} value={sheet}>
										{sheet}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					) : (
						<div className="inline-flex h-9 items-center justify-start self-stretch overflow-hidden rounded-md bg-white px-3 py-2 outline-1 outline-offset-[-1px] outline-gray-300">
							<div className="flex-1 justify-start text-xs leading-none font-normal text-gray-900">
								{selectedSheet}
							</div>
							<ChevronDown className="h-3 w-3 text-gray-900" />
						</div>
					)}
				</div>
				<Button
					onClick={onNext}
					className="flex h-9 w-44 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 hover:bg-[#004a7a]"
				>
					<span className="justify-center text-xs leading-none font-medium text-white">
						{buttonText}
					</span>
				</Button>
			</div>
		</div>
	);
}
