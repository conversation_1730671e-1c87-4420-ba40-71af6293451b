import React from "react";

const PlannerIcon: React.FC = () => {
	return (
		<svg
			width="32"
			height="33"
			viewBox="0 0 32 33"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M29.3335 19.168V13.8346C29.3335 8.80632 29.3335 6.29217 27.7714 4.73006C26.2094 3.16797 23.6951 3.16797 18.6668 3.16797H16.0002C10.9718 3.16797 8.4577 3.16797 6.89559 4.73006C5.3335 6.29217 5.3335 8.80632 5.3335 13.8346V19.168C5.3335 24.1962 5.3335 26.7105 6.89559 28.2725C8.4577 29.8346 10.9718 29.8346 16.0002 29.8346H18.6668C23.6951 29.8346 26.2094 29.8346 27.7714 28.2725C29.3335 26.7105 29.3335 24.1962 29.3335 19.168Z"
				fill="#C4F4F6"
				stroke="#141B34"
				strokeWidth="2"
			/>
			<path
				d="M6.6665 8.5H2.6665M6.6665 16.5H2.6665M6.6665 24.5H2.6665"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
			<path
				d="M23.3333 9.83203H18M20.6667 15.1654H18"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
			<path
				d="M12 29.8346V3.16797"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
		</svg>
	);
};

export default PlannerIcon;
