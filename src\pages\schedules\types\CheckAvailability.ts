export type CheckAvailabilityQueryParams = {
    client_id?: string;
    duration_minutes?: string;
    location_id?: string;
    service_id?: string;
    start_time?: string;
    station_id?: string;
}

export type CheckAvailabilityAvailableResponse = {
    success: boolean;
    message: string;
    data: {
        available: boolean;
        service_available: boolean;
        operating_hours_ok: boolean;
        planner_available: boolean;
        conflicting_appointments: string[];
        slot_details: {
            start_time: string;
            end_time: string;
            duration_minutes: number;
            capacity: number;
        }
    }
}

export type CheckAvailabilityUnavailableResponse = {
    success: boolean;
    message: string;
    data: {
        available: false,
        service_available: boolean;
        operating_hours_ok: boolean;
        planner_available: boolean;
        blocking_reasons: string[];
        conflicting_appointments: string[];
        suggested_alternatives: {
            start_time: string;
            end_time: string;
            available: boolean;
        }[]
    }
}