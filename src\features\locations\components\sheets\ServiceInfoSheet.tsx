import { useState } from "react";
import { X, Plus, Edit, Trash2, Co<PERSON> } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { InputText } from "@/components/common/InputText";
import { Skeleton } from "@/components/ui/skeleton";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import type { ServiceData } from "../../api/servicesApi";

interface ServiceInfoSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	service: ServiceData | null;
	onEdit?: (service: ServiceData) => void;
}

interface FormItem {
	id: number;
	name: string;
	taggedProviders: number;
}

interface LocationItem {
	id: number;
	name: string;
	taggedProviders: number;
}

interface ProviderItem {
	id: number;
	name: string;
	location: string;
}

export function ServiceInfoSheet({
	open,
	onOpenChange,
	service,
	onEdit,
}: ServiceInfoSheetProps) {
	const { organizationId } = useOrganizationContext();
	const [activeTab, setActiveTab] = useState("forms");

	// Mock data - replace with real API calls
	const [forms] = useState<FormItem[]>([
		{ id: 1, name: "General Medical Fitness Test", taggedProviders: 12 },
		{ id: 2, name: "General Medical Fitness Test", taggedProviders: 12 },
		{ id: 3, name: "General Medical Fitness Test", taggedProviders: 12 },
		{ id: 4, name: "General Medical Fitness Test", taggedProviders: 12 },
		{ id: 5, name: "General Medical Fitness Test", taggedProviders: 12 },
	]);

	const [locations] = useState<LocationItem[]>([
		{ id: 1, name: "General Medical Fitness Test", taggedProviders: 12 },
		{ id: 2, name: "General Medical Fitness Test", taggedProviders: 12 },
		{ id: 3, name: "General Medical Fitness Test", taggedProviders: 12 },
		{ id: 4, name: "General Medical Fitness Test", taggedProviders: 12 },
	]);

	const [providers] = useState<ProviderItem[]>([
		{ id: 1, name: "General Medical Fitness Test", location: "Location name" },
		{ id: 2, name: "General Medical Fitness Test", location: "Location name" },
		{ id: 3, name: "General Medical Fitness Test", location: "Location name" },
		{ id: 4, name: "General Medical Fitness Test", location: "Location name" },
		{ id: 5, name: "General Medical Fitness Test", location: "Location name" },
	]);

	const handleClose = () => {
		onOpenChange(false);
	};

	const handleEdit = () => {
		if (service && onEdit) {
			onEdit(service);
		}
	};

	const getServiceMethods = () => {
		if (!service || !service.appointment_methods) return [];
		return service.appointment_methods.map(method => ({
			id: method.id.toString(),
			label: method.name,
			icon: getMethodIcon(method.name)
		}));
	};

	const getMethodIcon = (name: string) => {
		const lowerName = name.toLowerCase();
		if (lowerName.includes("audio") || lowerName.includes("phone")) {
			return "🎵";
		} else if (lowerName.includes("person") || lowerName.includes("office")) {
			return "👥";
		} else if (lowerName.includes("video") || lowerName.includes("virtual")) {
			return "📹";
		}
		return "👥"; // Default icon
	};

	const formatDuration = (minutes: number) => {
		return `${minutes} mins`;
	};

	const formatPrice = (price: number) => {
		return `$${price.toFixed(2)}`;
	};

	if (!service) return null;

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-0 py-0 sm:w-[800px] sm:max-w-[800px] [&>button]:hidden">
				{/* Header */}
				<div className="flex items-center justify-between border-b px-6 py-4">
					<div>
						<SheetTitle className="text-xl font-semibold">
							Service Information Modal
						</SheetTitle>
					</div>
					<Button
						variant="ghost"
						size="icon"
						onClick={handleClose}
						className="h-8 w-8"
					>
						<X className="h-4 w-4" />
					</Button>
				</div>

				{/* Service Details Header */}
				<div className="border-b px-6 py-6">
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<h2 className="text-2xl font-bold text-gray-900 mb-2">
								{service.name}
							</h2>
							
							{/* Service Description */}
							{service.description && (
								<p className="text-gray-600 mb-4">
									{service.description}
								</p>
							)}
							
							{/* Service Status and Settings */}
							<div className="flex items-center gap-4 mb-4">
								<div className="flex items-center gap-2">
									<Badge 
										variant={service.is_available ? "default" : "secondary"}
										className={service.is_available 
											? "bg-green-100 text-green-800 border-green-200"
											: "bg-gray-100 text-gray-800"
										}
									>
										{service.is_available ? "Active" : "Inactive"}
									</Badge>
									<Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
										Service Available
									</Badge>
									{service.auto_approve && (
										<Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
											Auto Approve
										</Badge>
									)}
								</div>
							</div>

							{/* Service Methods */}
							<div className="flex items-center gap-2 mb-4">
								{getServiceMethods().map((method) => (
									<div key={method.id} className="flex items-center gap-1 text-sm text-gray-600">
										<span>{method.icon}</span>
										<span>{method.label}</span>
									</div>
								))}
							</div>

							{/* Duration and Price */}
							<div className="flex items-center gap-6 text-sm text-gray-600">
								<div className="flex items-center gap-1">
									<span>⏱️</span>
									<span>{formatDuration(service.time_in_minute || 30)}</span>
								</div>
								<div className="flex items-center gap-1">
									<span>💰</span>
									<span>{formatPrice(12.99)}</span> {/* Mock price - not in API yet */}
								</div>
							</div>
						</div>

						{/* Action Buttons */}
						<div className="flex items-center gap-2">
							<Button variant="ghost" size="icon" className="h-8 w-8">
								<Copy className="h-4 w-4" />
							</Button>
							<Button variant="ghost" size="icon" className="h-8 w-8">
								<Edit className="h-4 w-4" />
							</Button>
							<Button variant="ghost" size="icon" className="h-8 w-8">
								<Trash2 className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</div>

				{/* Tabs Content */}
				<div className="px-6">
					<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
						<TabsList className="grid w-full grid-cols-3 mb-6">
							<TabsTrigger value="forms">Forms</TabsTrigger>
							<TabsTrigger value="locations">Locations</TabsTrigger>
							<TabsTrigger value="providers">Providers</TabsTrigger>
						</TabsList>

						{/* Forms Tab */}
						<TabsContent value="forms" className="space-y-4">
							<div className="flex items-center justify-between">
								<div>
									<h3 className="text-sm font-medium text-gray-900">Form name</h3>
									<p className="text-sm text-gray-500">Tagged Providers</p>
								</div>
								<Button 
									variant="outline" 
									size="sm"
									className="flex items-center gap-2"
								>
									<Plus className="h-4 w-4" />
									Add a Form
								</Button>
							</div>
							
							<div className="space-y-2">
								{forms.map((form) => (
									<div 
										key={form.id}
										className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
									>
										<div className="flex-1">
											<p className="text-sm font-medium text-gray-900">
												{form.name}
											</p>
											<p className="text-sm text-gray-500">
												{form.taggedProviders}
											</p>
										</div>
										<div className="flex items-center gap-2">
											<Button variant="ghost" size="icon" className="h-8 w-8">
												<Edit className="h-4 w-4" />
											</Button>
											<Button variant="ghost" size="icon" className="h-8 w-8">
												<Copy className="h-4 w-4" />
											</Button>
											<Button variant="ghost" size="icon" className="h-8 w-8 text-red-500">
												<Trash2 className="h-4 w-4" />
											</Button>
										</div>
									</div>
								))}
							</div>
						</TabsContent>

						{/* Locations Tab */}
						<TabsContent value="locations" className="space-y-4">
							<div className="flex items-center justify-between">
								<div>
									<h3 className="text-sm font-medium text-gray-900">Form name</h3>
									<p className="text-sm text-gray-500">Tagged Providers</p>
								</div>
							</div>
							
							<div className="space-y-2">
								{locations.map((location) => (
									<div 
										key={location.id}
										className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
									>
										<div className="flex-1">
											<p className="text-sm font-medium text-gray-900">
												{location.name}
											</p>
											<p className="text-sm text-gray-500">
												{location.taggedProviders}
											</p>
										</div>
										<div className="flex items-center gap-2">
											<Button variant="ghost" size="icon" className="h-8 w-8">
												<Edit className="h-4 w-4" />
											</Button>
											<Button variant="ghost" size="icon" className="h-8 w-8">
												<Copy className="h-4 w-4" />
											</Button>
										</div>
									</div>
								))}
							</div>
						</TabsContent>

						{/* Providers Tab */}
						<TabsContent value="providers" className="space-y-4">
							<div className="flex items-center justify-between">
								<div>
									<h3 className="text-sm font-medium text-gray-900">Provider</h3>
									<p className="text-sm text-gray-500">Location</p>
								</div>
							</div>
							
							<div className="space-y-2">
								{providers.map((provider) => (
									<div 
										key={provider.id}
										className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
									>
										<div className="flex-1">
											<p className="text-sm font-medium text-gray-900">
												{provider.name}
											</p>
											<p className="text-sm text-gray-500">
												{provider.location}
											</p>
										</div>
										<div className="flex items-center gap-2">
											<Button variant="ghost" size="icon" className="h-8 w-8">
												<Edit className="h-4 w-4" />
											</Button>
											<Button variant="ghost" size="icon" className="h-8 w-8">
												<Copy className="h-4 w-4" />
											</Button>
										</div>
									</div>
								))}
							</div>
						</TabsContent>
					</Tabs>
				</div>

				{/* Footer */}
				<div className="border-t px-6 py-4 mt-8">
					<div className="flex items-center justify-end gap-3">
						<Button variant="outline" onClick={handleClose}>
							Close
						</Button>
						<Button onClick={handleEdit} className="bg-blue-600 hover:bg-blue-700">
							Edit
						</Button>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
