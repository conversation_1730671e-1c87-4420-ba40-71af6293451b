import { type FC } from "react";
import { Trash2, <PERSON>ci<PERSON>, Send } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import type { FormTypes } from "../types";
import { Badge } from "@/components/ui/badge";

export interface AllFormsCardProps {
	form: FormTypes;
	onEdit?: (form: FormTypes) => void;
	onView?: (form: FormTypes) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
}

export const AllFormsCard: React.FC<AllFormsCardProps> = ({
	form,
	onEdit,
	onView,
	isSelected = false,
	onSelectionChange,
}) => {
	return (
		<div
			className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
			onClick={() => onView?.(form)}
		>
			{/* Checkbox Section */}
			<div
				className="flex h-16 items-center px-4"
				onClick={(e) => e.stopPropagation()}
			>
				<Checkbox
					checked={isSelected}
					onCheckedChange={onSelectionChange}
					className="cursor-pointer"
				/>
			</div>

			{/* Form Name Section */}
			<div className="flex flex-2 items-center px-3">
				<h2 className="text-sm leading-5 font-medium">{form.name}</h2>
			</div>

			{/* Type Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className="border-transparent bg-[#c3efce] text-[10px] text-[#0a2914]"
				>
					{form.type}
				</Badge>
			</div>

			{/* Service Section */}
			<div className="flex flex-1 items-center gap-1.5 px-3">
				<h2 className="text-[10px] leading-5 font-normal">
					{form.service.name}
				</h2>
			</div>

			{/* Providers Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className="bg-foreground-muted border-transparent px-2 py-1 text-[10px] text-[#0a2914]"
				>
					{form.providers}
				</Badge>
			</div>

			{/* Status Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className="border-transparent bg-[#c3efce] text-[10px] text-[#0a2914]"
				>
					{form.status}
				</Badge>
			</div>

			{/* Created At Section */}
			<div className="flex flex-1 items-center px-3">
				<h3 className="text-[10px] leading-5 font-normal">
					{form.createdAt}
				</h3>
			</div>

			{/* Actions Section */}
			<div className="flex flex-1 items-center justify-end px-3">
				<div className="flex items-center gap-2.5">
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onEdit?.(form);
						}}
					>
						<Pencil className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(form);
						}}
					>
						<Send className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							console.log("QR Code action for:", form.name);
						}}
					>
						<Trash2 className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
};
