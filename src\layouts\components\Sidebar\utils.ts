import { useMemo } from 'react';
import type { NavigationItem } from './types';
import { usePermissions } from '@/hooks/usePermissions';

/**
 * Check if user has access to a navigation item based on permissions, roles, groups, and features
 */
// export const checkNavigationItemAccess = (
//   item: NavigationItem,
//   permissions: ReturnType<typeof usePermissions>
// ): boolean => {
//   const { canAny, canAll, hasRole, hasAnyRole, inGroup, inAnyGroup, hasFeature } = permissions;

//   // If no restrictions are defined, allow access
//   if (!item.permissions && !item.roles && !item.groups && !item.features) {
//     return true;
//   }

//   const requireAll = item.requireAll ?? false;
//   const checks: boolean[] = [];

//   // Check permissions
//   if (item.permissions && item.permissions.length > 0) {
//     const permissionCheck = requireAll 
//       ? canAll(item.permissions) 
//       : canAny(item.permissions);
//     checks.push(permissionCheck);
//   }

//   // Check roles
//   if (item.roles && item.roles.length > 0) {
//     const roleCheck = requireAll 
//       ? item.roles.every(role => hasRole(role))
//       : hasAnyRole(item.roles);
//     checks.push(roleCheck);
//   }

//   // Check groups
//   if (item.groups && item.groups.length > 0) {
//     const groupCheck = requireAll 
//       ? item.groups.every(group => inGroup(group))
//       : inAnyGroup(item.groups);
//     checks.push(groupCheck);
//   }

//   // Check features
//   if (item.features && item.features.length > 0) {
//     const featureCheck = requireAll 
//       ? item.features.every(feature => hasFeature(feature))
//       : item.features.some(feature => hasFeature(feature));
//     checks.push(featureCheck);
//   }

//   // If requireAll is true, all checks must pass
//   // If requireAll is false, at least one check must pass
//   return requireAll ? checks.every(check => check) : checks.some(check => check);
// };

/**
 * Hook to filter navigation items based on user permissions
 */
// export const useFilteredNavigationItems = (items: NavigationItem[]): NavigationItem[] => {
//   const permissions = usePermissions();

//   return useMemo(() => {
//     const filterItems = (items: NavigationItem[]): NavigationItem[] => {
//       return items
//         .filter(item => checkNavigationItemAccess(item, permissions))
//         .map(item => ({
//           ...item,
//           children: item.children ? filterItems(item.children) : undefined
//         }))
//         .filter(item => !item.children || item.children.length > 0); // Remove parent items with no accessible children
//     };

//     return filterItems(items);
//   }, [items, permissions]);
// }; 