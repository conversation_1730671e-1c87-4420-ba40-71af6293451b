// pages/Planner/PlannerApp.tsx
import React from 'react';
import { Routes, Route, useNavigate, useParams } from 'react-router-dom';

// Import Planner components
import { PlannerDashboard } from './components/PlannerDashboard';
import { LocationSelection } from './components/LocationSelection';
import { ProviderSelection } from './components/ProviderSelection';
import { PreferencesManagement } from './components/PreferenceManagement';
import { AddOrganizationPreference } from './AddOrganizationPreference';
import { AddServicePreference } from './components/AddServicePreference';
import { ServiceRulesList } from './components/ServiceRulesList';
import { RuleConflictsScreen } from './components/RuleConflictsScreen';
import { AppointmentConflictsScreen } from './components/AppointmentConflictsScreen';
import { SuccessScreen } from './components/SuccessScreen';
import { useLocation } from 'react-router';

// Route wrapper components
const PlannerDashboardWrapper = () => {
  const navigate = useNavigate();
  return (
    <PlannerDashboard 
      onSelectLevel={(level) => {
        switch (level) {
          case 'organization':
            navigate('organization');
            break;
          case 'location':
            navigate('locations');
            break;
          case 'provider':
            navigate('providers');
            break;
        }
      }} 
    />
  );
};

const LocationSelectionWrapper = () => {
  const navigate = useNavigate();
  return (
    <LocationSelection 
      onBack={() => navigate('..')}
      onSelectLocation={(location) => navigate(`location/${location.id}`)}
    />
  );
};

const ProviderSelectionWrapper = () => {
  const navigate = useNavigate();
  return (
    <ProviderSelection 
      onBack={() => navigate('..')}
      onSelectProvider={(provider) => navigate(`provider/${provider.id}`)}
    />
  );
};

const OrganizationPreferencesWrapper = () => {
  const navigate = useNavigate();
  return (
    <PreferencesManagement
      level="organization"
      entityName="Organization Name"
      onAddOrganizationPreference={() => navigate('add-preference')}
      onAddServicePreference={() => navigate('services/add')}
      onAddCategoryPreference={() => navigate('categories/add')}
      onEditPreference={(id, type) => {
        switch (type) {
          case 'organization':
            navigate(`preferences/${id}/edit`);
            break;
          case 'service':
            navigate(`services/${id}/edit`);
            break;
          case 'category':
            navigate(`categories/${id}/edit`);
            break;
        }
      }}
      onViewServiceRules={(service) => navigate(`services/${service.id}/rules`)}
      onBack={() => navigate('..')}
    />
  );
};

const LocationPreferencesWrapper = () => {
  const { locationId } = useParams();
  const navigate = useNavigate();
  return (
    <PreferencesManagement
      level="location"
      entityName={`Location ${locationId}`}
      onAddOrganizationPreference={() => navigate('add-preference')}
      onAddServicePreference={() => navigate('services/add')}
      onAddCategoryPreference={() => navigate('categories/add')}
      onEditPreference={(id, type) => {
        switch (type) {
          case 'organization':
            navigate(`preferences/${id}/edit`);
            break;
          case 'service':
            navigate(`services/${id}/edit`);
            break;
          case 'category':
            navigate(`categories/${id}/edit`);
            break;
        }
      }}
      onViewServiceRules={(service) => navigate(`services/${service.id}/rules`)}
      onBack={() => navigate('../locations')}
    />
  );
};

const ProviderPreferencesWrapper = () => {
  const { providerId } = useParams();
  const navigate = useNavigate();
  return (
    <PreferencesManagement
      level="provider"
      entityName={`Provider ${providerId}`}
      onAddOrganizationPreference={() => navigate('add-preference')}
      onAddServicePreference={() => navigate('services/add')}
      onAddCategoryPreference={() => navigate('categories/add')}
      onEditPreference={(id, type) => {
        switch (type) {
          case 'organization':
            navigate(`preferences/${id}/edit`);
            break;
          case 'service':
            navigate(`services/${id}/edit`);
            break;
          case 'category':
            navigate(`categories/${id}/edit`);
            break;
        }
      }}
      onViewServiceRules={(service) => navigate(`services/${service.id}/rules`)}
      onBack={() => navigate('../providers')}
    />
  );
};

// Form wrapper components
const AddOrgPreferenceWrapper = () => {
  const navigate = useNavigate();
  
  return (
    <AddOrganizationPreference
      onBack={() => navigate('..')}
      onSave={(data) => {
        console.log('Saving organization preference:', data);
        navigate('..');
      }}
    />
  );
};

const AddServicePreferenceWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Determine the current context based on the URL path
  const getCurrentContext = () => {
    const pathname = location.pathname;
    
    if (pathname.includes('/organization/')) {
      return { 
        level: 'organization', 
        entityId: null, // Organization doesn't have an entity ID
        basePath: '/dashboard/schedule/planner/organization'
      };
    } else if (pathname.includes('/location/')) {
      // Extract locationId from path like /location/123/services/add
      const match = pathname.match(/\/location\/([^\/]+)/);
      const locationId = match?.[1];
      return { 
        level: 'location', 
        entityId: locationId,
        basePath: `/dashboard/schedule/planner/location/${locationId}`
      };
    } else if (pathname.includes('/provider/')) {
      // Extract providerId from path like /provider/456/services/add
      const match = pathname.match(/\/provider\/([^\/]+)/);
      const providerId = match?.[1];
      return { 
        level: 'provider', 
        entityId: providerId,
        basePath: `/dashboard/schedule/planner/provider/${providerId}`
      };
    }
    
    // Default fallback to organization
    return { 
      level: 'organization', 
      entityId: null,
      basePath: '/dashboard/schedule/planner/organization'
    };
  };

  
  
  const { basePath } = getCurrentContext();
  console.log(basePath)
  
  const handleSave = (data: any) => {
    console.log('Saving service preference:', data);
    
    // Simulate conflict detection - let's always trigger conflicts for testing
    const hasRuleConflicts = true; // Force conflicts for testing
    const hasAppointmentConflicts = Math.random() > 0.5;

    console.log(basePath)
    
    if (hasRuleConflicts) {
      navigate(`${basePath}/services/conflicts`, { 
        state: { preferenceData: data }
      });
    } else if (hasAppointmentConflicts) {
      navigate(`${basePath}/services/appointment-conflicts`, { 
        state: { preferenceData: data }
      });
    } else {
      navigate(`${basePath}/services/success`, { 
        state: { preferenceData: data }
      });
    }
  };
  
  return (
    <AddServicePreference
      onBack={() => navigate(basePath)}
      onSave={handleSave}
    />
  );
};

const ServiceRulesWrapper = () => {
  const navigate = useNavigate();
  const { serviceId } = useParams();
  
  // Sample rules data
  const sampleRules = [
    {
      id: '1',
      title: 'Rule Name, here the admin can give this rule a nick name',
      frequency: '10/day',
      occurrence: 'Daily',
      availability: '8:00 am - 8:00 pm',
      timePeriod: '03 Feb 2025 - 31 Dec 2025',
      createdDate: '07 June 2025',
      priority: 1
    },
    {
      id: '2',
      title: 'Another service rule',
      frequency: '15/day',
      occurrence: 'Weekly',
      availability: '9:00 am - 5:00 pm',
      timePeriod: '01 Jan 2025 - 31 Dec 2025',
      createdDate: '05 June 2025',
      priority: 2
    }
  ];
  
  return (
    <ServiceRulesList
      serviceName={`Service ${serviceId}`}
      rules={sampleRules}
      onBack={() => navigate('../..')}
      onAddPreference={() => navigate('../add')}
      onEditRule={(ruleId) => navigate(`${ruleId}/edit`)}
      onDeleteRule={(ruleId) => console.log('Delete rule:', ruleId)}
      onReorderRules={(rules) => console.log('Reorder rules:', rules)}
      sortable={true}
    />
  );
};

const ConflictsWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get current context from URL
  const getCurrentBasePath = () => {
    const pathname = location.pathname;
    
    if (pathname.includes('/organization/')) {
      return '/dashboard/schedule/planner/organization';
    } else if (pathname.includes('/location/')) {
      const match = pathname.match(/\/location\/([^\/]+)/);
      const locationId = match?.[1];
      return `/dashboard/schedule/planner/location/${locationId}`;
    } else if (pathname.includes('/provider/')) {
      const match = pathname.match(/\/provider\/([^\/]+)/);
      const providerId = match?.[1];
      return `/dashboard/schedule/planner/provider/${providerId}`;
    }
    
    return '/dashboard/schedule/planner/organization';
  };
  
  const basePath = getCurrentBasePath();
  
  const mockConflictData = {
    title: 'New Service Rule',
    frequency: '10/day',
    occurrence: 'Daily',
    availability: '8:00 am - 8:00 pm',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const handleResolve = () => {
    // After resolving conflicts, check for appointment conflicts
    const hasAppointmentConflicts = Math.random() > 0.5;
    
    if (hasAppointmentConflicts) {
      navigate(`${basePath}/services/appointment-conflicts`);
    } else {
      // No more conflicts, go to success
      navigate(`${basePath}/services/success`);
    }
  };
  
  return (
    <RuleConflictsScreen
      newRule={mockConflictData}
      conflictingRules={[
        {
          id: '1',
          title: 'Existing Rule 1',
          frequency: '15/day',
          occurrence: 'Daily',
          availability: '9:00 am - 6:00 pm',
          timePeriod: '01 Jan 2025 - 31 Dec 2025',
          hasTimeConflict: true
        },
        {
          id: '2',
          title: 'Existing Rule 2',
          frequency: '20/day',
          occurrence: 'Daily',
          availability: '10:00 am - 7:00 pm',
          timePeriod: '01 Feb 2025 - 31 Dec 2025'
        }
      ]}
      conflictCount={2}
      onBack={() => navigate(`${basePath}/services/add`)}
      onEdit={() => navigate(`${basePath}/services/add`)}
      onReplace={handleResolve}
      onOverride={handleResolve}
    />
  );
};

const AppointmentConflictsWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get current context from URL
  const getCurrentBasePath = () => {
    const pathname = location.pathname;
    
    if (pathname.includes('/organization/')) {
      return '/dashboard/schedule/planner/organization';
    } else if (pathname.includes('/location/')) {
      const match = pathname.match(/\/location\/([^\/]+)/);
      const locationId = match?.[1];
      return `/dashboard/schedule/planner/location/${locationId}`;
    } else if (pathname.includes('/provider/')) {
      const match = pathname.match(/\/provider\/([^\/]+)/);
      const providerId = match?.[1];
      return `/dashboard/schedule/planner/provider/${providerId}`;
    }
    
    return '/dashboard/schedule/planner/organization';
  };
  
  const basePath = getCurrentBasePath();
  
  const mockConflictData = {
    title: 'New Service Rule',
    frequency: '10/day',
    occurrence: 'Daily',
    availability: '8:00 am - 8:00 pm',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const sampleAppointments = [
    {
      id: '1',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '2',
      patientName: 'Jane Smith',
      providerName: 'Dr. Sarah Wilson',
      appointmentType: 'Follow-up Consultation',
      date: '15 Apr 2025',
      time: '2:00 pm - 3:00 pm'
    }
  ];
  
  const handleResolve = () => {
    // After resolving appointment conflicts, go to success
    navigate(`${basePath}/services/success`);
  };
  
  return (
    <AppointmentConflictsScreen
      newRule={mockConflictData}
      impactedAppointments={sampleAppointments}
      impactCount={sampleAppointments.length}
      onBack={() => navigate(`${basePath}/services/add`)}
      onLetThemBe={handleResolve}
      onCancelAll={handleResolve}
      onRescheduleAll={handleResolve}
      onViewAllAppointments={() => console.log('View all appointments')}
    />
  );
};

const SuccessWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get current context from URL
  const getCurrentBasePath = () => {
    const pathname = location.pathname;
    
    if (pathname.includes('/organization/')) {
      return '/dashboard/schedule/planner/organization';
    } else if (pathname.includes('/location/')) {
      const match = pathname.match(/\/location\/([^\/]+)/);
      const locationId = match?.[1];
      return `/dashboard/schedule/planner/location/${locationId}`;
    } else if (pathname.includes('/provider/')) {
      const match = pathname.match(/\/provider\/([^\/]+)/);
      const providerId = match?.[1];
      return `/dashboard/schedule/planner/provider/${providerId}`;
    }
    
    return '/dashboard/schedule/planner/organization';
  };
  
  const basePath = getCurrentBasePath();
  
  return (
    <SuccessScreen
      serviceName="Service Name"
      locationName="Location Name"
      onViewAll={() => navigate(basePath)} // Go back to the main preferences page
      onAddAnother={() => navigate(`${basePath}/services/add`)} // Add another service preference
    />
  );
};

// Category placeholder component
const CategoryPlaceholder = () => {
  const navigate = useNavigate();
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-semibold mb-4">Category Preferences</h1>
        <p className="text-gray-600 mb-4">Category preference functionality to be implemented</p>
        <button 
          onClick={() => navigate('..')}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Back
        </button>
      </div>
    </div>
  );
};

export const PlannerApp: React.FC = () => {
  return (
    <Routes>
      {/* Planner Dashboard */}
      <Route index element={<PlannerDashboardWrapper />} />
      
      {/* Selection pages */}
      <Route path="locations" element={<LocationSelectionWrapper />} />
      <Route path="providers" element={<ProviderSelectionWrapper />} />
      
      {/* Organization level routes */}
      <Route path="organization" element={<OrganizationPreferencesWrapper />} />
      <Route path="organization/add-preference" element={<AddOrgPreferenceWrapper />} />
      <Route path="organization/preferences/:id/edit" element={<AddOrgPreferenceWrapper />} />
      
      {/* Organization Service routes */}
      <Route path="organization/services/add" element={<AddServicePreferenceWrapper />} />
      <Route path="organization/services/:id/edit" element={<AddServicePreferenceWrapper />} />
      <Route path="organization/services/:serviceId/rules" element={<ServiceRulesWrapper />} />
      <Route path="organization/services/conflicts" element={<ConflictsWrapper />} />
      <Route path="organization/services/appointment-conflicts" element={<AppointmentConflictsWrapper />} />
      <Route path="organization/services/success" element={<SuccessWrapper />} />
      
      {/* Organization Category routes */}
      <Route path="organization/categories/add" element={<CategoryPlaceholder />} />
      <Route path="organization/categories/:id/edit" element={<CategoryPlaceholder />} />
      
      {/* Location level routes */}
      <Route path="location/:locationId" element={<LocationPreferencesWrapper />} />
      <Route path="location/:locationId/add-preference" element={<AddOrgPreferenceWrapper />} />
      <Route path="location/:locationId/preferences/:id/edit" element={<AddOrgPreferenceWrapper />} />
      
      {/* Location Service routes */}
      <Route path="location/:locationId/services/add" element={<AddServicePreferenceWrapper />} />
      <Route path="location/:locationId/services/:id/edit" element={<AddServicePreferenceWrapper />} />
      <Route path="location/:locationId/services/:serviceId/rules" element={<ServiceRulesWrapper />} />
      <Route path="location/:locationId/services/conflicts" element={<ConflictsWrapper />} />
      <Route path="location/:locationId/services/appointment-conflicts" element={<AppointmentConflictsWrapper />} />
      <Route path="location/:locationId/services/success" element={<SuccessWrapper />} />
      
      {/* Location Category routes */}
      <Route path="location/:locationId/categories/add" element={<CategoryPlaceholder />} />
      <Route path="location/:locationId/categories/:id/edit" element={<CategoryPlaceholder />} />
      
      {/* Provider level routes */}
      <Route path="provider/:providerId" element={<ProviderPreferencesWrapper />} />
      <Route path="provider/:providerId/add-preference" element={<AddOrgPreferenceWrapper />} />
      <Route path="provider/:providerId/preferences/:id/edit" element={<AddOrgPreferenceWrapper />} />
      
      {/* Provider Service routes */}
      <Route path="provider/:providerId/services/add" element={<AddServicePreferenceWrapper />} />
      <Route path="provider/:providerId/services/:id/edit" element={<AddServicePreferenceWrapper />} />
      <Route path="provider/:providerId/services/:serviceId/rules" element={<ServiceRulesWrapper />} />
      <Route path="provider/:providerId/services/conflicts" element={<ConflictsWrapper />} />
      <Route path="provider/:providerId/services/appointment-conflicts" element={<AppointmentConflictsWrapper />} />
      <Route path="provider/:providerId/services/success" element={<SuccessWrapper />} />
      
      {/* Provider Category routes */}
      <Route path="provider/:providerId/categories/add" element={<CategoryPlaceholder />} />
      <Route path="provider/:providerId/categories/:id/edit" element={<CategoryPlaceholder />} />
    </Routes>
  );
};