import { useQuery } from "@tanstack/react-query";
import { locationsApi } from "../api";
import { queryKeys } from "@/lib/query/keys";
import type { LocationsFilters } from "../types";
import { useOrganizationContext } from "@/features/organizations/context";

export const useLocations = (
	filters: LocationsFilters = {},
	orgId?: number
) => {
	const { organizationId } = useOrganizationContext();
	return useQuery({
		queryKey: queryKeys.locations.list(filters),
		queryFn: () =>
			locationsApi.getLocations(filters, orgId ? orgId : organizationId!),
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};
