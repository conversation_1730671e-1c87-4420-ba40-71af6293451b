import React, { Suspense, lazy, memo, useMemo } from "react";
import { useUIStore } from "@/stores/uiStore";
import {
	Drawer,
	DrawerContent,
	DrawerHeader,
	DrawerTitle,
	DrawerDescription,
} from "@/components/ui/drawer";

// Define base drawer component props
interface BaseDrawerProps {
	data: Record<string, any>;
	onClose: () => void;
	[key: string]: any;
}

// Lazy load drawer components for better performance
const UserProfileDrawer = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseDrawerProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">User Profile</h3>
				<p>User ID: {data.userId || "Unknown"}</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

const NotificationsDrawer = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseDrawerProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Notifications</h3>
				<p>Filter: {data.filter || "all"}</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

const SettingsDrawer = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseDrawerProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Settings</h3>
				<p>Settings drawer placeholder</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

const CustomerDetailsDrawer = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseDrawerProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Customer Details</h3>
				<p>Customer details drawer placeholder</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

// Drawer Registry with lazy components
const DRAWER_COMPONENTS = {
	"user-profile": UserProfileDrawer,
	notifications: NotificationsDrawer,
	settings: SettingsDrawer,
	"customer-details": CustomerDetailsDrawer,
} as const;

type DrawerId = keyof typeof DRAWER_COMPONENTS;

const SIZE_CLASSES = {
	sm: "sm:max-w-sm",
	md: "sm:max-w-md",
	lg: "sm:max-w-lg",
	xl: "sm:max-w-xl",
	full: "sm:max-w-full",
};

// Loading fallback component
const DrawerLoadingFallback = memo(() => (
	<div className="flex items-center justify-center p-8">
		<div className="h-6 w-6 animate-spin rounded-full border-b-2 border-gray-900"></div>
		<span className="ml-2">Loading...</span>
	</div>
));
DrawerLoadingFallback.displayName = "DrawerLoadingFallback";

// Error boundary for drawer components
class DrawerErrorBoundary extends React.Component<
	{ children: React.ReactNode; onError?: () => void },
	{ hasError: boolean }
> {
	constructor(props: { children: React.ReactNode; onError?: () => void }) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError() {
		return { hasError: true };
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		console.error("Drawer Error:", error, errorInfo);
		this.props.onError?.();
	}

	render() {
		if (this.state.hasError) {
			return (
				<div className="p-4 text-center">
					<p className="text-red-600">
						Something went wrong loading this drawer.
					</p>
					<button
						onClick={() => this.setState({ hasError: false })}
						className="mt-2 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
					>
						Try again
					</button>
				</div>
			);
		}

		return this.props.children;
	}
}

// Optimized drawer content component
const DrawerContentInner = memo(
	({
		drawerId,
		drawer,
		drawerData,
		onClose,
	}: {
		drawerId: string;
		drawer: any;
		drawerData: Record<string, any>;
		onClose: () => void;
	}) => {
		const DrawerComponent = DRAWER_COMPONENTS[drawerId as DrawerId];

		if (!DrawerComponent) {
			console.warn(`Drawer component not found for id: ${drawerId}`);
			return (
				<div className="p-4 text-center">
					<p>Drawer component not found: {drawerId}</p>
				</div>
			);
		}

		return (
			<DrawerErrorBoundary onError={onClose}>
				<Suspense fallback={<DrawerLoadingFallback />}>
					<DrawerComponent
						data={drawerData}
						onClose={onClose}
						{...drawer.data}
					/>
				</Suspense>
			</DrawerErrorBoundary>
		);
	}
);
DrawerContentInner.displayName = "DrawerContentInner";

export const DrawerManager = memo(() => {
	const { activeDrawer, drawerData, drawers, closeDrawer } = useUIStore();

	const currentDrawer = useMemo(
		() => drawers.find((d) => d.id === activeDrawer),
		[drawers, activeDrawer]
	);

	const sizeClass = useMemo(
		() => SIZE_CLASSES[currentDrawer?.size || "md"],
		[currentDrawer?.size]
	);

	const handleOpenChange = useMemo(
		() => (open: boolean) => {
			if (!open && currentDrawer?.dismissible) {
				closeDrawer();
			}
		},
		[closeDrawer, currentDrawer?.dismissible]
	);

	if (!activeDrawer || !currentDrawer) return null;

	return (
		<Drawer
			open={!!activeDrawer}
			onOpenChange={handleOpenChange}
			direction={currentDrawer.direction}
		>
			<DrawerContent className={sizeClass}>
				<DrawerHeader>
					<DrawerTitle>
						{currentDrawer.data?.title || "Drawer"}
					</DrawerTitle>
					<DrawerDescription>
						{currentDrawer.data?.description || ""}
					</DrawerDescription>
				</DrawerHeader>
				<div className="flex-1 overflow-auto p-4">
					<DrawerContentInner
						drawerId={activeDrawer}
						drawer={currentDrawer}
						drawerData={drawerData}
						onClose={closeDrawer}
					/>
				</div>
			</DrawerContent>
		</Drawer>
	);
});

DrawerManager.displayName = "DrawerManager";
