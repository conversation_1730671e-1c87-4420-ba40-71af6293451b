import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Uploader } from "@/components/common/Uploader";
import { InputText } from "@/components/common/InputText";
import { InputPhone } from "@/components/common/InputPhone";
import { Separator } from "@/components/ui/seperator";

const orgTypes = ["Walk In Clinic", "Hospital", "Specialty Clinic"];

const countries = ["Canada", "USA"];

const OrganizationInformation: React.FC = () => {
	const [orgName] = useState("University of Waterloo Medical Center");
	const [orgType, setOrgType] = useState(orgTypes[0]);
	const [phone, setPhone] = useState("");
	const [address, setAddress] = useState("");
	const [country, setCountry] = useState(countries[0]);
	const [province, setProvince] = useState("");
	const [city, setCity] = useState("");
	const [logoFiles, setLogoFiles] = useState([]);

	return (
		<form className="flex flex-col items-center justify-between gap-3 space-y-6 md:flex-row">
			<div className="flex flex-col gap-3">
				<h2 className="mb-4 text-xl font-semibold">
					Organization Information
				</h2>
				<Separator />
				<div className="grid grid-cols-2 gap-6">
					<div className="col-span-2">
						<InputText
							variant="with-label"
							label="Organization Name *"
							id="org-name"
							type="text"
							value={orgName}
						/>
					</div>
					<div className="col-span-2">
						<Label htmlFor="org-type">Type</Label>
						<Select
							value={orgType}
							onValueChange={(value: any) => setOrgType(value)} // fix this type eventually - whover works on this
						>
							<SelectTrigger
								className="col-span-2 mt-2 w-full"
								id="org-type"
							>
								<SelectValue placeholder="Select type" />
							</SelectTrigger>
							<SelectContent>
								{orgTypes.map((type) => (
									<SelectItem key={type} value={type}>
										{type}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					<div className="col-span-2 flex flex-col gap-2">
						<Label htmlFor="org-phone">Phone *</Label>
						<InputPhone
							variant="with-country-dropdown"
							value={phone}
							onChange={(value) => setPhone(value)}
							defaultCountry="US"
							placeholder="Enter phone number"
							className="w-full"
							showFlag={true}
							format="international"
							searchable={true}
							showValidation={true}
						/>
					</div>
					<div className="col-span-2">
						<Label htmlFor="org-address">Address *</Label>
						<Input
							id="org-address"
							type="text"
							placeholder="456 Elm Avenue"
							value={address}
							className="mt-2"
							onChange={(e) => setAddress(e.target.value)}
						/>
					</div>
					<div className="col-span-2">
						<Label htmlFor="org-country">Country</Label>
						<Select
							value={country}
							onValueChange={(value: any) => setCountry(value)} //update the type here
						>
							<SelectTrigger
								className="mt-2 w-full"
								id="org-country"
							>
								<SelectValue placeholder="Select country" />
							</SelectTrigger>
							<SelectContent>
								{countries.map((c) => (
									<SelectItem key={c} value={c}>
										{c}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					<div>
						<Label htmlFor="org-province">Province</Label>
						<Input
							id="org-province"
							type="text"
							className="mt-2 w-full"
							placeholder="Ontario"
							value={province}
							onChange={(e) => setProvince(e.target.value)}
						/>
					</div>
					<div>
						<Label htmlFor="org-city">City</Label>
						<Input
							id="org-city"
							type="text"
							className="mt-2 w-full"
							placeholder="Waterloo"
							value={city}
							onChange={(e) => setCity(e.target.value)}
						/>
					</div>
				</div>
				<div className="flex justify-end gap-2">
					<button type="button" className="rounded border px-4 py-2">
						Cancel
					</button>
					<button
						type="submit"
						className="bg-primary rounded px-4 py-2 text-white"
					>
						Edit
					</button>
				</div>
			</div>
			<div className="col-span-2 mt-2 flex flex-col items-center gap-6 md:w-1/3">
				<div className="">
					<Uploader
						files={logoFiles}
						onFilesChange={(files: File[]) =>
							setLogoFiles(files as typeof logoFiles)
						}
						accept=".jpg,.jpeg,.png"
						maxFileSize={2 * 1024 * 1024}
						maxFiles={1}
						uploadText="Upload logo"
						descriptionText="JPG or PNG, min 800x800px, max 2MB"
						size="md"
					/>
				</div>
				<div className="flex text-xs text-gray-500">
					Upload photos in JPG, or PNG format. Minimum resolution:
					800x800 pixels. Max file size: 2 MB.
				</div>
			</div>
		</form>
	);
};

export default OrganizationInformation;
