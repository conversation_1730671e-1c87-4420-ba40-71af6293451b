import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface TimePickerProps {
	value?: string;
	onChange?: (time: string) => void;
	onBlur?: () => void;
	label?: string;
	placeholder?: string;
	disabled?: boolean;
	className?: string;
	required?: boolean;
	error?: string;
}

export const TimePicker = ({
	value = "",
	onChange,
	onBlur,
	label,
	placeholder = "Select time",
	disabled = false,
	className,
	required = false,
	error,
}: TimePickerProps) => {
	// Parse the time value (format: "HH:MM AM/PM")
	const parseTime = (timeString: string) => {
		if (!timeString) return { hours: "", minutes: "", period: "AM" };

		const [time, period = "AM"] = timeString.split(" ");
		const [hours = "", minutes = ""] = time.split(":");

		return { hours, minutes, period };
	};

	const { hours, minutes, period } = parseTime(value);

	// Generate hours (1-12 for 12-hour format)
	const hourOptions = Array.from({ length: 12 }, (_, i) => {
		const hour = i + 1;
		return hour.toString().padStart(2, "0");
	});

	// Generate minutes (00-59)
	const minuteOptions = Array.from({ length: 60 }, (_, i) => {
		return i.toString().padStart(2, "0");
	});

	const updateTime = (
		newHours: string,
		newMinutes: string,
		newPeriod: string
	) => {
		if (newHours && newMinutes && newPeriod) {
			const timeString = `${newHours}:${newMinutes} ${newPeriod}`;
			onChange?.(timeString);
		}
	};

	const handleHourChange = (newHours: string) => {
		updateTime(newHours, minutes || "00", period);
	};

	const handleMinuteChange = (newMinutes: string) => {
		updateTime(hours || "01", newMinutes, period);
	};

	const handlePeriodChange = (newPeriod: string) => {
		updateTime(hours || "01", minutes || "00", newPeriod);
	};

	return (
		<div className={cn("space-y-1", className)}>
			{label && (
				<Label className="text-sm font-medium text-[#3B5566]">
					{label}
					{required && (
						<span className="text-destructive ml-1">*</span>
					)}
				</Label>
			)}

			{/* Unified Time Picker Container */}
			<div
				className={cn(
					"border-input bg-background ring-offset-background focus-within:ring-ring flex items-center rounded-md border px-12 text-sm focus-within:ring-2 focus-within:ring-offset-2",
					error && "border-destructive focus-within:ring-destructive",
					disabled && "cursor-not-allowed opacity-50"
				)}
			>
				{/* Hours */}
				<Select
					value={hours}
					onValueChange={(value) => handleHourChange(value as string)}
					disabled={disabled}
				>
					<SelectTrigger className="h-auto w-4.5 justify-center border-0 p-0 shadow-none focus:ring-0 focus:ring-offset-0 [&>svg]:hidden">
						<SelectValue placeholder="00" />
					</SelectTrigger>
					<SelectContent>
						{hourOptions.map((hour) => (
							<SelectItem key={hour} value={hour}>
								{hour}
							</SelectItem>
						))}
					</SelectContent>
				</Select>

				<span className="text-muted-foreground mx-1">:</span>

				{/* Minutes */}
				<Select
					value={minutes}
					onValueChange={(value) =>
						handleMinuteChange(value as string)
					}
					disabled={disabled}
				>
					<SelectTrigger className="h-auto w-4.5 border-0 p-0 shadow-none focus:ring-0 focus:ring-offset-0 [&>svg]:hidden">
						<SelectValue placeholder="00" />
					</SelectTrigger>
					<SelectContent>
						{minuteOptions.map((minute) => (
							<SelectItem key={minute} value={minute}>
								{minute}
							</SelectItem>
						))}
					</SelectContent>
				</Select>

				<span className="text-muted-foreground mx-2"></span>

				{/* AM/PM */}
				<Select
					value={period}
					onValueChange={(value) =>
						handlePeriodChange(value as string)
					}
					disabled={disabled}
				>
					<SelectTrigger className="h-auto w-8 border-0 p-0 shadow-none focus:ring-0 focus:ring-offset-0 [&>svg]:hidden">
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="AM">AM</SelectItem>
						<SelectItem value="PM">PM</SelectItem>
					</SelectContent>
				</Select>
			</div>

			{error && <p className="text-destructive text-sm">{error}</p>}
		</div>
	);
};
