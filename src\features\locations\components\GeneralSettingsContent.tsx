import { useState } from "react";
import { User, Video, Volume2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { cn } from "@/lib/utils";
import { ExpirationSettings } from "@/components/common/ExpirationSettings";
import { DualRangeSlider } from "@/components/ui/dual-range-slider";

interface GeneralSettings {
	scheduleVisibility: boolean;
	appointmentMethods: {
		inPerson: boolean;
		video: boolean;
		audio: boolean;
	};
	autoApprove: boolean;
	chat: boolean;
	chatMethod: "one-way" | "both-parties";
	scheduleBlock: boolean;
	scheduleBlockDate: Date | null;
	scheduleBlockWeeks: number;
	scheduleBlockSpecificDates: boolean;
}

export const GeneralSettingsContent = () => {
	const [settings, setSettings] = useState<GeneralSettings>({
		scheduleVisibility: true,
		appointmentMethods: {
			inPerson: true,
			video: false,
			audio: false,
		},
		autoApprove: true,
		chat: true,
		chatMethod: "both-parties",
		scheduleBlock: true,
		scheduleBlockDate: new Date("2025-08-04"),
		scheduleBlockWeeks: 4,
		scheduleBlockSpecificDates: true,
	});

	const updateSettings = (updates: Partial<GeneralSettings>) => {
		setSettings((prev) => ({ ...prev, ...updates }));
	};

	const updateAppointmentMethod = (
		method: keyof GeneralSettings["appointmentMethods"],
		value: boolean
	) => {
		setSettings((prev) => ({
			...prev,
			appointmentMethods: {
				...prev.appointmentMethods,
				[method]: value,
			},
		}));
	};

	return (
		<div className="space-y-4">
			{/* Schedule Visibility */}
			<div className="space-y-3 border-b border-gray-200 py-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">
							Schedule Visibility
						</h3>
						<p className="text-sm text-gray-600">
							This enables people to view the availability of this
							business and book appointments
						</p>
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.scheduleVisibility}
							onCheckedChange={(checked) =>
								updateSettings({ scheduleVisibility: checked })
							}
						/>
						<span className="text-muted">
							{settings.scheduleVisibility ? "On" : "Off"}
						</span>
					</div>
				</div>
			</div>

			{/* Method of Appointments */}
			<div className="space-y-4 border-b border-gray-200 pb-4">
				<h3 className="text-lg font-medium">Method of Appointments</h3>
				<div className="flex gap-6">
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="in-person"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<User className="h-4 w-4" />
								In Person
							</Label>
							<Checkbox
								id="in-person"
								checked={settings.appointmentMethods.inPerson}
								onCheckedChange={(checked) =>
									updateAppointmentMethod(
										"inPerson",
										!!checked
									)
								}
							/>
						</div>
					</Button>
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="video"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<Video className="h-4 w-4" />
								Video
							</Label>
							<Checkbox
								id="video"
								checked={settings.appointmentMethods.video}
								onCheckedChange={(checked) =>
									updateAppointmentMethod("video", !!checked)
								}
							/>
						</div>
					</Button>
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="audio"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<Volume2 className="h-4 w-4" />
								Audio
							</Label>
							<Checkbox
								id="audio"
								checked={settings.appointmentMethods.audio}
								onCheckedChange={(checked) =>
									updateAppointmentMethod("audio", !!checked)
								}
							/>
						</div>
					</Button>
				</div>
			</div>

			{/* Auto Approve */}
			<div className="space-y-3 border-b border-gray-200 pb-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">Auto Approve</h3>
						<p className="text-sm text-gray-600">
							If there is no conflict, appointments are
							automatically approved
						</p>
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.autoApprove}
							onCheckedChange={(checked) =>
								updateSettings({ autoApprove: checked })
							}
						/>
						<span className="text-muted">
							{settings.autoApprove ? "On" : "Off"}
						</span>
					</div>
				</div>
			</div>

			{/* Chat */}
			<div className="space-y-4 border-b border-gray-200 pb-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">Chat</h3>
						<p className="text-sm text-gray-600">
							If there is no conflict, appointments are
							automatically approved
						</p>
					</div>

					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.chat}
							onCheckedChange={(checked) =>
								updateSettings({ chat: checked })
							}
						/>
						<span className="text-muted">
							{settings.chat ? "On" : "Off"}
						</span>
					</div>
				</div>

				{settings.chat && (
					<div className="ml-0">
						<RadioGroup
							value={settings.chatMethod}
							onValueChange={(value) =>
								updateSettings({
									chatMethod: value as
										| "one-way"
										| "both-parties",
								})
							}
							className="flex w-full gap-12.5 space-y-4"
						>
							<div className="flex items-start space-x-3">
								<RadioGroupItem
									value="one-way"
									id="one-way"
									className="mt-1"
								/>
								<div className="space-y-1">
									<Label
										htmlFor="one-way"
										className="text-sm font-medium"
									>
										One Way Chat
									</Label>
									<p className="text-sm text-gray-600">
										Only Admins can send messages to
										patients.
									</p>
								</div>
							</div>
							<div className="flex items-start space-x-3">
								<RadioGroupItem
									value="both-parties"
									id="both-parties"
									className="mt-1"
								/>
								<div className="space-y-1">
									<Label
										htmlFor="both-parties"
										className="text-sm font-medium"
									>
										Allow Both Parties to Chat
									</Label>
									<p className="text-sm text-gray-600">
										Both patients and admins can chat
									</p>
								</div>
							</div>
						</RadioGroup>
					</div>
				)}
			</div>

			{/* Schedule Block */}
			<div className="space-y-4 pb-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">Schedule Block</h3>
						<p className="text-sm text-gray-600">
							This allows a buffer time and restricts how far out
							patients can book
						</p>
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.scheduleBlock}
							onCheckedChange={(checked) =>
								updateSettings({ scheduleBlock: checked })
							}
						/>
						<span className="text-muted">
							{settings.scheduleBlock ? "On" : "Off"}
						</span>
					</div>
				</div>

				{settings.scheduleBlock && (
					<div className="ml-0 space-y-6">
						{/* Date Range Selection */}
						<div className="flex items-center justify-between gap-4">
							<div className="flex flex-col items-center gap-2.5">
								<div className="flex items-center gap-2.5">
									<span className="text-sm font-medium">
										After
									</span>
									<DatePicker
										value={
											settings.scheduleBlockDate ||
											undefined
										}
										onChange={(date) =>
											updateSettings({
												scheduleBlockDate:
													date as Date | null,
											})
										}
										variant="default"
										className="w-auto"
									/>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="specific-dates-before"
										checked={
											settings.scheduleBlockSpecificDates
										}
										onCheckedChange={(checked) =>
											updateSettings({
												scheduleBlockSpecificDates:
													!!checked,
											})
										}
									/>
									<Label
										htmlFor="specific-dates-before"
										className="text-sm"
									>
										Select Specific Date(s)
									</Label>
								</div>
							</div>
							{/* Slider for Weeks */}
							<div className="flex-1 space-y-3">
								<DualRangeSlider
									value={[1, 52]}
									onValueChange={(value: number[]) =>
										updateSettings({
											scheduleBlockWeeks: value[0],
										})
									}
									min={1}
									max={52}
									className="w-full"
								/>
							</div>
							<div className="flex flex-col items-center gap-2.5">
								<div className="flex items-center gap-2.5">
									<span className="text-sm font-medium">
										Till
									</span>
									<ExpirationSettings
										value={settings.scheduleBlockWeeks.toString()}
										onValueChange={(value) =>
											updateSettings({
												scheduleBlockWeeks:
													parseInt(value),
											})
										}
										onUnitChange={() => {}}
										unit="weeks"
										useSpecificDate={false}
										onUseSpecificDateChange={() => {}}
										label=""
										units={[
											{ value: "hours", label: "Hours" },
											{
												value: "minutes",
												label: "Minutes",
											},
											{ value: "weeks", label: "Weeks" },
											{ value: "days", label: "Days" },
										]}
										showAlternativeOption={false}
										containerWidth="w-[145px]"
									/>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="specific-dates-after"
										checked={false}
										onCheckedChange={() => {}}
									/>
									<Label
										htmlFor="specific-dates-after"
										className="text-sm"
									>
										Select Specific Date(s)
									</Label>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end gap-3 border-t border-gray-200 pt-6">
				<Button variant="outline">Cancel</Button>
				<Button>Save</Button>
			</div>
		</div>
	);
};
