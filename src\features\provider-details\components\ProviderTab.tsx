import React, { useState } from "react";
import { Search, Filter, Plus, UserCheck } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";

interface ProviderTabProps {
	className?: string;
}

interface Provider {
	id: string;
	name: string;
	specialty: string;
	status: "active" | "inactive" | "pending";
	license: string;
	organization: string;
	patientCount: number;
	rating: number;
}

// Mock data for demonstration
const mockProviders: Provider[] = [
	{
		id: "1",
		name: "Dr. <PERSON>",
		specialty: "Cardiology",
		status: "active",
		license: "MD-12345",
		organization: "Healthcare Partners Inc.",
		patientCount: 142,
		rating: 4.8,
	},
	{
		id: "2",
		name: "Dr. <PERSON>",
		specialty: "Orthopedics",
		status: "active",
		license: "MD-12346",
		organization: "City Medical Center",
		patientCount: 98,
		rating: 4.6,
	},
	{
		id: "3",
		name: "Dr. <PERSON>",
		specialty: "Pediatrics",
		status: "pending",
		license: "MD-12347",
		organization: "Wellness Clinic Network",
		patientCount: 67,
		rating: 4.9,
	},
	{
		id: "4",
		name: "Dr. <PERSON>",
		specialty: "Internal Medicine",
		status: "inactive",
		license: "MD-12348",
		organization: "Healthcare Partners Inc.",
		patientCount: 89,
		rating: 4.4,
	},
];

export function ProviderTab({ className }: ProviderTabProps) {
	const [selectedProviders, setSelectedProviders] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [isLoading] = useState(false);
	const [showCreateForm, setShowCreateForm] = useState(false);

	// Filter providers based on search term
	const filteredProviders = mockProviders.filter(
		(provider) =>
			provider.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			provider.specialty
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			provider.organization
				.toLowerCase()
				.includes(searchTerm.toLowerCase())
	);

	const handleSelectAll = (checked: boolean) => {
		if (checked) {
			setSelectedProviders(
				filteredProviders.map((provider) => provider.id)
			);
		} else {
			setSelectedProviders([]);
		}
	};

	const handleProviderSelection = (providerId: string, selected: boolean) => {
		if (selected) {
			setSelectedProviders((prev) => [...prev, providerId]);
		} else {
			setSelectedProviders((prev) =>
				prev.filter((id) => id !== providerId)
			);
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "active":
				return "text-green-600 bg-green-100";
			case "inactive":
				return "text-red-600 bg-red-100";
			case "pending":
				return "text-yellow-600 bg-yellow-100";
			default:
				return "text-gray-600 bg-gray-100";
		}
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-1 pl-4">
				<Checkbox
					label="Select All"
					checked={
						selectedProviders.length === filteredProviders.length &&
						filteredProviders.length > 0
					}
					className="border-primary"
					onCheckedChange={handleSelectAll}
				/>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search providers..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => console.log("Filter providers")}
					>
						<Filter className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						className="cursor-pointer"
						onClick={() => setShowCreateForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Provider
					</Button>
				</div>
			</div>

			{/* Loading State */}
			{isLoading && (
				<div className="grid grid-cols-1 gap-4">
					{[...Array(6)].map((_, i) => (
						<div key={i} className="animate-pulse">
							<div className="h-24 rounded-lg bg-gray-200"></div>
						</div>
					))}
				</div>
			)}

			{/* Providers List */}
			{!isLoading && (
				<>
					{filteredProviders.length === 0 ? (
						<div className="py-12 text-center">
							<UserCheck className="mx-auto h-12 w-12 text-gray-400" />
							<h3 className="mt-2 text-sm font-medium text-gray-900">
								No providers found
							</h3>
							<p className="mt-1 text-sm text-gray-500">
								Get started by adding your first healthcare
								provider.
							</p>
							<Button
								className="mt-4"
								onClick={() => setShowCreateForm(true)}
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Provider
							</Button>
						</div>
					) : (
						<div className="flex flex-col gap-0.5">
							{filteredProviders.map((provider) => (
								<div
									key={provider.id}
									className="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-50"
								>
									<div className="flex items-center gap-3">
										<Checkbox
											checked={selectedProviders.includes(
												provider.id
											)}
											onCheckedChange={(checked) =>
												handleProviderSelection(
													provider.id,
													checked
												)
											}
										/>
										<UserCheck className="h-8 w-8 text-gray-400" />
										<div>
											<h4 className="font-medium text-gray-900">
												{provider.name}
											</h4>
											<p className="text-sm text-gray-500">
												{provider.specialty} •{" "}
												{provider.organization}
											</p>
											<p className="text-xs text-gray-400">
												License: {provider.license}
											</p>
										</div>
									</div>
									<div className="flex items-center gap-4">
										<div className="text-right">
											<p className="text-sm font-medium text-gray-900">
												{provider.patientCount} patients
											</p>
											<p className="text-xs text-gray-500">
												Rating: {provider.rating}/5.0
											</p>
										</div>
										<span
											className={`rounded-full px-2 py-1 text-xs ${getStatusColor(provider.status)}`}
										>
											{provider.status}
										</span>
										<Button
											variant="ghost"
											size="sm"
											onClick={() =>
												console.log(
													"View provider:",
													provider.id
												)
											}
										>
											View
										</Button>
									</div>
								</div>
							))}
						</div>
					)}
				</>
			)}

			{/* Create Provider Modal Placeholder */}
			{showCreateForm && (
				<div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
					<div className="mx-4 w-full max-w-md rounded-lg bg-white p-6">
						<h3 className="mb-4 text-lg font-semibold">
							Add Provider
						</h3>
						<p className="mb-4 text-gray-600">
							Provider registration form would go here.
						</p>
						<div className="flex justify-end gap-2">
							<Button
								variant="outline"
								onClick={() => setShowCreateForm(false)}
							>
								Cancel
							</Button>
							<Button onClick={() => setShowCreateForm(false)}>
								Add Provider
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
