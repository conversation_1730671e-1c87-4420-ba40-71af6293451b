"use client";
import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { OperatingHours } from "@/components/common/OperatingHours";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { weeklyScheduleSchema } from "@/lib/utils/validation";

// Example form schema that includes the weekly schedule
const formSchema = z.object({
	businessName: z.string().min(1, "Business name is required"),
	email: z.string().email("Invalid email address"),
	schedule: weeklyScheduleSchema,
	slotDuration: z.number().min(5).max(120),
	appliesTo: z.object({
		schedule: z.boolean(),
		waitlist: z.boolean(),
	}),
});

type FormData = z.infer<typeof formSchema>;

// Example existing data for edit mode
const existingBusinessData: FormData = {
	businessName: "Acme Consulting",
	email: "<EMAIL>",
	slotDuration: 30,
	appliesTo: {
		schedule: true,
		waitlist: true,
	},
	schedule: {
		monday: {
			enabled: true,
			slots: [
				{ id: "1", startTime: "09:00 AM", endTime: "12:00 PM" },
				{ id: "2", startTime: "01:00 PM", endTime: "05:00 PM" },
			],
		},
		tuesday: {
			enabled: true,
			slots: [{ id: "1", startTime: "10:00 AM", endTime: "04:00 PM" }],
		},
		wednesday: {
			enabled: true,
			slots: [
				{ id: "1", startTime: "09:00 AM", endTime: "12:00 PM" },
				{ id: "2", startTime: "02:00 PM", endTime: "06:00 PM" },
			],
		},
		thursday: {
			enabled: false,
			slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
		},
		friday: {
			enabled: true,
			slots: [{ id: "1", startTime: "09:00 AM", endTime: "03:00 PM" }],
		},
		saturday: {
			enabled: false,
			slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
		},
		sunday: {
			enabled: false,
			slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
		},
	},
};

export const OperatingHoursContent = () => {
	const [validationErrors, setValidationErrors] = React.useState<
		Record<string, string[]>
	>({});
	const [isEditMode, setIsEditMode] = React.useState(false);

	const form = useForm<FormData>({
		resolver: zodResolver(formSchema),
		defaultValues: isEditMode
			? existingBusinessData
			: {
					businessName: "",
					email: "",
					slotDuration: 10,
					appliesTo: {
						schedule: true,
						waitlist: false,
					},
					schedule: {
						monday: {
							enabled: true,
							slots: [
								{
									id: "1",
									startTime: "08:00 AM",
									endTime: "05:00 PM",
								},
							],
						},
						tuesday: {
							enabled: true,
							slots: [
								{
									id: "1",
									startTime: "08:00 AM",
									endTime: "05:00 PM",
								},
							],
						},
						wednesday: {
							enabled: true,
							slots: [
								{
									id: "1",
									startTime: "08:00 AM",
									endTime: "05:00 PM",
								},
							],
						},
						thursday: {
							enabled: true,
							slots: [
								{
									id: "1",
									startTime: "08:00 AM",
									endTime: "05:00 PM",
								},
							],
						},
						friday: {
							enabled: false,
							slots: [
								{
									id: "1",
									startTime: "09:00 AM",
									endTime: "06:00 PM",
								},
							],
						},
						saturday: {
							enabled: true,
							slots: [
								{
									id: "1",
									startTime: "08:00 AM",
									endTime: "05:00 PM",
								},
							],
						},
						sunday: {
							enabled: false,
							slots: [
								{
									id: "1",
									startTime: "08:00 AM",
									endTime: "05:00 PM",
								},
							],
						},
					},
				},
	});

	const handleSubmit = (data: FormData) => {
		// Check if there are any validation errors
		const hasValidationErrors = Object.values(validationErrors).some(
			(errors) => errors.length > 0
		);

		if (hasValidationErrors) {
			console.log("Form has validation errors:", validationErrors);
			return;
		}

		console.log("Form submitted successfully:", data);
		// Here you would typically save to your backend
	};

	const toggleMode = () => {
		setIsEditMode(!isEditMode);
		form.reset(
			isEditMode
				? {
						businessName: "",
						email: "",
						slotDuration: 10,
						appliesTo: { schedule: true, waitlist: false },
						schedule: {
							monday: {
								enabled: true,
								slots: [
									{
										id: "1",
										startTime: "08:00 AM",
										endTime: "05:00 PM",
									},
								],
							},
							tuesday: {
								enabled: true,
								slots: [
									{
										id: "1",
										startTime: "08:00 AM",
										endTime: "05:00 PM",
									},
								],
							},
							wednesday: {
								enabled: true,
								slots: [
									{
										id: "1",
										startTime: "08:00 AM",
										endTime: "05:00 PM",
									},
								],
							},
							thursday: {
								enabled: true,
								slots: [
									{
										id: "1",
										startTime: "08:00 AM",
										endTime: "05:00 PM",
									},
								],
							},
							friday: {
								enabled: false,
								slots: [
									{
										id: "1",
										startTime: "09:00 AM",
										endTime: "06:00 PM",
									},
								],
							},
							saturday: {
								enabled: true,
								slots: [
									{
										id: "1",
										startTime: "08:00 AM",
										endTime: "05:00 PM",
									},
								],
							},
							sunday: {
								enabled: false,
								slots: [
									{
										id: "1",
										startTime: "08:00 AM",
										endTime: "05:00 PM",
									},
								],
							},
						},
					}
				: existingBusinessData
		);
	};

	return (
		<div className="mx-auto mt-2.5 max-w-4xl space-y-6">
			<Card className="border-none p-0 shadow-none">
				<CardContent className="p-0">
					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(handleSubmit)}
							className="space-y-6"
						>
							{/* Weekly Schedule Component with controlled props */}
							<FormField
								control={form.control}
								name="schedule"
								render={({ field }) => (
									<FormItem>
										<FormControl>
											<OperatingHours
												value={field.value}
												onChange={field.onChange}
												onValidationChange={
													setValidationErrors
												}
												slotDuration={form.watch(
													"slotDuration"
												)}
												onSlotDurationChange={(
													duration
												) =>
													form.setValue(
														"slotDuration",
														duration
													)
												}
												appliesTo={form.watch(
													"appliesTo"
												)}
												onAppliesToChange={(
													appliesTo
												) =>
													form.setValue(
														"appliesTo",
														appliesTo
													)
												}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Submit Button */}
							<div className="flex justify-end">
								<Button
									type="submit"
									className="cursor-pointer"
								>
									Save
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
};
