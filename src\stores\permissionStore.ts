import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface ScopedPermissions {
	locations?: Record<string, string[]>;
	stations?: Record<string, string[]>;
}

export interface FieldControl {
	visible?: boolean;
	editable?: boolean;
	required?: boolean;
}

export interface FieldControls {
	[form: string]: {
		[field: string]: FieldControl;
	};
}

export interface permissionState {
	// Core auth data
	organizationId: number | null;
	productId: string | null;
	role: string;
	permissions: string[];
	scopedPermissions: ScopedPermissions;
	groups: string[];
	features: string[];
	fieldControls: FieldControls;
	loading: boolean;

	// Context
	currentLocation?: string;
	currentStation?: string;

	// Actions
	setContext: (context: {
		orgId?: number;
		productId?: string;
		location?: string;
		station?: string;
	}) => void;
	setAuthData: (data: any) => void;
	setCurrentContext: (location?: string, station?: string) => void;

	// Permission checks
	hasPermission: (
		permission: string,
		context?: { location?: string; station?: string }
	) => boolean;
	hasAnyPermission: (
		permissions: string[],
		context?: { location?: string; station?: string }
	) => boolean;
	hasAllPermissions: (
		permissions: string[],
		context?: { location?: string; station?: string }
	) => boolean;

	// Group checks
	inGroup: (group: string) => boolean;
	inAnyGroup: (groups: string[]) => boolean;

	// Feature checks
	featureEnabled: (feature: string) => boolean;

	// Field controls
	isFieldVisible: (form: string, field: string) => boolean;
	isFieldEditable: (form: string, field: string) => boolean;
	isFieldRequired: (form: string, field: string) => boolean;
	getFieldControl: (form: string, field: string) => FieldControl;

	// Role checks
	hasRole: (role: string) => boolean;
	hasAnyRole: (roles: string[]) => boolean;

	// Utility
	logout: () => void;
}

export const usePermissionStore = create<permissionState>()(
	devtools(
		(set, get) => ({
			// Initial state
			organizationId: null,
			productId: null,
			role: "",
			permissions: [],
			scopedPermissions: {},
			groups: [],
			features: [],
			fieldControls: {},
			loading: true,
			currentLocation: undefined,
			currentStation: undefined,

			// Actions
			setContext: ({ orgId, productId, location, station }) =>
				set((state) => ({
					organizationId: orgId ?? state.organizationId,
					productId: productId ?? state.productId,
					currentLocation: location ?? state.currentLocation,
					currentStation: station ?? state.currentStation,
				})),

			setAuthData: (data) => {
				const transformedData = {
					...data,
					// Transform API response to store format
					organizationId: data.organization_id ?? data.organizationId,
					productId: data.product_id ?? data.productId,
					scopedPermissions: data.scopedPermissions ?? {},
					loading: false,
				};
				delete transformedData.organization_id;
				delete transformedData.product_id;

				set((state) => ({ ...state, ...transformedData }));
			},

			setCurrentContext: (location, station) =>
				set({ currentLocation: location, currentStation: station }),

			// Permission checks
			hasPermission: (permission, context) => {
				const state = get();

				// Check global permissions first
				if (state.permissions.includes(permission)) {
					return true;
				}

				// Check scoped permissions
				const loc = context?.location ?? state.currentLocation;
				const station = context?.station ?? state.currentStation;

				if (
					loc &&
					state.scopedPermissions.locations?.[loc]?.includes(
						permission
					)
				) {
					return true;
				}

				if (
					station &&
					state.scopedPermissions.stations?.[station]?.includes(
						permission
					)
				) {
					return true;
				}

				return false;
			},

			hasAnyPermission: (permissions, context) => {
				const state = get();
				return permissions.some((permission) =>
					state.hasPermission(permission, context)
				);
			},

			hasAllPermissions: (permissions, context) => {
				const state = get();
				return permissions.every((permission) =>
					state.hasPermission(permission, context)
				);
			},

			// Group checks
			inGroup: (group) => get().groups.includes(group),
			inAnyGroup: (groups) => {
				const state = get();
				return groups.some((group) => state.groups.includes(group));
			},

			// Feature checks
			featureEnabled: (feature) => get().features.includes(feature),

			// Field controls
			isFieldVisible: (form, field) => {
				const state = get();
				const control = state.fieldControls?.[form]?.[field] ?? {};
				return control.visible ?? true;
			},

			isFieldEditable: (form, field) => {
				const state = get();
				const control = state.fieldControls?.[form]?.[field] ?? {};
				return control.editable ?? true;
			},

			isFieldRequired: (form, field) => {
				const state = get();
				const control = state.fieldControls?.[form]?.[field] ?? {};
				return control.required ?? false;
			},

			getFieldControl: (form, field) => {
				const state = get();
				return state.fieldControls?.[form]?.[field] ?? {};
			},

			// Role checks
			hasRole: (role) => get().role === role,
			hasAnyRole: (roles) => roles.includes(get().role),

			// Utility
			logout: () => {
				set({
					organizationId: null,
					productId: null,
					role: "",
					permissions: [],
					scopedPermissions: {},
					groups: [],
					features: [],
					fieldControls: {},
					currentLocation: undefined,
					currentStation: undefined,
					loading: false,
				});
			},
		}),
		{
			name: "auth-store",
		}
	)
);
