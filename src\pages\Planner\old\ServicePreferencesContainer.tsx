// components/planner/ServicePreferencesContainer.tsx
import React, { useState } from 'react';
import { RuleConflictsScreen } from './RuleConflictsScreen';
import { AppointmentConflictsScreen } from './AppointmentConflictsScreen';
import { ServiceRulesList } from './ServiceRulesList';
import { SuccessScreen } from './SuccessScreen';
import { ServicePreferencesTable } from './ServicePreferencesTable';
import { AddServicePreference } from './AddServicePreference';

type ViewState = 'table' | 'add' | 'edit' | 'rule-conflicts' | 'appointment-conflicts' | 'service-rules' | 'success';

interface ConflictData {
  hasRuleConflicts: boolean;
  hasAppointmentConflicts: boolean;
  ruleConflicts?: any[];
  appointmentConflicts?: any[];
}

interface ServicePreferencesContainerProps {
  onBack?: () => void;
  initialServiceData?: any;
  startWithAddForm?: boolean; // New prop to determine if we should start with add form
}

export const ServicePreferencesContainer: React.FC<ServicePreferencesContainerProps> = ({
  onBack,
  initialServiceData, // Added this parameter
  startWithAddForm = false // New parameter with default value
}) => {
  const [currentView, setCurrentView] = useState<ViewState>(() => {
    // If we should start with add form, go to 'add'
    if (startWithAddForm) {
      return 'add';
    }
    // If we have initial service data, it means we're coming from viewing a specific service
    return initialServiceData ? 'service-rules' : 'table';
  });
  
  const [editingPreferenceId, setEditingPreferenceId] = useState<string | null>(null);
  const [currentPreferenceData, setCurrentPreferenceData] = useState<any>(null);
  const [conflictData, setConflictData] = useState<ConflictData | null>(null);
  const [selectedService, setSelectedService] = useState<any>(initialServiceData || null);

  // Sample conflict data for demonstration
  const sampleRuleConflicts = [
    {
      id: '1',
      title: 'Physical, in person appointments',
      frequency: '100/month',
      occurrence: 'Daily',
      availability: '10:00 am - 6:00 pm',
      timePeriod: '01 Apr 2025 - 31 Dec 2025',
      hasTimeConflict: true
    },
    {
      id: '2',
      title: 'Family Friday, special time',
      frequency: '75/month',
      occurrence: 'Bi-weekly',
      availability: '6:00 am - 10:00 pm',
      timePeriod: '15 Jan 2025 - 15 Jul 2025'
    },
    {
      id: '3',
      title: 'Rule Name, here the admin can give this rule a nick name',
      frequency: '300/month',
      occurrence: 'Daily',
      availability: '8:00 am - 8:00 pm',
      timePeriod: '03 Feb 2025 - 31 Dec 2025'
    }
  ];

  const sampleAppointmentConflicts = [
    {
      id: '1',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '2',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '3',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '4',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '5',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '6',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    }
  ];

  const sampleServiceRules = [
    {
      id: '1',
      title: 'Rule Name, here the admin can give this rule a nick name',
      frequency: '10/day',
      occurrence: 'Daily',
      availability: '8:00 am - 8:00 pm',
      timePeriod: '03 Feb 2025 - 31 Dec 2025',
      createdDate: '07 June 2025',
      priority: 1
    },
    {
      id: '2',
      title: 'Rule Name, here the admin can give this rule a nick name',
      frequency: '10/day',
      occurrence: 'Daily',
      availability: '8:00 am - 8:00 pm',
      timePeriod: '03 Feb 2025 - 31 Dec 2025',
      createdDate: '07 June 2025',
      priority: 2
    },
    {
      id: '3',
      title: 'Rule Name, here the admin can give this rule a nick name',
      frequency: '10/day',
      occurrence: 'Daily',
      availability: '8:00 am - 8:00 pm',
      timePeriod: '03 Feb 2025 - 31 Dec 2025',
      createdDate: '07 June 2025',
      priority: 3
    },
    {
      id: '4',
      title: 'Rule Name, here the admin can give this rule a nick name',
      frequency: '10/day',
      occurrence: 'Daily',
      availability: '8:00 am - 8:00 pm',
      timePeriod: '03 Feb 2025 - 31 Dec 2025',
      createdDate: '07 June 2025',
      priority: 4
    }
  ];

  const handleAddPreference = () => {
    setEditingPreferenceId(null);
    setCurrentPreferenceData(null);
    setCurrentView('add');
  };

  const handleEditPreference = (preferenceId: string) => {
    setEditingPreferenceId(preferenceId);
    // In real app, load the preference data here
    setCurrentPreferenceData(null);
    setCurrentView('edit');
  };

  const handleViewServiceRules = (service: any) => {
    setSelectedService(service);
    setCurrentView('service-rules');
  };

  const handleSavePreference = (preferenceData: any) => {
    setCurrentPreferenceData(preferenceData);
    
    // Simulate conflict detection (replace with actual API call)
    const hasRuleConflicts = Math.random() > 0.3; // 70% chance of rule conflicts
    const hasAppointmentConflicts = Math.random() > 0.5; // 50% chance of appointment conflicts
    
    const conflicts: ConflictData = {
      hasRuleConflicts,
      hasAppointmentConflicts,
      ruleConflicts: hasRuleConflicts ? sampleRuleConflicts : [],
      appointmentConflicts: hasAppointmentConflicts ? sampleAppointmentConflicts : []
    };
    
    setConflictData(conflicts);
    
    if (hasRuleConflicts) {
      setCurrentView('rule-conflicts');
    } else if (hasAppointmentConflicts) {
      setCurrentView('appointment-conflicts');
    } else {
      // No conflicts, proceed to success
      handleSuccessfulSave();
    }
  };

  const handleSuccessfulSave = () => {
    // Here you would make the actual API call to save the preference
    console.log('Saving preference to API...', currentPreferenceData);
    setCurrentView('success');
  };

  const handleEditFromConflict = () => {
    setCurrentView(editingPreferenceId ? 'edit' : 'add');
  };

  const handleRuleConflictResolution = (selectedRuleIds: string[], action: 'replace' | 'override') => {
    console.log(`${action} rules:`, selectedRuleIds);
    
    // Check if there are appointment conflicts next
    if (conflictData?.hasAppointmentConflicts) {
      setCurrentView('appointment-conflicts');
    } else {
      handleSuccessfulSave();
    }
  };

  const handleAppointmentConflictResolution = (action: 'let-be' | 'cancel' | 'reschedule') => {
    console.log(`Appointment resolution:`, action);
    handleSuccessfulSave();
  };

  const handleBackToTable = () => {
    setCurrentView('table');
    setSelectedService(null);
    setConflictData(null);
    setCurrentPreferenceData(null);
    setEditingPreferenceId(null);
  };

  const handleBackToAdd = () => {
    setCurrentView('add');
  };

  const handleViewAllFromSuccess = () => {
    setCurrentView('service-rules');
  };

  const handleAddAnotherFromSuccess = () => {
    setCurrentView('add');
    setCurrentPreferenceData(null);
    setEditingPreferenceId(null);
  };

  // Render current view
  switch (currentView) {
    case 'table':
      return (
        <ServicePreferencesTable
          onAddPreference={handleAddPreference}
          onViewServiceRules={handleViewServiceRules}
          onEditPreference={handleEditPreference}
        />
      );
      
    case 'add':
    case 'edit':
      return (
        <AddServicePreference
          onBack={handleBackToTable}
          onSave={handleSavePreference}
        />
      );
      
    case 'rule-conflicts':
      return (
        <RuleConflictsScreen
          newRule={currentPreferenceData || {
            title: 'New Rule',
            frequency: '10/day',
            occurrence: 'Daily',
            availability: '8:00 am - 8:00 pm',
            timePeriod: '03 Feb 2025 - 31 Dec 2025'
          }}
          conflictingRules={conflictData?.ruleConflicts || []}
          conflictCount={conflictData?.ruleConflicts?.length || 0}
          onBack={handleBackToAdd}
          onEdit={handleEditFromConflict}
          onReplace={(selectedRuleIds) => handleRuleConflictResolution(selectedRuleIds, 'replace')}
          onOverride={(selectedRuleIds) => handleRuleConflictResolution(selectedRuleIds, 'override')}
        />
      );
      
    case 'appointment-conflicts':
      return (
        <AppointmentConflictsScreen
          newRule={currentPreferenceData || {
            title: 'New Rule',
            frequency: '10/day',
            occurrence: 'Daily',
            availability: '8:00 am - 8:00 pm',
            timePeriod: '03 Feb 2025 - 31 Dec 2025'
          }}
          impactedAppointments={conflictData?.appointmentConflicts || []}
          impactCount={conflictData?.appointmentConflicts?.length || 0}
          onBack={handleBackToAdd}
          onLetThemBe={() => handleAppointmentConflictResolution('let-be')}
          onCancelAll={() => handleAppointmentConflictResolution('cancel')}
          onRescheduleAll={() => handleAppointmentConflictResolution('reschedule')}
          onViewAllAppointments={() => console.log('View all appointments')}
        />
      );
      
    case 'service-rules':
      return (
        <ServiceRulesList
          serviceName={selectedService?.serviceName || 'General Medical Test'}
          rules={sampleServiceRules}
          onBack={handleBackToTable}
          onAddPreference={handleAddPreference}
          onEditRule={(ruleId) => console.log('Edit rule:', ruleId)}
          onDeleteRule={(ruleId) => console.log('Delete rule:', ruleId)}
          onReorderRules={(rules) => console.log('Reorder rules:', rules)}
          sortable={true}
        />
      );
      
    case 'success':
      return (
        <SuccessScreen
          serviceName={currentPreferenceData?.serviceName || 'Service Name'}
          locationName="Location Name"
          onViewAll={handleViewAllFromSuccess}
          onAddAnother={handleAddAnotherFromSuccess}
        />
      );
      
    default:
      return (
        <ServicePreferencesTable
          onAddPreference={handleAddPreference}
          onViewServiceRules={handleViewServiceRules}
          onEditPreference={handleEditPreference}
        />
      );
  }
};