import React, { useState, useEffect, use<PERSON>emo } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Check } from "lucide-react";
import { Label } from "@/components/ui/label";
import { InputPhone } from "@/components/common/InputPhone";
import { UploadCard } from "@/components/ui-components/Upload";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import { useCreateClient } from "@/hooks/useClients";
import { uploadImage } from "@/lib/api/upload";
import type { BusinessAttribute } from "@/types/businessAttributes";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { Checkbox } from "@/components/ui/checkbox";

interface AddPatientSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: PatientFormData) => void;
}

export interface PatientFormData {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
	image?: File;
	customFields?: Record<string, string | number | boolean | Date>;
}

export function AddPatientSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddPatientSheetProps) {
	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useBusinessAttributesForForm();

	const businessAttributes: BusinessAttribute[] = useMemo(() => {
		const attributes = (businessAttributesData as any)?.data || [];
		const deduplicatedAttributes = attributes.reduce(
			(acc: BusinessAttribute[], current: BusinessAttribute) => {
				const existingIndex = acc.findIndex(
					(attr) => attr.key === current.key
				);

				if (existingIndex === -1) {
					acc.push(current);
				} else {
					const existing = acc[existingIndex];
					if (current.is_system_field && !existing.is_system_field) {
						acc[existingIndex] = current;
					}
				}
				return acc;
			},
			[]
		);

		const filteredAttributes = deduplicatedAttributes.filter(
			(attr: BusinessAttribute) => {
				const mainFormFields = [
					"first_name",
					"last_name",
					"email",
					"phone_number",
					"profile_picture_url",
					"is_active",
				];
				return !mainFormFields.includes(attr.key);
			}
		);

		return filteredAttributes;
	}, [businessAttributesData, isLoadingAttributes]);

	const [formData, setFormData] = useState<PatientFormData>({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
		customFields: {},
	});
	const [file, setFile] = useState<File | null>(null);
	const [isSuccess, setIsSuccess] = useState(false);
	const [isUploading, setIsUploading] = useState(false);
	const [profilePictureUrl, setProfilePictureUrl] = useState<string | null>(
		null
	);

	const createClientMutation = useCreateClient({
		onSuccess: () => {
			setIsSuccess(true);
			onSubmit?.({
				...formData,
				image: file || undefined,
			});
		},
		onError: (error) => {
			console.error("Error creating client:", error);
		},
	});

	useEffect(() => {
		if (businessAttributes.length > 0) {
			const initialCustomFields: Record<
				string,
				string | number | boolean | Date
			> = {};
			businessAttributes.forEach((attr) => {
				switch (attr.type) {
					case "checkbox":
					case "boolean":
						initialCustomFields[attr.key] = false;
						break;
					case "number":
						initialCustomFields[attr.key] = "";
						break;
					case "date":
						initialCustomFields[attr.key] = "";
						break;
					default:
						initialCustomFields[attr.key] = "";
				}
			});
			setFormData((prev) => ({
				...prev,
				customFields: initialCustomFields,
			}));
		}
	}, [businessAttributes]);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value, type, checked } = e.target;

		if (name.startsWith("customFields.")) {
			const fieldName = name.split(".")[1];
			let fieldValue: string | number | boolean = value;
			if (type === "checkbox") {
				fieldValue = checked;
			} else if (type === "number") {
				fieldValue = value === "" ? "" : Number(value);
			}

			setFormData({
				...formData,
				customFields: {
					...formData.customFields,
					[fieldName]: fieldValue,
				},
			});
		} else {
			setFormData({
				...formData,
				[name]: value,
			});
		}
	};

	const handleCustomFieldChange = (
		fieldName: string,
		value: string | number | boolean | Date
	) => {
		setFormData({
			...formData,
			customFields: {
				...formData.customFields,
				[fieldName]: value,
			},
		});
	};

	const renderCustomFieldInput = (attr: BusinessAttribute) => {
		const currentValue = formData.customFields?.[attr.key] || "";

		switch (attr.type) {
			case "email":
				return (
					<Input
						id={attr.key}
						name={`customFields.${attr.key}`}
						type="email"
						value={String(currentValue)}
						onChange={handleInputChange}
						className="h-9 text-xs"
						placeholder={`Enter ${attr.label.toLowerCase()}`}
						required={attr.is_required}
					/>
				);

			case "phone":
				return (
					<InputPhone
						variant="with-country-dropdown"
						value={String(currentValue)}
						onChange={(value) =>
							handleCustomFieldChange(attr.key, value)
						}
						defaultCountry="US"
						placeholder={`Enter ${attr.label.toLowerCase()}`}
						className="w-full"
						showFlag={true}
						format="international"
						searchable={true}
						showValidation={true}
					/>
				);

			case "date":
				return (
					<DatePicker
						variant="default"
						value={
							currentValue instanceof Date
								? currentValue
								: undefined
						}
						onChange={(date) => {
							if (date instanceof Date) {
								handleCustomFieldChange(attr.key, date);
							} else {
								handleCustomFieldChange(attr.key, "");
							}
						}}
						placeholder={`Select ${attr.label.toLowerCase()}`}
						className="h-9 w-full"
					/>
				);

			case "boolean":
				return (
					<div className="flex items-center space-x-2">
						<Checkbox
							id={attr.key}
							checked={Boolean(currentValue)}
							onCheckedChange={(checked) =>
								handleCustomFieldChange(
									attr.key,
									Boolean(checked)
								)
							}
						/>
						<Label
							htmlFor={attr.key}
							className="text-xs font-normal"
						>
							{attr.label}
						</Label>
					</div>
				);

			case "dropdown":
				if (!attr.options || attr.options.length === 0) {
					return (
						<Input
							id={attr.key}
							name={`customFields.${attr.key}`}
							type="text"
							value={String(currentValue)}
							onChange={handleInputChange}
							className="h-9 text-xs"
							placeholder={`Enter ${attr.label.toLowerCase()}`}
							required={attr.is_required}
						/>
					);
				}
				return (
					<Select
						value={String(currentValue)}
						onValueChange={(value) => {
							if (typeof value === "string") {
								handleCustomFieldChange(attr.key, value);
							}
						}}
					>
						<SelectTrigger className="h-9 text-xs">
							<SelectValue
								placeholder={`Select ${attr.label.toLowerCase()}`}
							/>
						</SelectTrigger>
						<SelectContent>
							{attr.options.map((option) => (
								<SelectItem
									key={option.value}
									value={option.value}
								>
									{option.label || option.value}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				);

			case "checkbox":
				return (
					<div className="flex items-center space-x-2">
						<input
							type="checkbox"
							id={attr.key}
							name={`customFields.${attr.key}`}
							checked={Boolean(currentValue)}
							onChange={handleInputChange}
							className="h-4 w-4"
						/>
						<Label
							htmlFor={attr.key}
							className="text-xs font-normal"
						>
							{attr.label}
						</Label>
					</div>
				);

			case "number":
				return (
					<Input
						id={attr.key}
						name={`customFields.${attr.key}`}
						type="number"
						value={String(currentValue)}
						onChange={handleInputChange}
						className="h-9 text-xs"
						placeholder={`Enter ${attr.label.toLowerCase()}`}
						required={attr.is_required}
					/>
				);

			case "attachment":
				return (
					<div className="text-xs text-gray-500 italic">
						Attachment fields are not supported in this form
					</div>
				);

			case "text":
			case "textarea":
			default:
				return (
					<Input
						id={attr.key}
						name={`customFields.${attr.key}`}
						type="text"
						value={String(currentValue)}
						onChange={handleInputChange}
						className="h-9 text-xs"
						placeholder={`Enter ${attr.label.toLowerCase()}`}
						required={attr.is_required}
					/>
				);
		}
	};

	const uploadFileToServer = async (file: File) => {
		setIsUploading(true);
		try {
			const url = await uploadImage(file);
			setProfilePictureUrl(url);
		} catch (error) {
			console.error("Error uploading file:", error);
			setFile(null);
			setProfilePictureUrl(null);
		} finally {
			setIsUploading(false);
		}
	};

	const handleFileUpload = () => {
		const input = document.createElement("input");
		input.type = "file";
		input.accept = ".svg,.png,.jpg,.jpeg";
		input.onchange = async (e) => {
			const target = e.target as HTMLInputElement;
			if (target.files && target.files[0]) {
				const selectedFile = target.files[0];
				setFile(selectedFile);
				await uploadFileToServer(selectedFile);
			}
		};
		input.click();
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
	};

	const handleDrop = async (e: React.DragEvent) => {
		e.preventDefault();
		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			const droppedFile = e.dataTransfer.files[0];
			setFile(droppedFile);
			await uploadFileToServer(droppedFile);
		}
	};

	const handleRemoveFile = () => {
		setFile(null);
		setProfilePictureUrl(null);
	};

	const handleChangeFile = () => {
		handleFileUpload();
	};

	const resetForm = () => {
		const resetCustomFields: Record<
			string,
			string | number | boolean | Date
		> = {};
		businessAttributes.forEach((attr) => {
			switch (attr.type) {
				case "checkbox":
				case "boolean":
					resetCustomFields[attr.key] = false;
					break;
				case "number":
					resetCustomFields[attr.key] = "";
					break;
				case "date":
					resetCustomFields[attr.key] = "";
					break;
				default:
					resetCustomFields[attr.key] = "";
			}
		});

		setFormData({
			firstName: "",
			lastName: "",
			email: "",
			phone: "",
			customFields: resetCustomFields,
		});
		setFile(null);
		setProfilePictureUrl(null);
		setIsSuccess(false);
	};

	const handleSubmit = async () => {
		if (
			!formData.firstName ||
			!formData.lastName ||
			!formData.email ||
			!formData.phone
		) {
			return;
		}
		const processedCustomFields: Record<string, string | number | boolean> =
			{};
		if (formData.customFields) {
			Object.entries(formData.customFields).forEach(([key, value]) => {
				if (value instanceof Date) {
					processedCustomFields[key] = value
						.toISOString()
						.split("T")[0]; 
				} else {
					processedCustomFields[key] = value;
				}
			});
		}

		const clientData = {
			first_name: formData.firstName,
			last_name: formData.lastName,
			email: formData.email,
			phone_number: formData.phone,
			profile_picture_url: profilePictureUrl || undefined,
			attributes: processedCustomFields,
		};

		createClientMutation.mutate(clientData);
	};

	const handleAddAnother = () => {
		resetForm();
	};

	const handleDone = () => {
		resetForm();
		onOpenChange(false);
	};

	const handleClose = () => {
		resetForm();
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={handleClose}>
			<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
				<div className="flex h-full flex-col gap-6">
					<SheetHeader className="flex-row items-center justify-between space-y-0 px-0">
						<div className="flex flex-col gap-2">
							<SheetTitle className="text-base font-semibold">
								Add New Patient
							</SheetTitle>
							<p className="text-xs text-gray-500">
								Fill out the information below to add a patient.
							</p>
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleClose}
							className="h-9 w-9 rounded-md"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetHeader>

					{isSuccess ? (
						<div className="flex flex-1 flex-col items-center justify-center gap-8">
							<div className="flex flex-col items-center gap-11">
								<div className="rounded-full bg-[#005893]/20 p-8">
									<Check className="h-12 w-12 text-[#005893]" />
								</div>
								<div className="flex w-full max-w-72 flex-col items-center gap-3">
									<h2 className="text-center text-xl font-semibold">
										Patients Added
									</h2>
									<p className="text-center text-sm">
										A new patients has been added
										successfully.
									</p>
								</div>
							</div>
							<div className="flex justify-center gap-3">
								<Button
									variant="secondary"
									onClick={handleAddAnother}
									className="h-9"
								>
									Add Another Patients
								</Button>
								<Button
									onClick={handleDone}
									className="h-9 w-20 bg-[#005893] hover:bg-[#004a7a]"
								>
									Done
								</Button>
							</div>
						</div>
					) : (
						<>
							<div className="flex-1">
								<div className="space-y-4">
									<div className="space-y-2">
										<Label
											htmlFor="firstName"
											className="text-xs"
										>
											First Name *
										</Label>
										<Input
											id="firstName"
											name="firstName"
											value={formData.firstName}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter first name"
										/>
									</div>

									<div className="space-y-2">
										<Label
											htmlFor="lastName"
											className="text-xs"
										>
											Last Name *
										</Label>
										<Input
											id="lastName"
											name="lastName"
											value={formData.lastName}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter last name"
										/>
									</div>
									<div className="space-y-2">
										<Label
											htmlFor="email"
											className="text-xs"
										>
											Email Address *
										</Label>
										<Input
											id="email"
											name="email"
											type="email"
											value={formData.email}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter email address"
										/>
									</div>
									<div className="space-y-2">
										<div className="flex items-center gap-2">
											<label className="text-xs font-medium text-gray-900">
												Phone Number *
											</label>
										</div>
										<InputPhone
											variant="with-country-dropdown"
											value={formData.phone}
											onChange={(value) =>
												setFormData({
													...formData,
													phone: value,
												})
											}
											defaultCountry="US"
											placeholder="Enter phone number"
											className="w-full"
											showFlag={true}
											format="international"
											searchable={true}
											showValidation={true}
										/>
									</div>
									<UploadCard
										variant="horizontal-compact"
										width="w-full"
										title={
											isUploading
												? "Uploading file..."
												: "Click or drag file here to upload file"
										}
										description={
											isUploading
												? "Please wait while your file is being uploaded"
												: profilePictureUrl
													? "File uploaded successfully! Ready to submit."
													: "Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
										}
										buttonText={
											isUploading
												? "Uploading..."
												: "Browse"
										}
										accept=".svg,.png,.jpg,.jpeg"
										onBrowseClick={
											isUploading
												? undefined
												: handleFileUpload
										}
										onDragOver={
											isUploading
												? undefined
												: handleDragOver
										}
										onDrop={
											isUploading ? undefined : handleDrop
										}
										isUploaded={!!file}
										fileName={file?.name}
										fileSize={
											file
												? `${(file.size / 1024 / 1024).toFixed(2)} MB`
												: undefined
										}
										filePreview={
											profilePictureUrl || undefined
										}
										onRemove={
											isUploading
												? undefined
												: handleRemoveFile
										}
										onChange={
											isUploading
												? undefined
												: handleChangeFile
										}
									/>
									{isLoadingAttributes ? (
										<div className="space-y-2">
											<div className="h-4 animate-pulse rounded bg-gray-200"></div>
											<div className="h-9 animate-pulse rounded bg-gray-200"></div>
										</div>
									) : (
										businessAttributes.map((attr) => (
											<div
												key={attr.id}
												className="space-y-2"
											>
												<Label
													htmlFor={attr.key}
													className="text-xs"
												>
													{attr.label}
													{attr.is_required && " *"}
												</Label>
												{renderCustomFieldInput(attr)}
											</div>
										))
									)}
								</div>
							</div>

							<div className="flex justify-end gap-3 pb-5">
								<Button
									variant="outline"
									onClick={handleClose}
									className="h-9"
								>
									Close
								</Button>
								<Button
									onClick={handleSubmit}
									disabled={createClientMutation.isPending}
									className="h-9 bg-[#005893]"
								>
									{createClientMutation.isPending
										? "Saving..."
										: "Save Patients"}
								</Button>
							</div>
						</>
					)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
