import { Upload, Plus, ChevronLeft, UserPlus } from "lucide-react";
import { Controller } from "react-hook-form";
import { InputText } from "@/components/common/InputText";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { Control, FieldErrors } from "react-hook-form";
import type { StationFormData } from "../../../schemas/stationSchema";
import { Uploader } from "@/components/common/Uploader";
import type { CreateProviderStationRequest } from "../../../types";
import { useEffect } from "react";

interface StationDetailsStepProps {
	control: Control<StationFormData>;
	errors: FieldErrors<StationFormData>;
	watch: (field?: keyof StationFormData) => any;
	setValue: (field: keyof StationFormData, value: any, options?: any) => void;
	onBack?: () => void;
	onAddServiceProvider?: () => void;
	providerFormData?: CreateProviderStationRequest;
	onProviderDataChange?: (
		field: keyof CreateProviderStationRequest,
		value: string
	) => void;
	showProviderSection?: boolean;
}

export function StationDetailsStep({
	control,
	errors,
	watch,
	setValue,
	onBack,
	onAddServiceProvider,
	providerFormData,
	onProviderDataChange,
	showProviderSection = true,
}: StationDetailsStepProps) {
	// Debug: Log when provider form data changes
	useEffect(() => {
		console.log(
			"StationDetailsStep - providerFormData updated:",
			providerFormData
		);
	}, [providerFormData]);

	// Sync form values with provider data on mount and when provider data changes
	useEffect(() => {
		if (providerFormData) {
			// Sync station name if it exists in provider data
			if (
				providerFormData.name &&
				providerFormData.name !== watch("stationName")
			) {
				setValue("stationName", providerFormData.name);
			}
			// Sync description if it exists in provider data
			if (
				providerFormData.description &&
				providerFormData.description !== watch("description")
			) {
				setValue("description", providerFormData.description);
			}
		}
	}, [providerFormData, setValue, watch]);

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center gap-3">
				<button
					onClick={onBack}
					className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
				>
					<ChevronLeft className="h-5 w-5 text-gray-600" />
				</button>
				<div>
					<h3 className="text-lg font-bold text-gray-900">
						Add New Service Provider Station
					</h3>
					<p className="text-sm text-gray-500">
						Provide details to add a new Service Provider
					</p>
				</div>
			</div>

			{/* Station Name */}
			<div className="space-y-1.5">
				<label className="text-sm font-medium text-gray-900">
					Station name <span className="text-red-500">*</span>
				</label>
				<Controller
					name="stationName"
					control={control}
					render={({ field }) => (
						<InputText
							placeholder="Enter station name"
							value={field.value}
							onChange={(e) => {
								const value = e.target.value;
								field.onChange(value);
								// Also update provider form data
								onProviderDataChange?.("name", value);
							}}
							className="w-full border-gray-200"
							id="station-name"
							variant="default"
						/>
					)}
				/>
				{errors.stationName && (
					<p className="text-sm text-red-500">
						{errors.stationName.message}
					</p>
				)}
			</div>

			{/* File Upload */}
			<Uploader
				accept=".svg, .png, .jpg"
				onFilesChange={(files) => {
					// Handle file upload for station image
					if (files.length > 0 && onProviderDataChange) {
						const file = files[0];
						// For now, we'll just store the filename - actual upload handling
						// should be done in the parent component
						onProviderDataChange("image", file.name);
					}
				}}
				className="w-full"
				multiple={false}
				size="sm"
				uploadIcon={<Upload className="h-5 w-5 text-gray-500" />}
			/>

			{/* Description */}
			<div className="space-y-1.5">
				<label className="text-sm font-medium text-gray-900">
					Description
				</label>
				<Controller
					name="description"
					control={control}
					render={({ field }) => (
						<Textarea
							placeholder="Enter description"
							value={field.value || ""}
							onChange={(e) => {
								const value = e.target.value;
								field.onChange(value);
								// Also update provider form data
								onProviderDataChange?.("description", value);
							}}
							className="min-h-[100px] w-full resize-none border-gray-200"
						/>
					)}
				/>
			</div>

			{/* Add Service Provider - only show when showProviderSection is true */}
			{showProviderSection && (
				<div className="space-y-1.5">
					<Button
						variant="outline"
						className="h-auto w-full cursor-pointer justify-between border-gray-200 p-4 hover:bg-gray-50"
						onClick={onAddServiceProvider}
					>
						<div className="flex items-center gap-3">
							<div className="flex h-8.5 w-8.5 items-center justify-center rounded-md bg-gray-100">
								<UserPlus className="h-5 w-5 text-gray-500" />
							</div>
							<div className="space-y-2 text-left">
								<div className="font-bold text-gray-900">
									Add Service Provider
								</div>
								<div className="text-sm text-gray-500">
									Add provider through email
								</div>
							</div>
						</div>
						<Plus className="h-5 w-5 text-gray-400" />
					</Button>
				</div>
			)}
		</div>
	);
}
