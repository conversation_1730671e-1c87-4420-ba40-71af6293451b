import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { organizationsApi } from "../api/organizationsApi";
import type { Organization } from "../api/organizationsApi";
import { toast } from "sonner";

// Get a single organization by id
export const useOrganization = (id: string, enabled = true) => {
	return useQuery({
		queryKey: ["organization", id],
		queryFn: () => organizationsApi.getOrganization(id),
		enabled: enabled && !!id,
		staleTime: 5 * 60 * 1000,
	});
};

// Create organization mutation
export const useCreateOrganization = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: organizationsApi.createOrganization,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
			toast.success("Organization created successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to create organization");
		},
	});
};

// Update organization mutation
export const useUpdateOrganization = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			id,
			data,
		}: {
			id: string;
			data: Partial<Organization>;
		}) => organizationsApi.updateOrganization(id, data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
			toast.success("Organization updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update organization");
		},
	});
};

// Delete organization mutation
export const useDeleteOrganization = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (id: string) => organizationsApi.deleteOrganization(id),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
			toast.success("Organization deleted successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to delete organization");
		},
	});
};
