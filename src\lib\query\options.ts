import type {
	UseMutationOptions,
} from "@tanstack/react-query";

export const shortLivedQueryOptions = {
	staleTime: 30 * 1000, // 30 seconds
	cacheTime: 2 * 60 * 1000, // 2 minutes
	refetchOnWindowFocus: true,
} as const;

export const mediumLivedQueryOptions = {
	staleTime: 5 * 60 * 1000, // 5 minutes
	cacheTime: 10 * 60 * 1000, // 10 minutes
	refetchOnWindowFocus: false,
} as const;

export const longLivedQueryOptions = {
	staleTime: 60 * 60 * 1000, // 1 hour
	cacheTime: 2 * 60 * 60 * 1000, // 2 hours
	refetchOnWindowFocus: false,
} as const;

export const backgroundRefreshOptions = {
	staleTime: 2 * 60 * 1000, // 2 minutes
	cacheTime: 5 * 60 * 1000, // 5 minutes
	refetchInterval: 5 * 60 * 1000, // 5 minutes
	refetchOnWindowFocus: false,
} as const;

export const criticalDataOptions = {
	staleTime: 0, // Always stale
	cacheTime: 60 * 1000, // 1 minute
	refetchOnWindowFocus: true,
	refetchOnMount: true,
} as const;


export const defaultMutationOptions: Partial<UseMutationOptions> = {
	retry: 1,
	retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
};

export const criticalMutationOptions: Partial<UseMutationOptions> = {
	retry: 3,
	retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
};
