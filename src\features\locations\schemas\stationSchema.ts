import { z } from "zod";

export const stationSchema = z.object({
	stationName: z
		.string()
		.min(1, "Station name is required")
		.max(100, "Station name must be less than 100 characters"),

	description: z
		.string()
		.max(500, "Description must be less than 500 characters")
		.optional(),

	autoApprove: z.boolean().default(false),

	serviceVisibility: z.boolean().default(true),

	serviceAvailability: z.boolean().default(true),

	availableMethods: z
		.array(z.enum(["in-person", "video", "audio"]))
		.min(1, "At least one method must be selected")
		.default(["in-person"]),

	stationDuration: z
		.number()
		.min(1, "Duration must be at least 1")
		.max(480, "Duration cannot exceed 8 hours"),

	durationUnit: z.enum(["minutes", "hours"]).default("minutes"),

	applyStationTo: z
		.enum(["all-locations", "selected-locations"])
		.default("all-locations"),

	selectedStations: z.array(z.string()).default([]),
});

export type StationFormData = z.infer<typeof stationSchema>;

// Schema for each step validation
export const stationStepSchemas = {
	step1: stationSchema.pick({
		// Step 1 is optional location selection
	}),

	step2: stationSchema.pick({
		stationName: true,
		description: true,
		autoApprove: true,
		serviceVisibility: true,
		serviceAvailability: true,
		availableMethods: true,
		stationDuration: true,
		durationUnit: true,
	}),

	step3: stationSchema.pick({
		applyStationTo: true,
		selectedStations: true,
	}),
};
