import React, { useState, useEffect, useMemo, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>eader } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { InputPhone } from "@/components/common/InputPhone";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import { useClientDetail, useUpdateClient } from "@/hooks/useClients";
import type { BusinessAttribute } from "@/types/businessAttributes";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { Checkbox } from "@/components/ui/checkbox";

interface EditPatientSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: EditPatientFormData) => void;
	clientId?: string;
}

export interface EditPatientFormData {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
	isActive: boolean;
	customFields?: Record<string, string | number | boolean | Date>;
}

export function EditPatientSheet({
	open,
	onOpenChange,
	onSubmit,
	clientId,
}: EditPatientSheetProps) {
	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useBusinessAttributesForForm();

	const businessAttributes: BusinessAttribute[] = useMemo(() => {
		const attributes = (businessAttributesData as any)?.data || [];
		console.log("EditPatientSheet - Client attributes loaded:", {
			businessAttributesData,
			attributes,
			isLoading: isLoadingAttributes,
		});

		const deduplicatedAttributes = attributes.reduce(
			(acc: BusinessAttribute[], current: BusinessAttribute) => {
				const existingIndex = acc.findIndex(
					(attr) => attr.key === current.key
				);

				if (existingIndex === -1) {
					acc.push(current);
				} else {
					const existing = acc[existingIndex];
					if (current.is_system_field && !existing.is_system_field) {
						acc[existingIndex] = current;
					}
				}

				return acc;
			},
			[]
		);

		const filteredAttributes = deduplicatedAttributes.filter(
			(attr: BusinessAttribute) => {
				const mainFormFields = [
					"first_name",
					"last_name",
					"email",
					"phone_number",
					"profile_picture_url",
					"is_active",
				];
				return !mainFormFields.includes(attr.key);
			}
		);

		return filteredAttributes;
	}, [businessAttributesData, isLoadingAttributes]);

	const {
		data: clientDetailData,
		isLoading: isLoadingClient,
		error,
		refetch,
	} = useClientDetail(clientId || "", {
		enabled: !!clientId && open,
	});

	const updateClientMutation = useUpdateClient({
		onSuccess: () => {
			onSubmit?.(formData);
			onOpenChange(false);
		},
		onError: (error) => {
			console.error("Error updating client:", error);
		},
	});

	const [formData, setFormData] = useState<EditPatientFormData>({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
		isActive: false,
		customFields: {},
	});
	const isFormInitialized = useRef(false);

	useEffect(() => {
		if (!open) {
			isFormInitialized.current = false;
			return;
		}

		if (open && clientDetailData?.data && !isFormInitialized.current) {
			const clientData = clientDetailData.data;
			setFormData((prev) => ({
				...prev,
				firstName: clientData.first_name || "",
				lastName: clientData.last_name || "",
				email: clientData.email || "",
				phone: clientData.phone_number || "",
				isActive: clientData.is_active ?? true,
			}));

			isFormInitialized.current = true;
		}
	}, [open, clientDetailData, clientId]);

	useEffect(() => {
		if (
			open &&
			clientDetailData?.data &&
			businessAttributes.length > 0 &&
			isFormInitialized.current
		) {
			const clientData = clientDetailData.data;
			const attributesMap: Record<string, any> = {};

			if (clientData.attributes && Array.isArray(clientData.attributes)) {
				clientData.attributes.forEach((attr) => {
					attributesMap[attr.key] = attr.value;
				});
			}

			const initialCustomFields: Record<
				string,
				string | number | boolean
			> = {};
			businessAttributes.forEach((attr) => {
				switch (attr.type) {
					case "checkbox":
					case "boolean":
						initialCustomFields[attr.key] =
							attributesMap[attr.key] ?? false;
						break;
					case "number":
						initialCustomFields[attr.key] =
							attributesMap[attr.key] ?? "";
						break;
					case "date":
						initialCustomFields[attr.key] =
							attributesMap[attr.key] ?? "";
						break;
					default:
						initialCustomFields[attr.key] =
							attributesMap[attr.key] ?? "";
				}
			});

			setFormData((prev) => ({
				...prev,
				customFields: initialCustomFields,
			}));
		}
	}, [open, clientDetailData, businessAttributes]);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value, type, checked } = e.target;

		if (name.startsWith("customFields.")) {
			const fieldName = name.split(".")[1];
			let fieldValue: string | number | boolean = value;
			if (type === "checkbox") {
				fieldValue = checked;
			} else if (type === "number") {
				fieldValue = value === "" ? "" : Number(value);
			}

			setFormData({
				...formData,
				customFields: {
					...formData.customFields,
					[fieldName]: fieldValue,
				},
			});
		} else {
			setFormData({
				...formData,
				[name]: value,
			});
		}
	};

	const handleCustomFieldChange = (key: string, value: any) => {
		setFormData({
			...formData,
			customFields: {
				...formData.customFields,
				[key]: value,
			},
		});
	};

	const handleActiveToggle = (checked: boolean) => {
		setFormData({
			...formData,
			isActive: checked,
		});
	};

	const resetForm = () => {
		const resetCustomFields: Record<
			string,
			string | number | boolean | Date
		> = {};
		businessAttributes.forEach((attr) => {
			switch (attr.type) {
				case "checkbox":
				case "boolean":
					resetCustomFields[attr.key] = false;
					break;
				case "number":
					resetCustomFields[attr.key] = "";
					break;
				case "date":
					resetCustomFields[attr.key] = "";
					break;
				default:
					resetCustomFields[attr.key] = "";
			}
		});

		setFormData({
			firstName: "",
			lastName: "",
			email: "",
			phone: "",
			isActive: false,
			customFields: resetCustomFields,
		});

		isFormInitialized.current = false;
	};

	const handleSubmit = async () => {
		if (
			!formData.firstName ||
			!formData.lastName ||
			!formData.email ||
			!formData.phone ||
			!clientId
		) {
			return;
		}

		const processedCustomFields: Record<string, string | number | boolean> =
			{};
		if (formData.customFields) {
			Object.entries(formData.customFields).forEach(([key, value]) => {
				if (value instanceof Date) {
					processedCustomFields[key] = value
						.toISOString()
						.split("T")[0];
				} else {
					processedCustomFields[key] = value;
				}
			});
		}

		const updateData = {
			first_name: formData.firstName,
			last_name: formData.lastName,
			email: formData.email,
			phone_number: formData.phone,
			is_active: formData.isActive,
			attributes: processedCustomFields,
		};

		updateClientMutation.mutate({
			id: clientId,
			data: updateData,
		});
	};

	const renderCustomFieldInput = (attr: BusinessAttribute) => {
		const currentValue = formData.customFields?.[attr.key] || "";

		switch (attr.type) {
			case "email":
				return (
					<Input
						id={attr.key}
						name={`customFields.${attr.key}`}
						type="email"
						value={String(currentValue)}
						onChange={handleInputChange}
						className="h-9 text-xs"
						placeholder={`Enter ${attr.label.toLowerCase()}`}
						required={attr.is_required}
					/>
				);

			case "phone":
				return (
					<InputPhone
						variant="with-country-dropdown"
						value={String(currentValue)}
						onChange={(value) =>
							handleCustomFieldChange(attr.key, value)
						}
						defaultCountry="US"
						placeholder={`Enter ${attr.label.toLowerCase()}`}
						className="w-full"
						showFlag={true}
						format="international"
						searchable={true}
						showValidation={true}
					/>
				);

			case "date":
				return (
					<DatePicker
						variant="default"
						value={
							currentValue instanceof Date
								? currentValue
								: undefined
						}
						onChange={(date) => {
							if (date instanceof Date) {
								handleCustomFieldChange(attr.key, date);
							}
						}}
						placeholder={`Select ${attr.label.toLowerCase()}`}
						className="h-9 w-full"
					/>
				);

			case "boolean":
				return (
					<div className="flex items-center space-x-2">
						<Checkbox
							id={attr.key}
							checked={Boolean(currentValue)}
							onCheckedChange={(checked) =>
								handleCustomFieldChange(
									attr.key,
									Boolean(checked)
								)
							}
						/>
						<Label
							htmlFor={attr.key}
							className="text-xs font-normal"
						>
							{attr.label}
						</Label>
					</div>
				);

			case "number":
				return (
					<Input
						id={attr.key}
						name={`customFields.${attr.key}`}
						type="number"
						value={String(currentValue)}
						onChange={handleInputChange}
						className="h-9 text-xs"
						placeholder={`Enter ${attr.label.toLowerCase()}`}
						required={attr.is_required}
					/>
				);

			case "attachment":
				return (
					<div className="text-xs text-gray-500 italic">
						Attachment fields are not supported in this form
					</div>
				);

			case "text":
			case "textarea":
			default:
				return (
					<Input
						id={attr.key}
						name={`customFields.${attr.key}`}
						type="text"
						value={String(currentValue)}
						onChange={handleInputChange}
						className="h-9 text-xs"
						placeholder={`Enter ${attr.label.toLowerCase()}`}
						required={attr.is_required}
					/>
				);
		}
	};

	const handleClose = () => {
		resetForm();
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={handleClose}>
			<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
				<div className="flex h-full flex-col gap-6">
					<SheetHeader className="flex-row items-center justify-between space-y-0 px-0">
						<div className="flex flex-1 items-center justify-start gap-2">
							<div className="text-lg font-semibold">
								Edit Details
							</div>
						</div>
						<div className="flex items-center justify-start gap-2">
							<span className="text-xs font-medium">Active</span>
							<div className="flex items-center gap-1.5">
								<Switch
									checked={formData.isActive}
									onCheckedChange={handleActiveToggle}
								/>
								<span className="text-xs text-gray-500">
									{formData.isActive ? "On" : "Off"}
								</span>
							</div>
						</div>
					</SheetHeader>

					{isLoadingClient ? (
						<div className="flex flex-1 flex-col items-center justify-center gap-8">
							<div className="flex flex-col items-center gap-4">
								<div className="h-8 w-8 animate-spin rounded-full border-2 border-[#005893] border-t-transparent"></div>
								<p className="text-sm text-gray-500">
									Loading patient details...
								</p>
							</div>
						</div>
					) : error ? (
						<div className="flex flex-1 flex-col items-center justify-center gap-8">
							<div className="flex flex-col items-center gap-4">
								<p className="text-sm text-red-600">
									Failed to load patient details
								</p>
								<p className="text-xs text-gray-500">
									{String(error)}
								</p>
								<Button
									variant="outline"
									onClick={() => refetch()}
									className="mt-2"
								>
									Try Again
								</Button>
							</div>
						</div>
					) : (
						<>
							<div className="flex flex-1 flex-col gap-6">
								<div className="space-y-5">
									<div className="space-y-2">
										<Label
											htmlFor="firstName"
											className="text-xs"
										>
											First Name *
										</Label>
										<Input
											id="firstName"
											name="firstName"
											value={formData.firstName}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter first name"
										/>
									</div>

									<div className="space-y-2">
										<Label
											htmlFor="lastName"
											className="text-xs"
										>
											Last Name *
										</Label>
										<Input
											id="lastName"
											name="lastName"
											value={formData.lastName}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter last name"
										/>
									</div>

									<div className="space-y-2">
										<Label
											htmlFor="email"
											className="text-xs"
										>
											Email Address *
										</Label>
										<Input
											id="email"
											name="email"
											type="email"
											value={formData.email}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter email address"
										/>
									</div>

									<div className="space-y-2">
										<div className="flex items-center gap-2">
											<label className="text-xs font-medium text-gray-900">
												Phone Number *
											</label>
										</div>
										<InputPhone
											variant="with-country-dropdown"
											value={formData.phone}
											onChange={(value) =>
												setFormData({
													...formData,
													phone: value,
												})
											}
											defaultCountry="US"
											placeholder="Enter phone number"
											className="w-full"
											showFlag={true}
											format="international"
											searchable={true}
											showValidation={true}
										/>
									</div>
									{isLoadingAttributes ? (
										<div className="space-y-2"></div>
									) : (
										businessAttributes.map((attr) => (
											<div
												key={attr.id}
												className="space-y-2"
											>
												<Label
													htmlFor={attr.key}
													className="text-xs"
												>
													{attr.label}
													{attr.is_required && " *"}
												</Label>
												{renderCustomFieldInput(attr)}
											</div>
										))
									)}
								</div>
							</div>

							<div className="flex w-full items-center justify-between gap-2.5 pt-7 pb-5">
								<Button
									variant="outline"
									onClick={handleClose}
									className="h-9"
								>
									Cancel
								</Button>
								<Button
									onClick={handleSubmit}
									disabled={updateClientMutation.isPending}
									className="h-9 bg-[#005893] hover:bg-[#004a7a]"
								>
									{updateClientMutation.isPending
										? "Saving..."
										: "Save"}
								</Button>
							</div>
						</>
					)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
