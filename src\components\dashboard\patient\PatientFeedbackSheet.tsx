import {
	Sheet,
	SheetContent,	
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { X, Clock, User, Stethoscope } from "lucide-react";
import { Star } from "lucide-react";

interface PatientFeedbackSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	feedbackData?: {
		patientName: string;
		patientInitials: string;
		date: string;
		rating: number;
		service: string;
		doctor: string;
		location: string;
		questions: Array<{
			id: string;
			question: string;
			answer: string;
		}>;
		feedback?: string;
	};
}

const defaultFeedbackData = {
	patientName: "<PERSON> Anderson",
	patientInitials: "SA",
	date: "Mar 12, 2025 , 2:00 PM",
	rating: 4,
	service: "Skin Check-up",
	doctor: "Dr. <PERSON>",
	location: "Dermatology Services",
	questions: [
		{
			id: "1",
			question: "I found booking my appointment online easy.",
			answer: "Strongly Agree",
		},
		{
			id: "2",
			question: "I was able to select an appointment time that was convenient for me.",
			answer: "Agree",
		},
		{
			id: "3",
			question: "I found booking my appointment online easy.",
			answer: "Strongly Agree",
		},
		{
			id: "4",
			question: "I was able to select an appointment time that was convenient for me.",
			answer: "Agree",
		},
	],
	feedback: "Request a Position Swap for Critical Situations. Request a Position Swap for Critical Situations. Request a Position Swap for Critical Situations. Request a Position Swap for Critical Situations.",
};

export function PatientFeedbackSheet({
	open,
	onOpenChange,
	feedbackData = defaultFeedbackData,
}: PatientFeedbackSheetProps) {
	const handleClose = () => {
		onOpenChange(false);
	};

	const renderStars = (rating: number) => {
		return Array.from({ length: 5 }, (_, index) => (
			<Star
				key={index}
				className={`h-4 w-4 ${
					index < rating
						? "fill-amber-400 text-amber-400"
						: "fill-gray-300 text-gray-300"
				}`}
			/>
		));
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full !max-w-[600px] overflow-y-auto p-4 [&>button]:hidden">
				<div className="flex flex-col items-start justify-start gap-4">
					<div className="inline-flex w-full items-center justify-between">
						<div className="flex flex-1 items-center justify-start gap-3">
							<Avatar className="h-9 w-9 rounded-full">
								<AvatarFallback className="bg-gray-100 text-base font-semibold">
									{feedbackData.patientInitials}
								</AvatarFallback>
							</Avatar>
							<div className="text-base font-semibold text-gray-900">
								{feedbackData.patientName}
							</div>
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleClose}
							className="h-4 w-4 p-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
					<div className="inline-flex items-center justify-start gap-4">
						<div className="flex items-center justify-start gap-1">
							<Clock className="h-3 w-3 text-gray-500" />
							<div className="text-xs font-normal text-gray-900">
								{feedbackData.date}
							</div>
						</div>
						<div className="flex items-center justify-start gap-1">
							{renderStars(feedbackData.rating)}
						</div>
					</div>
					<div className="h-px w-full border-t border-gray-200" />
					<div className="inline-flex w-full items-start justify-start gap-2.5">
						<div className="flex flex-1 items-center justify-start gap-1">
							<Stethoscope className="h-3 w-3 text-gray-500" />
							<div className="text-xs font-normal text-gray-900">
								{feedbackData.location}
							</div>
						</div>
						<div className="flex flex-1 items-center justify-start gap-1">
							<User className="h-3 w-3 text-gray-500" />
							<div className="text-xs font-normal text-gray-900">
								{feedbackData.doctor}
							</div>
						</div>
					</div>
					<div className="flex w-full flex-col items-start justify-center gap-1.5">
						<div className="text-[8px] font-normal leading-3 text-gray-500">
							Service
						</div>
						<div className="text-xs font-normal leading-none text-gray-900">
							{feedbackData.service}
						</div>
					</div>
					<div className="h-px w-full rounded-full bg-gray-200" />
					{feedbackData.questions.map((item) => (
						<div
							key={item.id}
							className="flex w-full flex-col items-start justify-start gap-1.5"
						>
							<div className="w-full text-xs font-normal leading-none text-gray-900">
								{item.question}
							</div>
							<div className="inline-flex w-full flex-wrap content-start items-start justify-start gap-3">
								<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
									<div className="text-[10px] font-medium leading-3 text-gray-900">
										{item.answer}
									</div>
								</div>
							</div>
						</div>
					))}
					<div className="h-px w-full rounded-full bg-gray-200" />
					{feedbackData.feedback && (
						<div className="flex w-full flex-col items-start justify-center gap-1.5">
							<div className="text-[8px] font-normal leading-3 text-gray-500">
								Feedback
							</div>
							<div className="w-full text-xs font-normal leading-none text-gray-900">
								{feedbackData.feedback}
							</div>
						</div>
					)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
