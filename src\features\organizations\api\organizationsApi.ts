import { apiClient } from "@/lib/api/clients";

export interface Organization {
	id?: number;
	name: string;
	address: string;
	country: string;
	state: string;
	city: string;
	zip_code: string;
	phone_number: string;
	business_category_id: number;
	logo_url: string;
}

const ORGANIZATIONS_ENDPOINTS = {
	base: "/api/v1/organizations",
	byId: (id: string) => `/api/v1/organizations/${id}`,
} as const;

export const organizationsApi = {
	getOrganizations: async (): Promise<Organization[]> => {
		const response = await apiClient.get(ORGANIZATIONS_ENDPOINTS.base, {
			params: {
				include:
					"location_count,station_count,service_count,avg_waittime",
			},
		});
		return response.data.data;
	},
	getOrganization: async (id: string): Promise<Organization> => {
		const response = await apiClient.get(ORGANIZATIONS_ENDPOINTS.byId(id));
		return response.data;
	},
	createOrganization: async (data: Organization): Promise<Organization> => {
		const response = await apiClient.post(
			ORGANIZATIONS_ENDPOINTS.base,
			data
		);
		return response.data;
	},
	updateOrganization: async (
		id: string,
		data: Partial<Organization>
	): Promise<Organization> => {
		const response = await apiClient.put(
			ORGANIZATIONS_ENDPOINTS.byId(id),
			data
		);
		return response.data;
	},
	deleteOrganization: async (id: string): Promise<void> => {
		await apiClient.delete(ORGANIZATIONS_ENDPOINTS.byId(id));
	},
};
