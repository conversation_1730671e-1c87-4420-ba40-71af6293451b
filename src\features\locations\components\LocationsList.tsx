import { useState, type FC } from "react";
import { MapPin } from "lucide-react";
import { useLocations } from "../hooks";
import type { LocationsFilters } from "../types";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { LocationTab } from "./LocationTab";
import { ServicesTab } from "./ServicesTab";
import { ScheduleSettingsTab } from "./ScheduleSettingsTab";
import { WalkInSettingsTab } from "./WalkInSettingsTab";
import { TeamMembersTab } from "./TeamMemberTabs";
import OrganizationSettingsTab from "./OrganizationSettingsTab";
import { useOrganizationContext } from "@/features/organizations/context";

export interface LocationsListProps {
	className?: string;
}

export const LocationsList: FC<LocationsListProps> = ({ className }) => {
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	// const { organizationId } = useOrganizationContext();
	// const { error } = useLocations(filters, organizationId!);

	const items = [
		{ value: "locations", label: "Locations" },
		{ value: "services", label: "Services" },
		{ value: "team-members", label: "Team Members" },
		{ value: "schedule-settings", label: "Schedule Settings" },
		{ value: "walk-in-settings", label: "Walk-in Settings" },
		{ value: "organization-settings", label: "Organization Settings" },
	];

	return (
		<div className={cn("flex flex-col gap-0.5 p-2", className)}>
			{/* Tabs */}
			<Tabs
				items={items}
				defaultValue="locations"
				useRouting={true}
				searchParamKey="location-tab"
			>
				<TabsContent value="team-members">
					<TeamMembersTab />
				</TabsContent>
				<TabsContent value="locations">
					<LocationTab />
				</TabsContent>
				<TabsContent value="services">
					<ServicesTab />
				</TabsContent>
				<TabsContent value="schedule-settings">
					<ScheduleSettingsTab />
				</TabsContent>
				<TabsContent value="walk-in-settings">
					<WalkInSettingsTab />
				</TabsContent>
				<TabsContent value="organization-settings">
					<OrganizationSettingsTab />
				</TabsContent>
			</Tabs>
		</div>
	);
};
