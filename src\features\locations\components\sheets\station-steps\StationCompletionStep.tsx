import { Check } from "lucide-react";
import { Button } from "@/components/ui/button";

interface StationCompletionStepProps {
	onCancel?: () => void;
	onViewStation?: () => void;
	providerName?: string;
	isImport?: boolean;
	isStationOnly?: boolean;
}

export function StationCompletionStep({
	onCancel,
	onViewStation,
	providerName = "Provider's Name",
	isImport = false,
	isStationOnly = false,
}: StationCompletionStepProps) {
	const title = isImport
		? "Station imported successfully"
		: "Station created successfully";
	const message = isImport
		? `has been imported, you can now access and manage this station.`
		: isStationOnly
			? `has been created, you can now access and manage this station.`
			: `has been created, you can now access and manage this station.`;

	return (
		<div className="flex h-full items-center justify-center">
			<div className="flex flex-col items-center justify-center space-y-6 py-12">
				{/* Success Icon */}
				<div className="flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
					<Check
						className="h-8 w-8 text-gray-700"
						strokeWidth={2.5}
					/>
				</div>

				{/* Success Message */}
				<div className="space-y-3 text-center">
					<h3 className="text-xl font-semibold text-gray-900">
						{title}
					</h3>
					<p className="max-w-sm text-sm text-gray-600">
						<span className="font-bold">{providerName}</span>{" "}
						{message}
					</p>
				</div>
			</div>
		</div>
	);
}
