import { apiClient } from "@/lib/api/clients";
import { uploadImage } from "@/lib/api/upload";
import type { CreateProviderStationRequest } from "../types";

// Backend API response interfaces
export interface CreateStationResponse {
	success: boolean;
	message: string;
	data: {
		id: number;
		name: string;
		image?: string;
		description?: string;
		service_provider_first_name: string;
		service_provider_last_name: string;
		service_provider_email: string;
		service_provider_phone: string;
		location_id: number;
		is_active: boolean;
		created_at: string;
		updated_at: string;
	};
}

export interface StationData {
	id: number;
	name: string;
	image?: string;
	description?: string;
	locations: Array<{
		id: number;
		name: string;
	}>;
	service_providers: Array<{
		id: number;
		first_name: string;
		last_name?: string;
		email: string;
		role: string;
	}>;
}

export interface GetStationsResponse {
	success: boolean;
	message: string;
	data: StationData[];
}

const STATIONS_ENDPOINTS = {
	base: "/api/v1/stations",
	byId: (id: string) => `/api/v1/stations/${id}`,
} as const;

export const stationsApi = {
	// Get stations for an organization and location
	getStations: async (
		locationId: string,
		orgId: number
	): Promise<GetStationsResponse> => {
		const response = await apiClient.get(STATIONS_ENDPOINTS.base, {
			headers: {
				"X-organizationId": orgId,
				"X-locationId": locationId,
			},
		});
		return response.data;
	},

	// Get all stations for an organization
	getAllStations: async (orgId: number): Promise<GetStationsResponse> => {
		const response = await apiClient.get(STATIONS_ENDPOINTS.base, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Create new station with image upload support
	createStation: async (
		data: CreateProviderStationRequest & { imageFile?: File },
		locationId: string,
		orgId: number
	): Promise<CreateStationResponse> => {
		let imageUrl = data.image;

		// Upload image if file is provided
		if (data.imageFile) {
			imageUrl = await uploadImage(data.imageFile);
		}

		const apiPayload = {
			name: data.name,
			image: imageUrl,
			description: data.description,
			service_provider_first_name: data.service_provider_first_name,
			service_provider_last_name: data.service_provider_last_name,
			service_provider_email: data.service_provider_email,
			service_provider_phone: data.service_provider_phone,
		};

		const response = await apiClient.post(
			STATIONS_ENDPOINTS.base,
			apiPayload,
			{
				headers: {
					"X-organizationId": orgId,
					"X-locationId": locationId,
				},
			}
		);
		return response.data;
	},
};
