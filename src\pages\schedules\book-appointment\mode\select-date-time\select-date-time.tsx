import { Label } from "@/components/ui/label";
import { RefactorMultiSelect } from "@/pages/schedules/components/custom-select";
import { providers, services } from "@/pages/schedules/db";
import { getFormattedHeader } from "@/pages/schedules/utils";
import { FaAngleLeft, FaAngleRight } from "react-icons/fa6";
import { FiUser } from "react-icons/fi";
import { GoCheckCircleFill } from "react-icons/go";
import { IoCalendar } from "react-icons/io5";
import { MdOutlineVideocam } from "react-icons/md";
import { PiSpeakerHighThin } from "react-icons/pi";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
} from "@/components/ui/pagination"
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { ToggleButton } from "@/components/common/ToggleButton";

export default function SelectDateTime({
    back,
    next
}: { back: () => void; next: () => void }) {
    const [selectedTypes, setSelectedTypes] = useState<string[]>([]);

    const toggleSelection = (type: string) => {
        setSelectedTypes((prev) =>
            prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
        );
    };

    const meetingTypes = [
        { id: "in-person", label: "In-Person", icon: FiUser },
        { id: "audio", label: "Audio", icon: PiSpeakerHighThin },
        { id: "video", label: "Video", icon: MdOutlineVideocam },
    ];

    return (
        <div className="mx-5 mt-5">

            <div className="space-y-6">
                <div className="flex flex-col gap-2">
                    <Label htmlFor="services" className="text-[#18181B] text-sm font-normal">Select Service</Label>

                    <RefactorMultiSelect
                        value={""}
                        setValue={() => { }}
                        placeholder=" "
                        label="Services"

                        options={services}
                    />
                </div>
                <div>
                    <div className="flex flex-col gap-2">
                        <Label htmlFor="providers" className="text-[#18181B] text-sm font-normal">Select Provider</Label>

                        <RefactorMultiSelect
                            value={""}
                            setValue={() => { }}
                            placeholder=" "
                            label="Providers"

                            options={providers}
                        />
                    </div>
                    <p className="text-xs text-[#71717A]">
                        Select a provider you want to book. You can select multiple providers together view all available time slots.
                    </p>
                </div>
                <div className="flex flex-col gap-3">
                    <Label htmlFor="appointmentType" className="text-[#18181B] text-sm font-normal">Appointment Method</Label>
                    <div className="flex flex-wrap gap-2">
                        {meetingTypes.map((type) => (
                            <ToggleButton
                                key={type.id}
                                label={type.label}
                                icon={type.icon as any}
                                isSelected={selectedTypes.includes(type.id)}
                                onClick={() => toggleSelection(type.id)}
                                className="py-5"
                            />
                        ))}
                    </div>
                </div>
            </div>

            <div className="mt-12 mb-5">
                <div className="max-w-[25rem] mx-auto">
                    <p className="text-center w-full mb-3">Select Date and Preferred Time Slot</p>
                    <div className="flex items-center justify-between">

                        <FaAngleLeft />
                        <div className="flex-1 flex items-center justify-center gap-x-1 text-center tracking-wider">
                            <h1 className="text-xl font-medium">
                                {getFormattedHeader(new Date(), "day")}
                            </h1>
                            <span
                                className="bg-[#F4F4F5] ml-1 p-2 rounded-md"
                            >
                                <IoCalendar />
                            </span>
                        </div>
                        <FaAngleRight />

                    </div>
                </div>

                <div className="relative rounded-lg bg-white overflow-hidden mt-6">
                    <div className="max-w-fit grid grid-flow-col place-content-center overflow-x-scroll overflow-y-hidden scrollbar-hide gap-x-3">
                        {Array.from({ length: 10 }).map((_, index) => (
                            <div key={index} className="w-[24rem]">
                                <div className="border border-[#E5E5E7] text-center py-3 rounded-lg">
                                    <h1 className="text-base">Monarch Corps</h1>

                                    <div className="grid grid-cols-2 gap-x-4 gap-y-3.5 mt-4 max-w-[22rem] mx-auto">

                                        {Array.from({ length: 10 }).map((_, index) => (
                                            <button
                                                key={index}
                                                tabIndex={0}
                                                className="text-nowrap cursor-pointer py-3 px-2.5 bg-[#0058930A] text-[#005893] border border-[#0000000A] rounded-lg text-sm font-normal"
                                            >
                                                <span>09:15 AM - 9:30 AM</span>
                                            </button>
                                        ))}

                                    </div>

                                    <div className="mt-6">
                                        <Pagination>
                                            <PaginationContent>
                                                <PaginationItem>
                                                    <FaAngleLeft />
                                                </PaginationItem>
                                                <PaginationItem>
                                                    <PaginationLink href="#">1</PaginationLink>
                                                </PaginationItem>
                                                <PaginationItem className="bg-white border rounded-lg">
                                                    <PaginationLink href="#">2</PaginationLink>
                                                </PaginationItem>
                                                <PaginationItem>
                                                    <PaginationEllipsis />
                                                </PaginationItem>
                                                <PaginationItem>
                                                    <PaginationLink href="#">3</PaginationLink>
                                                </PaginationItem>
                                                <PaginationItem>
                                                    <FaAngleRight />
                                                </PaginationItem>
                                            </PaginationContent>
                                        </Pagination>
                                    </div>

                                </div>
                            </div>
                        ))}
                    </div>
                </div>

            </div>

            <div className="flex items-center justify-end gap-x-3">
                <Button
                    type="button"
                    variant="outline"
                    onClick={back}
                    className="bg-[#F4F4F5] cursor-pointer"
                >
                    Back
                </Button>
                <Button
                    type="button"
                    className="cursor-pointer"
                    onClick={next}
                >
                    Next
                </Button>
            </div>

        </div>
    )
}