import { Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { ToggleButtonProps } from "./types";

export function ToggleButton({
	label,
	isSelected,
	onClick,
	icon: Icon,
	showCheckIndicator = true,
	className,
	disabled = false,
}: ToggleButtonProps) {
	return (
		<Button
			onClick={onClick}
			disabled={disabled}
			className={cn(
				"hover:bg-primary/20 relative flex flex-1 cursor-pointer items-center gap-3 rounded-lg border px-4 py-2.5 capitalize transition-all duration-200",
				isSelected
					? "border-primary bg-primary/10 text-primary shadow-sm"
					: "border-gray-200 bg-gray-50 text-gray-500 hover:border-gray-300 hover:bg-gray-100",
				disabled && "cursor-not-allowed opacity-50",
				className
			)}
		>
			{/* Icon */}
			{Icon && (
				<div className="relative">
					<Icon
						className={`h-4 w-4 ${isSelected ? "" : "opacity-60"}`}
					/>
				</div>
			)}

			{/* Label */}
			<span
				className={`text-sm font-medium ${isSelected ? "" : "opacity-80"}`}
			>
				{label}
			</span>

			{/* Checkbox-style indicator */}
			{showCheckIndicator && (
				<>
					{isSelected ? (
						<div className="bg-primary/10 flex h-5 w-5 items-center justify-center rounded-full">
							<Check className="text-primary h-3 w-3" />
						</div>
					) : (
						<div className="h-5 w-5 rounded-full border-2 border-gray-300" />
					)}
				</>
			)}
		</Button>
	);
}
