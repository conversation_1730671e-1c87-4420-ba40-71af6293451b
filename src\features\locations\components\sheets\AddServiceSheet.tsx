import { useState } from "react";
import { X, User, Video, Volume2, Info } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { ChevronRight } from "lucide-react";
import type { CreateServiceRequest } from "../../types";
import { ExpirationSettings } from "@/components/common/ExpirationSettings";
import { LocationStationAccordion } from "@/components/common/LocationStationAccordion";
import type {
	Location as AccordionLocation,
	ApplyServiceOption,
} from "@/components/common/LocationStationAccordion";
import { servicesApi } from "@/features/locations/api/servicesApi";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useAppointmentMethods } from "../../hooks/useAppointmentMethods";
import { useLocations } from "../../hooks/useLocations";
import { Skeleton } from "@/components/ui/skeleton";
import type {
	AppointmentMethod,
	Location as ApiLocation,
	LocationsResponse,
} from "../../types";

// Extended Location interface that includes stations from the optimized backend
interface LocationWithStations extends ApiLocation {}

interface AddServiceSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: CreateServiceRequest) => void;
}

export function AddServiceSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddServiceSheetProps) {
	const { organizationId } = useOrganizationContext();
	const [currentStep, setCurrentStep] = useState(1);

	// Fetch appointment methods using React Query
	const {
		data: appointmentMethods = [],
		isLoading: isLoadingMethods,
		error: methodsError,
	} = useAppointmentMethods(organizationId);

	// Fetch locations with stations from backend
	const {
		data: locationsData,
		isLoading: isLoadingLocations,
		error: locationsError,
	} = useLocations({}, organizationId || undefined) as {
		data: LocationsResponse | undefined;
		isLoading: boolean;
		error: any;
	};

	const [formData, setFormData] = useState<CreateServiceRequest>({
		serviceName: "",
		description: "",
		autoApprove: false,
		serviceVisibility: true,
		serviceAvailability: true,
		availableMethods: [], // Start with empty array, will be populated when methods load
		serviceDuration: 0,
		durationUnit: "minutes",
		applyServiceTo: "all-locations",
		selectedLocations: [],
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSubmissionComplete, setIsSubmissionComplete] = useState(false);
	const [submissionError, setSubmissionError] = useState<string | null>(null);
	const [selectedLocationIds, setSelectedLocationIds] = useState<Set<string>>(
		new Set()
	);
	const [selectedStationIds, setSelectedStationIds] = useState<Set<string>>(
		new Set()
	);
	const [locationStationMap, setLocationStationMap] = useState<
		Map<string, Set<string>>
	>(new Map());

	// Map appointment methods to the format expected by the UI
	const availableMethods = appointmentMethods.map((method) => ({
		id: method.id.toString(),
		label: method.name,
		icon:
			method.name.toLowerCase() === "in person"
				? User
				: method.name.toLowerCase() === "video"
					? Video
					: method.name.toLowerCase() === "audio"
						? Volume2
						: User,
	}));

	// Transform locations data to match LocationStationAccordion format
	const locations: AccordionLocation[] =
		locationsData?.map((location: LocationWithStations) => ({
			id: location.id,
			name: location.name,
			stations: location.stations || [], // Backend now includes stations
		})) || [];

	const handleInputChange = (
		field: keyof CreateServiceRequest,
		value: any
	) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleMethodToggle = (methodId: string) => {
		setFormData((prev) => ({
			...prev,
			availableMethods: prev.availableMethods.includes(methodId)
				? prev.availableMethods.filter((id) => id !== methodId)
				: [...prev.availableMethods, methodId],
		}));
	};

	const handleLocationSelection = (locationId: string, checked: boolean) => {
		setSelectedLocationIds((prev) => {
			const newSet = new Set(prev);
			if (checked) {
				newSet.add(locationId);
			} else {
				newSet.delete(locationId);
			}
			return newSet;
		});
	};

	const handleStationSelection = (stationId: string, checked: boolean) => {
		setSelectedStationIds((prev) => {
			const newSet = new Set(prev);
			if (checked) {
				newSet.add(stationId);
			} else {
				newSet.delete(stationId);
			}
			return newSet;
		});

		// Update location-station mapping
		setLocationStationMap((prev) => {
			const newMap = new Map(prev);

			// Find which location this station belongs to
			const stationLocation = locations.find((location) =>
				location.stations?.some((station) => station.id === stationId)
			);

			if (stationLocation) {
				const locationStations =
					newMap.get(stationLocation.id) || new Set();
				if (checked) {
					locationStations.add(stationId);
				} else {
					locationStations.delete(stationId);
				}

				if (locationStations.size > 0) {
					newMap.set(stationLocation.id, locationStations);
				} else {
					newMap.delete(stationLocation.id);
				}
			}

			return newMap;
		});
	};

	const handleSubmit = async () => {
		if (
			!formData.serviceName.trim() ||
			!organizationId ||
			formData.availableMethods.length === 0
		) {
			return;
		}

		setIsSubmitting(true);
		setSubmissionError(null);

		try {
			await servicesApi.createService(
				formData,
				organizationId,
				selectedLocationIds,
				selectedStationIds,
				locationStationMap
			);

			// Mark submission as complete and move to success screen
			setIsSubmissionComplete(true);
			setCurrentStep(3);
			onSubmit?.();
		} catch (error) {
			console.error("Error submitting service:", error);
			setSubmissionError("Failed to create service. Please try again.");
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleReset = () => {
		// Reset all form state
		setFormData({
			serviceName: "",
			description: "",
			autoApprove: false,
			serviceVisibility: true,
			serviceAvailability: true,
			availableMethods: [], // Reset to empty array
			serviceDuration: 0,
			durationUnit: "minutes",
			applyServiceTo: "all-locations",
			selectedLocations: [],
		});
		setSelectedLocationIds(new Set());
		setSelectedStationIds(new Set());
		setLocationStationMap(new Map());
		setCurrentStep(1);
		setIsSubmitting(false);
		setIsSubmissionComplete(false);
		setSubmissionError(null);
	};

	const handleCancel = () => {
		handleReset();
		onOpenChange(false);
	};

	const handleNext = () => {
		if (currentStep === 1) {
			setCurrentStep(2);
		} else if (currentStep === 2) {
			// Start submission process and show loading in step 2
			handleSubmit();
		} else if (currentStep === 3 && isSubmissionComplete) {
			// Close the sheet from success screen
			handleReset();
			onOpenChange(false);
		}
	};

	const canProceed = () => {
		if (currentStep === 1) {
			return (
				formData.serviceName.trim().length > 0 &&
				formData.availableMethods.length > 0 &&
				formData.serviceDuration > 0
			);
		}
		if (currentStep === 2) {
			// Can proceed if not loading locations and not currently submitting
			return !isLoadingLocations && !locationsError && !isSubmitting;
		}
		if (currentStep === 3) {
			// Can proceed from success screen
			return isSubmissionComplete;
		}
		return true;
	};

	const renderStepContent = () => {
		switch (currentStep) {
			case 1:
				return (
					<div className="space-y-6">
						{/* Service Name */}
						<div className="space-y-1.5">
							<label className="text-sm font-semibold text-[#323539]">
								Service Name{" "}
								<span className="text-red-500">*</span>
							</label>
							<InputText
								placeholder="Blood test"
								value={formData.serviceName}
								onChange={(e) =>
									handleInputChange(
										"serviceName",
										e.target.value
									)
								}
								className="w-full border-[#e5e5e7] text-[#323539]"
								id="service-name"
								variant="default"
							/>
						</div>

						{/* Description */}
						<div className="space-y-1.5">
							<label className="text-sm font-semibold text-[#323539]">
								Description
							</label>
							<Textarea
								placeholder="Blood test"
								value={formData.description}
								onChange={(e) =>
									handleInputChange(
										"description",
										e.target.value
									)
								}
								className="min-h-[114px] w-full resize-none border-[#e5e5e7] text-[#323539]"
							/>
						</div>

						{/* Select Preferences */}
						<div className="space-y-2">
							<h3 className="text-sm font-medium text-[#323539]">
								Select Preferences
							</h3>
							<div className="space-y-0">
								{/* Auto Approve */}
								<div className="flex items-center justify-between border-b-[0.5px] border-[#bfbfbf] px-6 py-3">
									<div className="flex items-center gap-2">
										<span className="text-sm text-zinc-900">
											Auto Approve
										</span>
										<Info className="h-3 w-3 text-zinc-400" />
									</div>
									<div className="flex items-center gap-1.5">
										<Switch
											checked={formData.autoApprove}
											onCheckedChange={(checked) =>
												handleInputChange(
													"autoApprove",
													checked
												)
											}
										/>
										<span className="text-xs text-zinc-500">
											{formData.autoApprove
												? "On"
												: "Off"}
										</span>
									</div>
								</div>

								{/* Service Visibility */}
								<div className="flex items-center justify-between border-b border-[#bfbfbf] px-6 py-3">
									<div className="flex items-center gap-2">
										<span className="text-sm text-zinc-900">
											Service Visibility
										</span>
										<Info className="h-3 w-3 text-zinc-400" />
									</div>
									<div className="flex items-center gap-1.5">
										<Switch
											checked={formData.serviceVisibility}
											onCheckedChange={(checked) =>
												handleInputChange(
													"serviceVisibility",
													checked
												)
											}
										/>
										<span className="text-xs text-zinc-500">
											{formData.serviceVisibility
												? "On"
												: "Off"}
										</span>
									</div>
								</div>

								{/* Service Availability */}
								<div className="flex items-center justify-between px-6 py-3">
									<div className="flex items-center gap-2">
										<span className="text-sm text-zinc-900">
											Service Availability
										</span>
										<Info className="h-3 w-3 text-zinc-400" />
									</div>
									<div className="flex items-center gap-1.5">
										<Switch
											checked={
												formData.serviceAvailability
											}
											onCheckedChange={(checked) =>
												handleInputChange(
													"serviceAvailability",
													checked
												)
											}
										/>
										<span className="text-xs text-zinc-500">
											{formData.serviceAvailability
												? "On"
												: "Off"}
										</span>
									</div>
								</div>
							</div>
						</div>

						{/* Service Available in Methods */}
						<div className="space-y-5 border-b border-[#bfbfbf] pb-3">
							<div className="space-y-2">
								<h3 className="text-sm font-medium text-zinc-900">
									Service Available in Methods{" "}
									<span className="text-red-500">*</span>
								</h3>
								{isLoadingMethods ? (
									<div className="flex items-center justify-center py-4">
										<span className="text-sm text-gray-500">
											Loading methods...
										</span>
									</div>
								) : methodsError ? (
									<div className="flex items-center justify-center py-4">
										<span className="text-sm text-red-500">
											Failed to load appointment methods
										</span>
									</div>
								) : availableMethods.length === 0 ? (
									<div className="flex items-center justify-center py-4">
										<span className="text-sm text-gray-500">
											No appointment methods available
										</span>
									</div>
								) : (
									<>
										<div className="flex gap-3">
											{availableMethods.map((method) => {
												const Icon = method.icon;
												const isSelected =
													formData.availableMethods.includes(
														method.id
													);
												return (
													<button
														key={method.id}
														type="button"
														onClick={() =>
															handleMethodToggle(
																method.id
															)
														}
														className={`flex cursor-pointer items-center gap-2.5 rounded-md border-[0.5px] p-2 ${
															isSelected
																? "border-zinc-100 bg-white"
																: "border-zinc-200 bg-white"
														}`}
													>
														<Icon className="h-3 w-3" />
														<span className="text-xs font-medium text-zinc-800">
															{method.label}
														</span>
														<Checkbox
															checked={isSelected}
															onCheckedChange={() =>
																handleMethodToggle(
																	method.id
																)
															}
															className="h-3 w-3"
														/>
													</button>
												);
											})}
										</div>
										{formData.availableMethods.length ===
											0 && (
											<p className="text-xs text-red-500">
												Please select at least one
												appointment method
											</p>
										)}
									</>
								)}
							</div>
						</div>

						{/* Service Duration */}
						<div className="flex items-start justify-between">
							<label className="mt-1 text-sm font-medium text-[#323539]">
								Service Duration
							</label>
							{/* Expiration Section */}
							<ExpirationSettings
								value={formData.serviceDuration.toString()}
								onValueChange={(value) =>
									handleInputChange("serviceDuration", value)
								}
								unit={formData.durationUnit}
								onUnitChange={(unit) =>
									handleInputChange("durationUnit", unit)
								}
								useSpecificDate={false}
								onUseSpecificDateChange={() => {}}
								label=""
							/>
						</div>
					</div>
				);
			case 2:
				if (isLoadingLocations) {
					return (
						<div className="flex items-center justify-center py-8">
							<span className="text-sm text-gray-500">
								Loading locations...
							</span>
						</div>
					);
				}

				if (locationsError) {
					return (
						<div className="flex items-center justify-center py-8">
							<span className="text-sm text-red-500">
								Failed to load locations. Please try again.
							</span>
						</div>
					);
				}

				if (locations.length === 0) {
					return (
						<div className="flex items-center justify-center py-8">
							<span className="text-sm text-gray-500">
								No locations available
							</span>
						</div>
					);
				}

				// Show submission progress if submitting
				if (isSubmitting) {
					return (
						<div className="flex flex-col items-center justify-center space-y-4 py-12">
							<div className="space-y-2 text-center">
								<Skeleton className="mx-auto h-8 w-8 rounded-full" />
								<Skeleton className="mx-auto h-4 w-32" />
								<Skeleton className="mx-auto h-3 w-48" />
							</div>
						</div>
					);
				}

				// Show error if submission failed
				if (submissionError) {
					return (
						<div className="flex flex-col items-center justify-center space-y-4 py-8">
							<div className="text-center text-red-500">
								<div className="mb-2 text-lg font-semibold">
									❌ Creation Failed
								</div>
								<p className="text-sm">{submissionError}</p>
							</div>
						</div>
					);
				}

				return (
					<LocationStationAccordion
						locations={locations}
						selectedLocationIds={selectedLocationIds}
						selectedStationIds={selectedStationIds}
						onLocationSelectionChange={handleLocationSelection}
						onStationSelectionChange={handleStationSelection}
					/>
				);
			case 3:
				return (
					<div className="flex flex-col items-center justify-center space-y-6 py-12">
						{/* Success Icon */}
						<div className="text-6xl text-green-500">✅</div>

						{/* Success Message */}
						<div className="space-y-2 text-center">
							<h3 className="text-xl font-semibold text-gray-900">
								Service Created Successfully!
							</h3>
							<p className="text-sm text-gray-600">
								Your service "{formData.serviceName}" has been
								created and is now available.
							</p>
						</div>

						{/* Service Details Summary */}
						<div className="w-full space-y-2 rounded-lg bg-gray-50 p-4">
							<h4 className="text-sm font-medium text-gray-900">
								Service Details:
							</h4>
							<div className="space-y-1 text-xs text-gray-600">
								<div>
									<strong>Name:</strong>{" "}
									{formData.serviceName}
								</div>
								{formData.description && (
									<div>
										<strong>Description:</strong>{" "}
										{formData.description}
									</div>
								)}
								<div>
									<strong>Duration:</strong>{" "}
									{formData.serviceDuration}{" "}
									{formData.durationUnit}
								</div>
								<div>
									<strong>Methods:</strong>{" "}
									{formData.availableMethods.length} selected
								</div>
								<div>
									<strong>Locations:</strong>{" "}
									{selectedLocationIds.size} selected
								</div>
								{selectedStationIds.size > 0 && (
									<div>
										<strong>Stations:</strong>{" "}
										{selectedStationIds.size} selected
									</div>
								)}
							</div>
						</div>

						{/* Next Steps */}
						<div className="space-y-2 text-center">
							<p className="text-xs text-gray-500">
								Your service is now live and available for
								booking.
							</p>
						</div>
					</div>
				);
			default:
				return null;
		}
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-9 py-9 sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-2xl font-semibold tracking-tight text-zinc-950">
							Add New Service
						</SheetTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6 rounded-sm"
						>
							<X className="h-3.5 w-3.5" />
							<span className="sr-only">Close</span>
						</Button>
					</div>
					<p className="text-[15px] text-zinc-500">
						{currentStep === 1
							? "Add Service information Below"
							: currentStep === 2
								? isSubmitting
									? "Creating your service..."
									: "Select which locations and stations provide this service"
								: "Service created successfully!"}
					</p>
				</SheetHeader>

				<div className="flex-1 space-y-5 pb-6">
					{renderStepContent()}
				</div>

				{/* Footer */}
				<SheetFooter className="mt-6 flex-row justify-end gap-3 p-0">
					{currentStep !== 3 && (
						<Button
							variant="outline"
							onClick={handleCancel}
							className="h-10 w-20 cursor-pointer"
							disabled={isSubmitting}
						>
							Cancel
						</Button>
					)}
					<Button
						className="h-10 cursor-pointer bg-[#005893] hover:bg-[#005893]/90"
						onClick={handleNext}
						disabled={!canProceed()}
					>
						{isSubmitting
							? "Creating..."
							: currentStep === 1
								? "Next"
								: currentStep === 2
									? submissionError
										? "Retry"
										: "Create Service"
									: "Done"}
					</Button>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
