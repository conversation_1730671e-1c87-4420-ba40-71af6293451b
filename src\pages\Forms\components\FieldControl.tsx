import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { IoAddCircleOutline } from "react-icons/io5";
import { PiFileArrowUp, PiImageSquare } from "react-icons/pi";
import { Type } from "lucide-react";
import { LuStretchHorizontal } from "react-icons/lu";
import {
	TooltipProvider,
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import type { FieldType } from "@/pages/Forms/types";

interface FieldControlProps {
	sectionIndex: number;
	onAddField: (sectionIndex: number, type: FieldType) => void;
	onAddSection: () => void;
	isVisible: boolean;
	position?: "field" | "section";
}

export const FieldControl: React.FC<FieldControlProps> = ({
	sectionIndex,
	onAddField,
	onAddSection,
	isVisible,
	position = "section",
}) => {
	if (!isVisible) return null;

	const positionClass =
		position === "field"
			? "absolute -right-16 top-1/2 -translate-y-1/2"
			: "absolute -right-16 top-0";

	return (
		<div
			className={`${positionClass} !mt-0 flex flex-col items-center justify-center gap-2 rounded-md border bg-white px-2 py-4 shadow-md`}
		>
			<TooltipProvider>
				<Tooltip delayDuration={0}>
					<TooltipTrigger asChild>
						<Button
							type="button"
							variant="outline"
							className="rounded-full border-none p-2"
							onClick={() => onAddField(sectionIndex, "text")}
						>
							<IoAddCircleOutline size={16} />
						</Button>
					</TooltipTrigger>
					<TooltipContent side="left" sideOffset={10}>
						Add Field
					</TooltipContent>
				</Tooltip>

				<Tooltip delayDuration={0}>
					<TooltipTrigger asChild>
						<Button
							type="button"
							variant="outline"
							className="rounded-full border-none p-2"
							onClick={() =>
								onAddField(sectionIndex, "attachment")
							}
						>
							<PiFileArrowUp size={16} />
						</Button>
					</TooltipTrigger>
					<TooltipContent side="left" sideOffset={10}>
						Add Attachment
					</TooltipContent>
				</Tooltip>

				<Tooltip delayDuration={0}>
					<TooltipTrigger asChild>
						<Button
							type="button"
							variant="outline"
							className="rounded-full border-none p-2"
							onClick={() => onAddField(sectionIndex, "longtext")}
						>
							<Type size={16} />
						</Button>
					</TooltipTrigger>
					<TooltipContent side="left" sideOffset={10}>
						Add Long Text
					</TooltipContent>
				</Tooltip>

				<Tooltip delayDuration={0}>
					<TooltipTrigger asChild>
						<Button
							type="button"
							variant="outline"
							className="rounded-full border-none p-2"
							onClick={() =>
								onAddField(sectionIndex, "infoImage")
							}
						>
							<PiImageSquare size={16} />
						</Button>
					</TooltipTrigger>
					<TooltipContent side="left" sideOffset={10}>
						Add Informational Image
					</TooltipContent>
				</Tooltip>

				<Tooltip delayDuration={0}>
					<TooltipTrigger asChild>
						<Button
							type="button"
							variant="outline"
							className="rounded-full border-none p-2"
							onClick={onAddSection}
						>
							<LuStretchHorizontal size={16} />
						</Button>
					</TooltipTrigger>
					<TooltipContent side="left" sideOffset={10}>
						Add Section
					</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		</div>
	);
};
