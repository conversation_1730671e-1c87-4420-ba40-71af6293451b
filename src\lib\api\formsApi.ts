import { apiClient } from "./clients";

const FORMS_ENDPOINTS = {
	base: "/api/v1/client-categories/get-forms-for-conditional",
	customIntakes:
		"/api/v1/client-categories/get-custom-intakes-for-conditional",
} as const;

export interface FormOption {
	id: string;
	label: string;
}

export interface FormQuestion {
	id: string;
	question: string;
	type: string;
	options: Array<{
		id: string;
		label: string;
	}>;
}

export interface FormData {
	form_id: string;
	form_title: string;
	questions: FormQuestion[];
}

export interface GetFormsForConditionalResponse {
	data: FormData[];
}

export interface CustomIntakeOption {
	id: number;
	label: string;
}

export interface CustomIntakeData {
	id: number;
	title: string;
	type: string;
	options: CustomIntakeOption[];
}

export interface GetCustomIntakesForConditionalResponse {
	data: CustomIntakeData[];
}

export const formsApi = {
	getFormsForConditional: async (
		organizationId: number
	): Promise<GetFormsForConditionalResponse> => {
		const response = await apiClient.get(FORMS_ENDPOINTS.base, {
			headers: {
				"X-organizationId": organizationId,
			},
		});
		return response.data;
	},
	getCustomIntakesForConditional: async (
		organizationId: number
	): Promise<GetCustomIntakesForConditionalResponse> => {
		const response = await apiClient.get(FORMS_ENDPOINTS.customIntakes, {
			headers: {
				"X-organizationId": organizationId,
			},
		});
		return response.data;
	},
};
