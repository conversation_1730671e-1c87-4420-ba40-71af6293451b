import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { <PERSON>rollArea } from "@/components/ui/scroll-area";
import { FileLock2 } from "lucide-react";
import { Search, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Bell, CalendarDays } from "lucide-react";


interface AccessLog {
	id: string;
	actor: string;
	action: string;
	timestamp: string;
	details?: string;
}

function formatDate(date: Date) {
	return new Intl.DateTimeFormat("en-GB", {
		day: "2-digit",
		month: "short",
		year: "numeric",
		hour: "numeric",
		minute: "numeric",
		hour12: true,
	}).format(date);
}

function parseTimestamp(raw: string): Date {
	// Replace em dash with regular dash or comma
	const safe = raw.replace(/—/g, ",");
	return new Date(safe);
}
  

const mockAccessLogs: AccessLog[] = [
	{
		id: "1",
		actor: "<PERSON>",
		action: "Booked an appointment",
		timestamp: "Oct 25, 2024 — 04:30 PM",
		details: "Dr. <PERSON><PERSON> • First Time Consultation",
	},
	{
		id: "2",
		actor: "<EMAIL>",
		action: "Accessed patient records",
		timestamp: "Oct 19, 2024 — 10:12 AM",
		details: "Patient: Michael Faraday • Record ID: #308923",
	},
	{
		id: "3",
		actor: "security_system",
		action: "Login attempt failed",
		timestamp: "Oct 17, 2024 — 02:01 AM",
		details: "User: unknown • IP: ************",
	},
	{
		id: "4",
		actor: "John Dalton",
		action: "Exported report",
		timestamp: "Oct 16, 2024 — 05:47 PM",
		details: "Audit log - October 2024",
	},
];

export function AccessLogsTabContent() {
	return (
		<div className="flex w-full flex-col gap-6">
			{/* Heading */}
			<div >
				<div className="flex w-full items-center justify-between">
					{/* Title */}
					<h1 className="flex items-center gap-2 text-left text-2xl font-bold">
						Access Logs
					</h1>

					{/* Search and Filter */}
					<div className="flex items-center gap-3">
						<div className="relative">
							<Search className="text-muted-foreground absolute top-2.5 left-3 h-4 w-4" />
							<Input
								type="text"
								placeholder="Search"
								className="h-9 w-64 rounded-md border border-gray-300 pl-9"
							/>
						</div>
						<Button
							variant="ghost"
							size="icon"
							className="h-9 w-9 border border-gray-300"
						>
							<Filter className="text-muted-foreground h-4 w-4" />
						</Button>
					</div>
				</div>
				<p className="text-muted-foreground mt-1 text-sm">
					Track access-related activity and security events.
				</p>
			</div>

			{/* List */}
			<div className="flex w-full flex-col items-start justify-start">
				{mockAccessLogs.map((log) => (
					<Card
						key={log.id}
						className="flex w-full rounded-none border-t border-transparent border-t-[#E4E4E7] pt-2 shadow-none"
					>
						<CardHeader className="relative flex w-full flex-nowrap items-center justify-between px-0 pb-2">
							<span className="w-full text-left text-sm text-gray-700">
								{log.actor} • {log.action}
								<span className="text-muted-foreground mt-1 block text-xs">
									{log.details}
								</span>
							</span>
							<span className="text-muted-foreground absolute top-1 right-3 flex items-center gap-1 text-xs">
								<CalendarDays className="h-4 w-4" />
								{formatDate(parseTimestamp(log.timestamp))}
							</span>
						</CardHeader>
					</Card>
				))}
			</div>
		</div>
	);
}
