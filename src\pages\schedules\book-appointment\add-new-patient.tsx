import { InputPhone } from "@/components/common/InputPhone";
import { InputText } from "@/components/common/InputText";
import { Uploader } from "@/components/common/Uploader";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { IoIosArrowBack } from "react-icons/io";

export default function AddNewPatient({
    children,
    open,
    onOpenChange
}: {
    open: boolean
    onOpenChange: (open: boolean) => void
    children: React.ReactNode
}) {
    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetTrigger>
                {children}
            </SheetTrigger>
            <SheetContent className="z-[1004] py-5 px-2 sm:max-w-[780px] overflow-scroll scrollbar-hide">
                <div className="px-4 mt-3 flex flex-col justify-between min-h-[76%]">
                    <div className="flex-1">
                        <div className="flex items-center gap-x-3">
                            <Button
                                onClick={() => onOpenChange(false)}
                                variant="outline"
                                className="cursor-pointer size-8"
                            >
                                <IoIosArrowBack />
                            </Button>
                            <h1 className="text-xl font-medium">Add New Patient</h1>
                        </div>
                        <div className="grid grid-cols-2 gap-x-2.5 gap-y-6.5 mt-8">

                            <div className="flex flex-col gap-y-3">
                                <Label htmlFor="first_name" className="font-normal">First Name</Label>
                                <InputText
                                    placeholder="Enter your first name"
                                    name="first_name"
                                    id="first_name"
                                    className="py-4.5"
                                />
                            </div>

                            <div className="flex flex-col gap-y-3">
                                <Label htmlFor="last_name" className="font-normal">Last Name</Label>
                                <InputText
                                    placeholder="Enter your last name"
                                    name="last_name"
                                    id="last_name"
                                    className="py-4.5"
                                />
                            </div>

                            <div className="flex flex-col gap-y-3">
                                <Label htmlFor="email" className="font-normal">Email*</Label>
                                <InputText
                                    placeholder="Enter your email"
                                    name="email"
                                    id="email"
                                    className="py-4.5"
                                    type="email"
                                />
                            </div>

                            <div className="flex flex-col gap-y-3">
                                <Label htmlFor="email" className="font-normal">Phone Number</Label>
                                <InputPhone
                                    placeholder="Enter your mobile number"
                                    name="phone_number"
                                    id="phone_number"
                                    className="!py-[19px]"
                                />
                            </div>

                            <div className="col-span-full mt-3">

                                <Uploader
                                    accept=".svg,.jpg,.jpeg,.png"
                                    descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
                                    maxFileSize={10485760}
                                    uploadText="Click or drag file here to upload file"
                                />

                            </div>

                            <div className="col-span-2 flex flex-col gap-y-3">
                                <Label htmlFor="validation_1" className="font-normal">[Validation Field 1]</Label>
                                <InputText
                                    placeholder="Validation 1"
                                    name="validation_1"
                                    id="validation_1"
                                    className="py-4.5"
                                />
                            </div>

                            <div className="col-span-2 flex flex-col gap-y-3">
                                <Label htmlFor="validation_2" className="font-normal">[Validation Field 2]</Label>
                                <InputText
                                    placeholder="Validation 2"
                                    name="validation_2"
                                    id="validation_2"
                                    className="py-4.5"
                                />
                            </div>

                        </div>
                    </div>
                    <div className="flex items-center justify-end gap-x-3 mt-7">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                            className="bg-[#F4F4F5] cursor-pointer"
                        >
                            Back
                        </Button>
                        <Button
                            type="button"
                            className="cursor-pointer"
                        >
                            Next
                        </Button>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    )
}