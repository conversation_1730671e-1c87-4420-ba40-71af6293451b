import { apiClient } from "@/lib/api/clients";

// Service data interfaces
export interface ServiceData {
	id: number;
	name: string;
	description: string;
	business_id: number;
	time_in_minute: number;
	is_available: boolean;
	auto_approve: boolean;
	is_visible: boolean;
	appointment_methods: Array<{
		id: number;
		name: string;
	}>;
}

export interface GetServicesResponse {
	success: boolean;
	message: string;
	data: ServiceData[];
}

// Frontend form data interface
export interface CreateServiceRequest {
	serviceName: string;
	description?: string;
	autoApprove: boolean;
	serviceVisibility: boolean;
	serviceAvailability: boolean;
	availableMethods: string[];
	serviceDuration: number;
	durationUnit: "minutes" | "hours";
	locationId?: string;
	applyServiceTo: "all-locations" | "selected-locations";
	selectedLocations?: string[];
}

// Backend API payload interface
interface LocationSelection {
	location_id: number;
	all_stations: boolean;
	station_ids: number[];
}

interface CreateServiceApiPayload {
	name: string;
	description?: string;
	time_in_minute: number;
	appointment_methods: number[];
	is_available: boolean;
	auto_approve: boolean;
	is_visible: boolean;
	apply_to_all_locations: boolean;
	location_selections: LocationSelection[];
}

export interface CreateServiceResponse {
	success: boolean;
	message: string;
	data: {
		id: number;
		serviceName: string;
		description: string;
		autoApprove: boolean;
		serviceVisibility: boolean;
		serviceAvailability: boolean;
		availableMethods: string[];
		serviceDuration: number;
		durationUnit: string;
		locationId?: string;
		isActive: boolean;
		createdAt: string;
		updatedAt: string;
	};
}

export const servicesApi = {
	createService: async (
		data: CreateServiceRequest,
		orgId: number,
		selectedLocationIds: Set<string> = new Set(),
		selectedStationIds: Set<string> = new Set(),
		locationStationMap: Map<string, Set<string>> = new Map()
	): Promise<CreateServiceResponse> => {
		// Convert duration to minutes if necessary
		const timeInMinute =
			data.durationUnit === "hours"
				? data.serviceDuration * 60
				: data.serviceDuration;

		// Build location selections based on the new schema
		const locationSelections: LocationSelection[] = [];

		// If applying to all locations, use the applyServiceTo field
		const applyToAllLocations = data.applyServiceTo === "all-locations";

		if (!applyToAllLocations) {
			// Build location selections for each selected location
			selectedLocationIds.forEach((locationId) => {
				const locationIdNum = parseInt(locationId);
				const stationsForLocation =
					locationStationMap.get(locationId) || new Set();
				const stationIds = Array.from(stationsForLocation).map((id) =>
					parseInt(id)
				);

				locationSelections.push({
					location_id: locationIdNum,
					all_stations: stationIds.length === 0, // If no specific stations selected, use all
					station_ids: stationIds,
				});
			});
		}

		// Transform frontend data to backend API format
		const apiPayload: CreateServiceApiPayload = {
			name: data.serviceName,
			description: data.description || "",
			time_in_minute: timeInMinute,
			appointment_methods: data.availableMethods.map((id) =>
				parseInt(id)
			),
			is_available: data.serviceAvailability,
			auto_approve: data.autoApprove,
			is_visible: data.serviceVisibility,
			apply_to_all_locations: applyToAllLocations,
			location_selections: locationSelections,
		};

		const response = await apiClient.post("/api/v1/services", apiPayload, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Get services for an organization
	getServices: async (
		orgId: number,
		filters: Record<string, any> = {}
	): Promise<GetServicesResponse> => {
		// Build query parameters from filters
		const params = new URLSearchParams(filters);

		// Object.entries(filters).forEach(([key, value]) => {
		// 	if (value !== undefined && value !== null) {
		// 		if (Array.isArray(value)) {
		// 			// Handle array parameters (multiple values with same key)
		// 			value.forEach((item) => {
		// 				if (item !== "") {
		// 					params.append(key, value.toString());
		// 				}
		// 			});
		// 		} else if (value !== "") {
		// 			// Handle single value parameters
		// 			params.append(key, value.toString());
		// 		}
		// 	}
		// });

		const queryString = params.toString();
		const url = queryString
			? `/api/v1/services?${queryString}`
			: "/api/v1/services";

		const response = await apiClient.get(url, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},
};
