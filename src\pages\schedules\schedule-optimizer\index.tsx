import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/sheet";
import Providers from "./providers";
import Recipients from "./recipients";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type { z } from "zod";
import { scheduleOptimizerSchema } from "../schema/schedule-optimizer";

type Props = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export default function ScheduleOptimizer({
    open,
    onOpenChange,
}: Props) {
    const form = useForm<z.infer<typeof scheduleOptimizerSchema>>({
        resolver: zodResolver(scheduleOptimizerSchema),
        defaultValues: {
            step: "providers",
            providers: [],
            services: [],
            appointmentMethod: "in-person",
        }
    })

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="z-[1003] py-5 px-2 sm:max-w-[610px]">
                <SheetHeader>
                    <SheetTitle className="text-2xl mb-1">
                        Schedule Optimizer Trigger
                    </SheetTitle>
                    <SheetDescription>
                        Bulk notify patients to fill out this empty time slot on [Provider]’s schedule.
                    </SheetDescription>
                </SheetHeader>

                {form.watch("step") === "providers" && <Providers form={form} />}
                {form.watch("step") === "recipients" && <Recipients form={form} />}

                <SheetFooter>
                    <div className="flex items-center justify-between">
                        <Button
                            variant={"ghost"}
                            className="cursor-pointer opacity-60 font-medium"
                            type="button"
                            onClick={() => form.reset()}
                        >
                            Reset
                        </Button>
                        <div className="space-x-4">
                            <SheetTrigger asChild>
                                <Button variant="outline" className="cursor-pointer py-5">
                                    Cancel
                                </Button>
                            </SheetTrigger>
                            <Button className="cursor-pointer py-5" onClick={() => form.setValue("step", "recipients")}>
                                Next
                            </Button>
                        </div>
                    </div>
                </SheetFooter>

            </SheetContent>
        </Sheet>
    );
}