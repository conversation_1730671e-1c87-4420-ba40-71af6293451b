import type { LucideIcon } from "lucide-react";

export interface ToggleButtonProps {
	/** The button label text */
	label: string;
	/** Whether the button is in selected state */
	isSelected: boolean;
	/** Click handler */
	onClick: () => void;
	/** Optional icon component */
	icon?: LucideIcon;
	/** Whether to show the check indicator (defaults to true) */
	showCheckIndicator?: boolean;
	/** Additional CSS classes */
	className?: string;
	/** Whether the button should be disabled */
	disabled?: boolean;
}
