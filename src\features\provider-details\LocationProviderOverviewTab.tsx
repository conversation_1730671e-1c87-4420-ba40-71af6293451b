import { useEffect, useState, type FC } from "react";
import { Search, Plus, MapPin, Settings2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { EditStationInformationSheet } from "../locations/components/sheets";
import { useLocations } from "../locations/hooks/useLocations";
import { LocationProviderCard } from "./components/LocationProviderCard";
import type {
	LocationsFilters,
	LocationsResponse,
	Location,
	CreateStationRequest,
} from "../locations/types";
import { SendBookingLinkSheet } from "@/features/schedule";
import { StationInformationSheet } from "../locations/components/sheets/station-information";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AddStationSheet } from "../locations/components/sheets";
import { AppointmentsView } from "./components";
import { AnalyticsView } from "./components/AnalyticsView";

interface LocationProviderOverviewTabProps {
	className?: string;
}

export const LocationProviderOverviewTab: FC<
	LocationProviderOverviewTabProps
> = ({ className }) => {
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddLocationForm, setShowAddLocationForm] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [
		showEditStationInformationSheet,
		setShowEditStationInformationSheet,
	] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<Location | null>(
		null
	);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);
	const [activeTab, setActiveTab] = useState("list");
	const { data: locationsData2, isLoading } = useLocations({
		page: currentPage,
		search: searchTerm,
		limit: 10,
	});

	// Debounced search filter
	useEffect(() => {
		const timer = setTimeout(() => {
			setFilters((prev) => ({
				...prev,
				search: searchTerm || undefined,
				page: 1,
			}));
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	const locationsData: LocationsResponse = {
		data: [
			{
				id: "1",
				name: "Dr. John Doe",
				address: {
					street: "5 Park Home Ave #130",
					city: "Toronto",
					state: "CA",
					zipCode: "12345",
					country: "USA",
				},
				phone: "************",
				email: "<EMAIL>",
				description: "This is a description of Location 1",
				isActive: true,
				timezone: "America/New_York",
				coordinates: {
					latitude: 40.7128,
					longitude: -74.006,
				},
				operatingHours: [],
				services: [],
				capacity: 100,
				amenities: [],
				organizationId: "1",
				createdAt: "2021-01-01",
				updatedAt: "2021-01-01",
			},
			{
				id: "2",
				name: "University of Toronto, Ontario Research Centre",
				address: {
					street: "5 Park Home Ave #130",
					city: "Toronto",
					state: "CA",
					zipCode: "12345",
					country: "USA",
				},
				phone: "************",
				email: "<EMAIL>",
				description: "This is a description of Location 1",
				isActive: true,
				timezone: "America/New_York",
				coordinates: {
					latitude: 40.7128,
					longitude: -74.006,
				},
				operatingHours: [],
				services: [],
				capacity: 100,
				amenities: [],
				organizationId: "1",
				createdAt: "2021-01-01",
				updatedAt: "2021-01-01",
			},
		],
		pagination: {
			page: 1,
			limit: 12,
			total: 2,
			totalPages: 1,
		},
	};

	const handleFilterChange = (newFilters: Partial<LocationsFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && locationsData?.data) {
			setSelectedLocations(
				locationsData.data.map((location) => location.id)
			);
		} else {
			setSelectedLocations([]);
		}
	};

	const handleLocationSelection = (locationId: string, selected: boolean) => {
		if (selected) {
			setSelectedLocations((prev) => [...prev, locationId]);
		} else {
			setSelectedLocations((prev) =>
				prev.filter((id) => id !== locationId)
			);
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleAddLocation = async (
		data: CreateStationRequest & { images?: File[] }
	) => {
		console.log("Adding new location:", data);
		// Here you would typically call your API to create the location
		// For now, we'll just log the data and close the form
		setShowAddLocationForm(false);
	};

	const handleViewLocation = (location: Location) => {
		setSelectedLocation(location);
		setShowLocationDetails(true);
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="grid h-screen max-h-screen w-full max-w-screen grid-cols-[400px_1fr] gap-x-7">
				<div>
					<h1 className="px-3 py-4 text-2xl font-semibold">
						Appointments
					</h1>
					<AppointmentsView />
				</div>

				<div className="w-full overflow-x-auto">
					<h1 className="px-3 py-4 text-2xl font-semibold">
						Analytics
					</h1>
					<AnalyticsView />
				</div>
			</div>

			{/* Add Location Sheet */}
			<AddStationSheet
				open={showAddLocationForm}
				onOpenChange={setShowAddLocationForm}
				onSubmit={handleAddLocation}
			/>

			{/* Location Details Sheet */}
			<StationInformationSheet
				open={showLocationDetails}
				onClose={() => setShowLocationDetails(false)}
				onSendBookingLink={() => setShowSendBookingLinkSheet(true)}
			/>

			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>

			{/* Edit Station Information Sheet */}
			<EditStationInformationSheet
				open={showEditStationInformationSheet}
				onClose={() => setShowEditStationInformationSheet(false)}
				stationId="station-123"
				stationData={selectedLocation as any}
				onSave={async () => {}}
				onProviderEdit={() => {}}
				onProviderDelete={() => {}}
				onAddProvider={() => {}}
			/>
		</div>
	);
};
