import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
	clientsApi,
	type CreateClientRequest,
	type UpdateClientRequest,
	type ClientsFilters,
	type Client,
} from "@/lib/api/clientsApi";
import { queryKeys, mediumLivedQueryOptions } from "@/lib/query";
import { useUIStore } from "@/stores/uiStore";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import type { Patient } from "@/components/ui-components/AllPatientListCard";

export const transformClientToPatient = (client: Client): Patient => {
	const mapSyncStatus = (
		emrSyncStatus: string | null | undefined
	): Patient["syncStatus"] => {
		if (!emrSyncStatus) return null;

		switch (emrSyncStatus.toLowerCase()) {
			case "synced":
			case "success":
			case "completed":
				return "synced";
			case "failed":
			case "error":
				return "failed";
			case "pending":
			case "in_progress":
			case "processing":
				return "pending";
			default:
				return null;
		}
	};

	return {
		id: client.id.toString(),
		name: `${client.first_name} ${client.last_name}`,
		email: client.email,
		phone: client.phone_number,
		status: "Active", 
		lastVisit: new Date(client.updated_at).toLocaleDateString("en-US", {
			day: "2-digit",
			month: "short",
			year: "numeric",
		}),
		syncStatus: mapSyncStatus((client as any).emr_sync_status),
		profile_picture_url: (client as any).profile_picture_url, 
	};
};

export const transformClientDetailToPatientData = (clientDetail: any) => {
	const client = clientDetail.data;

	const additionalDetails = [];

	additionalDetails.push({
		label: "External ID",
		value: client.external_id || "Not available",
	});

	additionalDetails.push({
		label: "Last Visit",
		value: client.last_visit
			? new Date(client.last_visit).toLocaleDateString("en-US", {
					day: "2-digit",
					month: "short",
					year: "numeric",
				})
			: "No visits",
	});

	additionalDetails.push({
		label: "EMR Sync Status",
		value: client.emr_sync_status || "Not synced",
	});

	return {
		name: `${client.first_name} ${client.last_name}`,
		priority: "High",
		status: client.is_active ? "Active" : "Inactive",
		email: client.email,
		phone: client.phone_number,
		patientId: client.id.toString(),
		externalId: client.external_id || "",
		profilePictureUrl: client.profile_picture_url,
		lastVisit: client.last_visit
			? new Date(client.last_visit).toLocaleDateString("en-US", {
					day: "2-digit",
					month: "short",
					year: "numeric",
				})
			: "No visits",
		phoneVerified: client.phone_number_verified,
		categories: client.categories?.map((cat: any) => cat.name) || [],
		customIntake: [
			...additionalDetails,
			...(client.attributes?.map((attr: any) => ({
				label: attr.label || attr.key || "Custom Field",
				value: attr.value?.toString() || "",
			})) || []),
		],
		forms: [],
		messages: [],
	};
};

export const useClients = (
	filters: ClientsFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	const { organizationId } = useOrganizationContext();

	const filtersWithOrgId = {
		...filters,
		organization_id: organizationId || undefined,
	};

	return useQuery({
		queryKey: queryKeys.clients.list(filtersWithOrgId),
		queryFn: () => clientsApi.getClients(filtersWithOrgId),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useClientDetail = (
	id: string | number,
	options?: {
		enabled?: boolean;
	}
) => {
	const { organizationId } = useOrganizationContext();

	return useQuery({
		queryKey: queryKeys.clients.detail(id.toString()),
		queryFn: () =>
			clientsApi.getClientById(id, organizationId || undefined),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!id && !!organizationId,
	});
};

export const useCreateClient = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { addToast } = useUIStore();
	const { organizationId } = useOrganizationContext();

	return useMutation<any, any, CreateClientRequest>({
		mutationFn: (data: CreateClientRequest) =>
			clientsApi.createClient(data, organizationId || undefined),
		onSuccess: (data: any) => {
			addToast({
				type: "success",
				title: "Client Created",
				message: data.message || "Client has been created successfully",
			});
			queryClient.invalidateQueries({
				queryKey: queryKeys.clients.lists(),
			});

			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};

export const useUpdateClient = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { addToast } = useUIStore();
	const { organizationId } = useOrganizationContext();

	return useMutation<
		any,
		any,
		{ id: string | number; data: UpdateClientRequest }
	>({
		mutationFn: ({
			id,
			data,
		}: {
			id: string | number;
			data: UpdateClientRequest;
		}) => clientsApi.updateClient(id, data, organizationId || undefined),
		onSuccess: (data: any) => {
			addToast({
				type: "success",
				title: "Client Updated",
				message: "Client information has been updated successfully.",
			});

			queryClient.invalidateQueries({ queryKey: queryKeys.clients.all });

			if (data?.data?.id) {
				queryClient.invalidateQueries({
					queryKey: queryKeys.clients.detail(data.data.id.toString()),
				});
			}
			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			addToast({
				type: "error",
				title: "Update Failed",
				message:
					error?.message || "Failed to update client information.",
			});
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};

export const useDeleteClient = (options?: {
	onSuccess?: () => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { addToast } = useUIStore();
	const { organizationId } = useOrganizationContext();

	return useMutation<void, any, string | number>({
		mutationFn: (id: string | number) =>
			clientsApi.deleteClient(id, organizationId || undefined),
		onSuccess: () => {
			addToast({
				type: "success",
				title: "Client Deleted",
				message: "Client has been deleted successfully.",
			});
			queryClient.invalidateQueries({ queryKey: queryKeys.clients.all });

			options?.onSuccess?.();
		},
		onError: (error: any) => {
			addToast({
				type: "error",
				title: "Delete Failed",
				message:
					error?.message ||
					"Failed to delete client. Please try again.",
			});
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};
