import React, { useState } from "react";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";
import type { StarRatingProps } from "./types";

const sizeMap = {
	small: "h-3 w-3",
	default: "h-4 w-4",
	large: "h-5 w-5",
};

const colorMap = {
	yellow: {
		filled: "fill-current text-yellow-600",
		empty: "fill-current text-gray-300",
	},
	blue: {
		filled: "fill-current text-blue-600",
		empty: "fill-current text-gray-300",
	},
	green: {
		filled: "fill-current text-green-600",
		empty: "fill-current text-gray-300",
	},
	red: {
		filled: "fill-current text-red-600",
		empty: "fill-current text-gray-300",
	},
	purple: {
		filled: "fill-current text-purple-600",
		empty: "fill-current text-gray-300",
	},
};

export const StarRating: React.FC<StarRatingProps> = ({
	rating,
	maxRating = 5,
	size = "default",
	className = "",
	color = "yellow",
	interactive = false,
	onRatingChange,
}) => {
	const [hoverRating, setHoverRating] = useState<number | null>(null);

	const handleStarClick = (starIndex: number) => {
		if (interactive && onRatingChange) {
			const newRating = starIndex + 1;
			onRatingChange(newRating);
		}
	};

	const handleStarHover = (starIndex: number) => {
		if (interactive) {
			setHoverRating(starIndex + 1);
		}
	};

	const handleMouseLeave = () => {
		if (interactive) {
			setHoverRating(null);
		}
	};

	const displayRating = hoverRating !== null ? hoverRating : rating;

	return (
		<div
			className={cn("flex items-center gap-1", className)}
			onMouseLeave={handleMouseLeave}
		>
			{[...Array(maxRating)].map((_, index) => (
				<Star
					key={index}
					className={cn(
						sizeMap[size],
						index < displayRating
							? colorMap[color].filled
							: colorMap[color].empty,
						interactive &&
							"cursor-pointer transition-colors hover:scale-110"
					)}
					onClick={() => handleStarClick(index)}
					onMouseEnter={() => handleStarHover(index)}
				/>
			))}
		</div>
	);
};
