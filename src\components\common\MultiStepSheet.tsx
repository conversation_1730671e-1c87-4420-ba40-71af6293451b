import { X } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

interface MultiStepSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	title: string;
	description?: string;
	currentStep: number;
	totalSteps: number;
	onNext: () => void;
	onPrevious?: () => void;
	onReset?: () => void;
	onCancel: () => void;
	canProceed: boolean;
	isSubmitting: boolean;
	children: React.ReactNode;
	nextButtonText?: string;
	showPreviousButton?: boolean;
	showResetButton?: boolean;
	maxWidth?: string;
}

export function MultiStepSheet({
	open,
	onOpenChange,
	title,
	description,
	currentStep,
	totalSteps,
	onNext,
	onPrevious,
	onReset,
	onCancel,
	canProceed,
	isSubmitting,
	children,
	nextButtonText,
	show<PERSON>re<PERSON><PERSON><PERSON>on = false,
	showReset<PERSON><PERSON>on = false,
	maxWidth = "sm:max-w-[525px]",
}: MultiStepSheetProps) {
	const getNextButtonText = () => {
		if (nextButtonText) return nextButtonText;
		if (isSubmitting) return "Saving...";
		if (currentStep === totalSteps) return "Save";
		if (currentStep === totalSteps - 1) return "Add";
		return "Next";
	};

	const getDescription = () => {
		if (description) return description;
		if (currentStep === 1) {
			return "Add your Service Provider's station information";
		}
		return "Select which Service Providers provide this service";
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent
				className={`z-[1003] w-full overflow-y-auto px-9 py-9 ${maxWidth} gap-8.5 [&>button]:hidden`}
			>
				<SheetHeader className="p-0">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-2xl font-semibold tracking-tight text-zinc-950">
							{title}
						</SheetTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6 rounded-sm"
						>
							<X className="h-3.5 w-3.5" />
							<span className="sr-only">Close</span>
						</Button>
					</div>
					<p className="text-[15px] text-zinc-500">
						{getDescription()}
					</p>
				</SheetHeader>

				<div className="flex-1 space-y-5 pb-6">{children}</div>

				<SheetFooter className="mt-6 flex-row justify-between gap-3 p-0">
					<div className="flex items-center justify-start gap-3">
						{showResetButton && (
							<Button
								variant="ghost"
								onClick={onReset}
								className="h-10 w-20 cursor-pointer"
								disabled={isSubmitting}
							>
								Reset
							</Button>
						)}
						{showPreviousButton && currentStep > 1 && (
							<Button
								variant="outline"
								onClick={onPrevious}
								className="h-10 w-20 cursor-pointer"
								disabled={isSubmitting}
							>
								Back
							</Button>
						)}
					</div>
					<div className="flex items-center justify-end gap-3">
						<Button
							variant="outline"
							onClick={onCancel}
							className="bg-secondary h-10 w-20 cursor-pointer"
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						<Button
							className="h-10 cursor-pointer"
							onClick={onNext}
							disabled={isSubmitting || !canProceed}
						>
							{getNextButtonText()}
						</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
