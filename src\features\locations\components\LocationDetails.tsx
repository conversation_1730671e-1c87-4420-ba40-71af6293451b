import React from "react";
import type { Location } from "../types";

export interface LocationDetailsProps {
	location: Location;
	open: boolean;
	onClose: () => void;
	onLocationUpdated?: (location: Location) => void;
}

export const LocationDetails: React.FC<LocationDetailsProps> = ({
	location,
	open,
	onClose,
	onLocationUpdated,
}) => {
	if (!open) return null;

	// TODO: Implement detailed location view with edit/delete actions
	return (
		<div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
			<div className="mx-4 max-h-[90vh] w-full max-w-3xl overflow-y-auto rounded-lg bg-white p-6">
				<div className="mb-4 flex items-start justify-between">
					<h2 className="text-xl font-semibold">{location.name}</h2>
					<button
						className="text-gray-400 hover:text-gray-600"
						onClick={onClose}
					>
						×
					</button>
				</div>
				<p className="mb-4 text-gray-600">
					Detailed location view will be implemented here.
				</p>
				<div className="flex justify-end">
					<button
						className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
						onClick={onClose}
					>
						Close
					</button>
				</div>
			</div>
		</div>
	);
};
