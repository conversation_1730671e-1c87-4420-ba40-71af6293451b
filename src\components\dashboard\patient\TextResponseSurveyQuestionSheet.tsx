import { Sheet, SheetContent } from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";

interface TextResponse {
	id: string;
	text: string;
	userName: string;
	userEmail: string;
	date: string;
}

interface TextResponseSurveyQuestionSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	questionData?: {
		question: string;
		type: string;
		totalResponses: number;
		responses: TextResponse[];
	};
}

const defaultQuestionData = {
	question:
		"In what areas do you believe you have excelled and demonstrated exceptional performance?",
	type: "Open Text",
	totalResponses: 925,
	responses: [
		{
			id: "1",
			text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
			userName: "Jhon Doe",
			userEmail: "<EMAIL>",
			date: "03 May 2025",
		},
		{
			id: "2",
			text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
			userName: "<PERSON><PERSON> Doe",
			userEmail: "<EMAIL>",
			date: "03 May 2025",
		},
		{
			id: "3",
			text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
			userName: "Jhon Doe",
			userEmail: "<EMAIL>",
			date: "03 May 2025",
		},
		{
			id: "4",
			text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
			userName: "Jhon Doe",
			userEmail: "<EMAIL>",
			date: "03 May 2025",
		},
		{
			id: "5",
			text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
			userName: "Jhon Doe",
			userEmail: "<EMAIL>",
			date: "03 May 2025",
		},
		{
			id: "6",
			text: "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.",
			userName: "Jhon Doe",
			userEmail: "<EMAIL>",
			date: "03 May 2025",
		},
	],
};

export function TextResponseSurveyQuestionSheet({
	open,
	onOpenChange,
	questionData = defaultQuestionData,
}: TextResponseSurveyQuestionSheetProps) {
	const handleClose = () => {
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full !max-w-[600px] overflow-y-auto p-4 [&>button]:hidden">
				<div className="inline-flex w-full flex-col items-center justify-start gap-3 overflow-hidden bg-white">
					<div className="inline-flex w-full items-start justify-between">
						<div className="flex-1 text-base leading-7 font-semibold text-gray-900">
							{questionData.question}
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleClose}
							className="h-4 w-4 p-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
					<div className="inline-flex w-full items-center justify-center gap-2.5">
						<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
							<div className="text-[10px] leading-3 font-medium text-gray-900">
								{questionData.type}
							</div>
						</div>
						<div className="flex-1 text-xs leading-none font-normal text-gray-500">
							{questionData.totalResponses} Responses
						</div>
					</div>

					<div className="h-px w-full border-t border-gray-200" />

					<div className="flex w-full flex-col items-start justify-start gap-2.5">
						{questionData.responses.map((response) => (
							<div
								key={response.id}
								className="flex w-full flex-col items-center justify-start gap-1.5 rounded-lg border border-gray-200 p-3"
							>
								<div className="w-full text-xs leading-none font-normal text-gray-700">
									{response.text}
								</div>
								<div className="h-px w-full border-t border-gray-200" />
								<div className="inline-flex w-full items-center justify-start gap-2">
									<div className="text-xs leading-none font-semibold text-gray-900">
										{response.userName}
									</div>
									<div className="flex-1 text-[10px] leading-3 font-normal text-gray-500">
										{response.userEmail}
									</div>
									<div className="text-[10px] leading-3 font-normal text-gray-500">
										{response.date}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
