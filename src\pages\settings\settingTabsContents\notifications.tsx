import { Bell, CalendarDays } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface Notification {
	id: string;
	message: string;
	date: Date;
}

const mockNotifications: Notification[] = [
	{
		id: "1",
		message:
			"<PERSON> booked an appointment with Dr. <PERSON><PERSON> on Monday, 25th October 2024 at 04:30 PM for a First Time Consultation.",
		date: new Date("2024-10-25T16:30:00"),
	},
	{
		id: "2",
		message:
			"You updated the password <NAME_EMAIL> on Sept 12, 2024.",
		date: new Date("2024-09-12T13:12:00"),
	},
	{
		id: "3",
		message: "New policy agreement was accepted by 6 users this week.",
		date: new Date("2024-08-31T09:30:00"),
	},
];

function formatDate(date: Date) {
	const options: Intl.DateTimeFormatOptions = {
		day: "2-digit",
		month: "short",
		year: "numeric",
		hour: "numeric",
		minute: "numeric",
		hour12: true,
	};
	return new Intl.DateTimeFormat("en-GB", options).format(date);
}

export function NotificationsTabContent() {
	return (
		<div className="flex w-full flex-col gap-6">
			{/* Header */}
			<div>
				<div className="flex w-full gap-4 items-center justify-between">
					{/* Title */}
					<h1 className="flex items-center gap-2 text-left text-2xl font-bold">
						Notifications
					</h1>

					{/* Search and Filter */}
					<div className="flex items-center gap-3">
						<div className="relative">
							<Search className="text-muted-foreground absolute top-2.5 left-3 h-4 w-4" />
							<Input
								type="text"
								placeholder="Search"
								className="h-9 w-64 rounded-md border border-gray-300 pl-9"
							/>
						</div>
						<Button
							variant="ghost"
							size="icon"
							className="h-9 w-9 border border-gray-300"
						>
							<Filter className="text-muted-foreground h-4 w-4" />
						</Button>
					</div>
				</div>

				<p className="text-muted-foreground mt-1 text-sm">
					View recent activity and system updates.
				</p>
			</div>

			{/* List */}
			<div className="flex w-full flex-col items-start justify-start">
				{mockNotifications.map((notif) => (
					<Card
						key={notif.id}
						className="flex w-full rounded-none border-t border-transparent border-t-[#E4E4E7] pt-2 shadow-none"
					>
						<CardHeader className="relative flex w-full flex-nowrap items-center justify-between px-0 pb-2">
							<span className="w-full text-left text-sm text-gray-700">
								{notif.message}
							</span>
							<span className="text-muted-foreground absolute top-1 right-3 flex items-center gap-1 text-xs">
								<CalendarDays className="h-4 w-4" />
								{formatDate(notif.date)}
							</span>
						</CardHeader>
					</Card>
				))}
			</div>
		</div>
	);
}
