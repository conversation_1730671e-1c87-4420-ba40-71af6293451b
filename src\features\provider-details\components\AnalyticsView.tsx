import { type FC } from "react";
import { Star, <PERSON>, ChevronDown, ThumbsUp } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>hart,
	Line,
	XAxis,
	YAxis,
	CartesianGrid,
	ResponsiveContainer,
	PieChart,
	Pie,
	Cell,
} from "recharts";
import {
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent,
} from "@/components/ui/chart";

interface AnalyticsViewProps {
	className?: string;
}

export const AnalyticsView: FC<AnalyticsViewProps> = ({ className }) => {
	const submissionData = [
		{ name: "Returning", value: 75, fill: "hsl(var(--chart-6))" },
		{ name: "New", value: 45, fill: "hsl(var(--chart-7))" },
	];

	const completionData = [
		{ name: "Submitted", value: 60, fill: "hsl(var(--chart-8))" },
		{ name: "Drop-Off", value: 40, fill: "hsl(var(--chart-9))" },
	];

	const ratingData = [
		{ stars: 5, count: 32, percentage: 76 },
		{ stars: 4, count: 22, percentage: 52 },
		{ stars: 3, count: 16, percentage: 38 },
		{ stars: 2, count: 2, percentage: 5 },
		{ stars: 1, count: 12, percentage: 29 },
	];

	const chartData = [
		{ day: "Mon", value: 1.2 },
		{ day: "Tue", value: 3.1 },
		{ day: "Wed", value: 2.3 },
		{ day: "Thu", value: 1.8 },
		{ day: "Fri", value: 2.7 },
		{ day: "Sat", value: 2.9 },
	];

	return (
		<div className="w-full">
			<div className="space-y-6">
				{/* Top Row */}
				<div className="flex gap-4">
					{/* Submission Card */}
					<Card className="bg-foreground-subtle flex flex-1 flex-col divide-y p-1 shadow-md">
						<CardContent className="flex flex-1 items-center gap-2 p-4">
							<ChartContainer
								config={{
									returning: {
										label: "Returning",
										color: "hsl(var(--chart-6))",
									},
									new: {
										label: "New",
										color: "hsl(var(--chart-7))",
									},
								}}
								className="min-h-[48px] w-[48px]"
							>
								<ResponsiveContainer width="100%" height="100%">
									<PieChart>
										<ChartTooltip
											cursor={false}
											content={
												<ChartTooltipContent
													hideLabel
												/>
											}
										/>
										<Pie
											data={submissionData}
											cx="50%"
											cy="50%"
											innerRadius={18}
											outerRadius={23}
											dataKey="value"
										>
											{submissionData.map(
												(entry, index) => (
													<Cell
														key={`cell-${index}`}
														fill={entry.fill}
													/>
												)
											)}
										</Pie>
										<ChartTooltip
											content={<ChartTooltipContent />}
										/>
									</PieChart>
								</ResponsiveContainer>
							</ChartContainer>
							<div className="flex flex-col">
								<div className="flex items-center gap-x-2">
									<h1 className="text-2xl font-semibold">
										120
									</h1>
									<span className="text-subtle2 text-sm">
										Submission
									</span>
								</div>
								<div className="flex gap-4">
									<div className="flex items-center space-x-2">
										<div className="bg-chart-6 h-2 w-2 rounded-xs"></div>
										<span className="text-xs">
											Returning
										</span>
									</div>
									<div className="flex items-center space-x-2">
										<div className="bg-chart-7 h-2 w-2 rounded-xs"></div>
										<span className="text-xs">New</span>
									</div>
								</div>
							</div>
						</CardContent>

						<CardContent className="flex flex-1 items-center gap-2 p-4">
							<ChartContainer
								config={{
									returning: {
										label: "Returning",
										color: "hsl(var(--chart-8))",
									},
									new: {
										label: "New",
										color: "hsl(var(--chart-9))",
									},
								}}
								className="min-h-[48px] w-[48px]"
							>
								<ResponsiveContainer width="100%" height="100%">
									<PieChart>
										<ChartTooltip
											cursor={false}
											content={
												<ChartTooltipContent
													hideLabel
												/>
											}
										/>
										<Pie
											data={completionData}
											cx="50%"
											cy="50%"
											innerRadius={18}
											outerRadius={23}
											dataKey="value"
										>
											{completionData.map(
												(entry, index) => (
													<Cell
														key={`cell-${index}`}
														fill={entry.fill}
													/>
												)
											)}
										</Pie>
										<ChartTooltip
											content={<ChartTooltipContent />}
										/>
									</PieChart>
								</ResponsiveContainer>
							</ChartContainer>
							<div className="flex flex-col">
								<div className="flex items-center gap-x-2">
									<h1 className="text-2xl font-semibold">
										60%
									</h1>
									<span className="text-subtle2 text-sm whitespace-nowrap">
										Completion Rate
									</span>
								</div>
								<div className="flex gap-4">
									<div className="flex items-center space-x-2">
										<div className="bg-chart-8 h-2 w-2 rounded-xs"></div>
										<span className="text-xs">
											Submitted
										</span>
									</div>
									<div className="flex items-center space-x-2">
										<div className="bg-chart-9 h-2 w-2 rounded-xs"></div>
										<span className="text-xs whitespace-nowrap">
											Drop-Off
										</span>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Rating Card */}
					<Card className="bg-subtle bg-foreground-subtle w-full flex-3 shadow-md">
						<CardContent className="flex gap-3 divide-x p-6">
							<div className="flex flex-1 flex-col items-center gap-y-1 pr-4">
								<Star className="h-12 w-12 fill-amber-500 text-amber-500" />
								<div className="mb-1 flex items-baseline space-x-2">
									<span className="text-3xl font-bold">
										4.9
									</span>
									<span className="text-sm whitespace-nowrap text-gray-500">
										Average Rating
									</span>
								</div>

								<div className="flex items-center space-x-1 text-sm">
									<ThumbsUp className="h-4 w-4 text-amber-500" />
									<div className="mt-1 flex items-center gap-x-1">
										<span className="font-medium text-black">
											4,200
										</span>
										<span className="leading-none text-gray-500">
											Reviews
										</span>
									</div>
								</div>
							</div>

							<div className="flex-2 space-y-3">
								{ratingData.map((rating) => (
									<div
										key={rating.stars}
										className="flex items-center space-x-3"
									>
										<div className="flex items-center space-x-1">
											{[...Array(5)].map((_, i) => (
												<Star
													key={i}
													className={`h-4 w-4 ${
														i < rating.stars
															? "fill-gray-400 text-gray-400"
															: "text-gray-300"
													}`}
												/>
											))}
										</div>
										<div className="h-2 flex-1 rounded-full bg-gray-200">
											<div
												className="h-2 rounded-full bg-amber-500"
												style={{
													width: `${rating.percentage}%`,
												}}
											></div>
										</div>
										<span className="w-6 text-right text-sm text-gray-600">
											{rating.count}
										</span>
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Station Visits Card */}
				<Card className="bg-subtle shadow-md">
					<CardHeader className="pb-6">
						<div className="flex items-center justify-between">
							<CardTitle className="text-xl font-semibold">
								Station Visits
							</CardTitle>
							<div className="flex items-center space-x-4">
								<Button
									variant="outline"
									size="sm"
									className="flex items-center space-x-2 bg-transparent"
								>
									<Calendar className="h-4 w-4" />
									<span>Jan 20, 2022 - Jun 09, 2022</span>
								</Button>
								<Button
									variant="outline"
									size="sm"
									className="flex items-center space-x-2 bg-transparent"
								>
									<span>All Services</span>
									<ChevronDown className="h-4 w-4" />
								</Button>
							</div>
						</div>
					</CardHeader>
					<CardContent>
						{/* Metrics Row */}
						<div className="mb-8 grid grid-cols-2 gap-6 divide-x md:grid-cols-4">
							<div className="flex flex-col gap-y-1 pr-4">
								<div className="mb-1 text-sm text-gray-500">
									Average wait time
								</div>
								<div className="text-2xl font-bold">
									30 mins
								</div>
							</div>
							<div className="flex flex-col gap-y-1 pr-4">
								<div className="mb-1 flex items-center justify-between text-sm text-gray-500">
									Appointments
									<span className="text-sm font-medium text-green-600">
										+25.66%
									</span>
								</div>
								<div className="flex items-baseline space-x-2">
									<span className="text-2xl font-bold">
										30
									</span>
								</div>
							</div>
							<div className="flex flex-col gap-y-1 pr-4">
								<div className="mb-1 flex items-center justify-between text-sm text-gray-500">
									Walk-in
									<span className="text-sm font-medium text-green-600">
										+11.23%
									</span>
								</div>
								<div className="flex items-baseline space-x-2">
									<span className="text-2xl font-bold">
										341
									</span>
								</div>
							</div>
							<div className="flex flex-col gap-y-1 pr-4">
								<div className="mb-1 text-sm text-gray-500">
									Patients
								</div>
								<div className="text-2xl font-bold">11</div>
							</div>
						</div>

						{/* Chart */}
						<div className="h-64">
							<ChartContainer
								config={{
									visits: {
										label: "Station Visits",
										color: "hsl(var(--chart-1))",
									},
								}}
								className="h-full w-full"
							>
								<ResponsiveContainer width="100%" height="100%">
									<LineChart
										data={chartData}
										margin={{
											top: 20,
											right: 30,
											left: 20,
											bottom: 20,
										}}
									>
										<CartesianGrid
											className="stroke-muted"
											vertical={false}
										/>
										<XAxis
											dataKey="day"
											axisLine={false}
											tickLine={false}
											className="text-muted-foreground"
										/>
										<YAxis
											axisLine={false}
											tickLine={false}
											className="text-muted-foreground"
											width={10}
											hide
										/>
										<ChartTooltip
											content={<ChartTooltipContent />}
										/>
										<Line
											type="monotone"
											dataKey="value"
											stroke="var(--color-primary)"
											strokeWidth={2}
											dot={false}
											activeDot={{
												r: 4,
												stroke: "var(--color-primary)",
												strokeWidth: 2,
											}}
										/>
									</LineChart>
								</ResponsiveContainer>
							</ChartContainer>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};
