import { useMutation, useQuery } from "@tanstack/react-query";
import * as HttpQuery from "../../http";
import * as Types from "../../types";

/**
 * List planner category rules
 * @param params - Query parameters
 * @returns Planner category rules
 */

export function useListPlannerCategoryRules(
    params: Types.PlannerTypes.PlannerCategoriesListQueryParams
) {
    return useQuery({
        queryKey: ["planner-category-rules"],
        queryFn: () => HttpQuery.APIVersion3ListPlannerCategoryRules(params)
    })
}

/**
 * Get planner category rules
 * @param id - Planner ID
 * @returns Planner category rules
 */

export function useGetPlannerCategoryRules(id: string) {
    return useQuery({
        queryKey: ["category-rules"],
        queryFn: () => HttpQuery.APIVersion3GetPlannerCategoryRules(id)
    })
}

/**
 * Update planner category rules
 * @param id - Planner ID
 * @param data - Planner category rules data
 * @returns Planner category rules updated response
 */

export function useUpdateCategoryRules() {
    return useMutation({
        mutationFn: ({
            id,
            data
        }: {
            id: string;
            data: Types.PlannerTypes.PlannerCategoryRulesUpdateParams;
        }) => HttpQuery.APIVersion3UpdatePlannerCategoryRules(id, data)
    })
}

/**
 * Delete planner category rules
 * @param id - Planner ID
 * @returns Planner category rules deleted response
 */

export function useDeleteCategoryRules() {
    return useMutation({
        mutationFn: ({
            id
        }: {
            id: string;
        }) => HttpQuery.APIVersion1DeletePlannerCategoryRules(id)
    })
}

/**
 * Create planner category rules
 * @param data - Planner category rules data
 * @returns Planner category rules created response
 */

export function useCreatePlannerCategoryRules() {
    return useMutation({
        mutationFn: ({
            data
        }: {
            data: Types.PlannerTypes.PlannerCategoryRulesCreateParams;
        }) => HttpQuery.APIVersion3CreatePlannerCategoryRules(data)
    })
}