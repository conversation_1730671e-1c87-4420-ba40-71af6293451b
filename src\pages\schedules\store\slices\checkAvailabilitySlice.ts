import { useQuery } from "@tanstack/react-query";
import * as HttpQuery from "../../http";
import * as CheckAvailabilityTypes from "../../types/CheckAvailability";

export function useCheckAvailability(
    params: CheckAvailabilityTypes.CheckAvailabilityQueryParams
) {
    return useQuery({
        queryKey: ['check-availability', params],
        queryFn: () => HttpQuery.APIVersion3CheckAvailability(params),
    })  
}
