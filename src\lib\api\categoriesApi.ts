import { apiClient } from "./clients";

const CATEGORIES_ENDPOINTS = {
	base: "/api/v1/client-categories",
	byId: (id: string | number) => `/api/v1/client-categories/${id}`,
	clients: (id: string | number) => `/api/v1/client-categories/${id}/clients`,
} as const;

export interface CategoryCondition {
	id: number;
	attribute: {
		id: number;
		name: string;
		type: string;
	};
	operator: {
		key: string;
		label: string;
	};
	value: any;
}

export interface CategoryData {
	id: number;
	name: string;
	color: string;
	is_conditional: boolean;
	description: string;
	type: string;
	client_count: number;
	station_count: number;
	is_active: boolean;
	business_id: number;
	conditions?: CategoryCondition[];
	created_at?: string;
	updated_at?: string;
}

export interface CategoriesFilters {
	page?: number;
	limit?: number;
	search?: string;
	status?: string;
	organization_id?: number;
}

export interface CategoriesPagination {
	total: number;
	count: number;
	per_page: number;
	current_page: number;
	total_pages: number;
}

export interface CategoriesMeta {
	pagination: CategoriesPagination;
}

export interface CategoriesResponse {
	success: boolean;
	message: string;
	data: CategoryData[];
	meta: CategoriesMeta;
}

export interface CategoryDetailResponse {
	success: boolean;
	message: string;
	data: CategoryData;
}

export interface LocationSelection {
	location_id: number;
	all_stations: boolean;
	station_ids: number[];
}

export interface CreateCategoryRequest {
	name: string;
	description: string;
	color: string;
	type: "manual" | "conditional";
	apply_to_all_locations?: boolean;
	location_ids?: number[];
	station_ids?: number[];
	client_ids?: number[];
	location_selections?: LocationSelection[];
	conditions?: Omit<CategoryCondition, "id">[];
	condition_type?: string;
	operator?: string;
	conditional_value?: string;
	date_range_start?: string;
	date_range_end?: string;
	information_type?: string;
	form_question_id?: string;
	custom_intake_id?: number;
	priority?: string;
}

export interface CreateCategoryResponse {
	success: boolean;
	message: string;
	data: CategoryData;
}

export interface UpdateCategoryRequest {
	id: number;
	name?: string;
	description?: string;
	color?: string;
	type?: "manual" | "conditional";
	is_active?: boolean;
	apply_to_all_locations?: boolean;
	location_ids?: number[];
	station_ids?: number[];
	client_ids?: number[];
	location_selections?: LocationSelection[];
	conditions?: Omit<CategoryCondition, "id">[];
	condition_type?: string;
	operator?: string;
	conditional_value?: string;
	date_range_start?: string;
	date_range_end?: string;
	information_type?: string;
	form_question_id?: string;
	custom_intake_id?: number;
	priority?: string;
}

export interface UpdateCategoryResponse {
	success: boolean;
	message: string;
	data: CategoryData;
}

export interface CategoryDetailData {
	id: number;
	name: string;
	type: string;
	description: string;
	color: string;
	business_id: number;
	client_count: number;
	location_count: number;
	station_count: number;
	conditions: Array<{
		id: number;
		field: string;
		parameter: string;
		value: string;
		attribute_id: number;
		operator: string;
	}>;
	locations: Array<{
		id: number;
		name: string;
		address: string;
	}>;
	stations: Array<{
		id: number;
		name: string;
		description: string;
	}>;
	location_selections: Array<{
		location_id: number;
		location_name: string;
		station_ids: number[];
		stations: Array<{
			id: number;
			name: string;
		}>;
	}>;
	applied_to: Array<{
		location: string;
		stations: string[];
	}>;
}

export interface CategoryClient {
	id: number;
	external_id?: string;
	first_name: string;
	last_name: string;
	full_name: string;
	email: string;
	phone_number: string;
	profile_picture_url?: string;
	is_active: boolean;
	last_visit?: string;
	emr_sync_status?: string;
}

export interface CategoryClientsFilters {
	page?: number;
	limit?: number;
	organization_id?: number;
}

export interface CategoryClientsMeta {
	total?: number;
	page?: number;
	per_page?: number;
	total_pages?: number;
}

export interface CategoryClientsResponse {
	success: boolean;
	data: CategoryClient[];
	meta: CategoryClientsMeta;
	message: string;
}

export interface AddClientsToCategory {
	client_ids: number[];
}

export interface AddClientsToCategoryResponse {
	success: boolean;
	message: string;
	data?: any;
}

export interface CategoryDetailExtendedResponse {
	success: boolean;
	data: CategoryDetailData;
	message: string;
}

export const categoriesApi = {
	getCategories: async (
		filters: CategoriesFilters = {}
	): Promise<CategoriesResponse> => {
		const params = new URLSearchParams();
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.limit !== undefined) {
			params.append("limit", filters.limit.toString());
		}
		if (filters.search) {
			params.append("search", filters.search);
		}
		if (filters.status) {
			params.append("status", filters.status);
		}

		const queryString = params.toString();
		const url = queryString
			? `${CATEGORIES_ENDPOINTS.base}?${queryString}`
			: CATEGORIES_ENDPOINTS.base;

		const headers: Record<string, any> = {};
		if (filters.organization_id) {
			headers["X-organizationId"] = filters.organization_id;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},

	getCategoryById: async (
		id: string | number,
		organizationId: number
	): Promise<CategoryDetailExtendedResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId,
		};

		const response = await apiClient.get(CATEGORIES_ENDPOINTS.byId(id), {
			headers,
		});
		return response.data;
	},

	createCategory: async (
		data: CreateCategoryRequest,
		organizationId: number
	): Promise<CreateCategoryResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId,
		};

		const response = await apiClient.post(CATEGORIES_ENDPOINTS.base, data, {
			headers,
		});
		return response.data;
	},

	updateCategory: async (
		data: UpdateCategoryRequest,
		organizationId: number
	): Promise<UpdateCategoryResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId,
		};

		const { id, ...updateData } = data;
		const response = await apiClient.put(
			CATEGORIES_ENDPOINTS.byId(id),
			updateData,
			{
				headers,
			}
		);
		return response.data;
	},

	deleteCategory: async (
		id: string | number,
		organizationId: number
	): Promise<void> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId,
		};

		await apiClient.delete(CATEGORIES_ENDPOINTS.byId(id), {
			headers,
		});
	},

	getCategoryClients: async (
		id: string | number,
		filters: CategoryClientsFilters = {}
	): Promise<CategoryClientsResponse> => {
		const params = new URLSearchParams();
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.limit !== undefined) {
			params.append("limit", filters.limit.toString());
		}

		const queryString = params.toString();
		const url = queryString
			? `${CATEGORIES_ENDPOINTS.clients(id)}?${queryString}`
			: CATEGORIES_ENDPOINTS.clients(id);

		const headers: Record<string, any> = {};
		if (filters.organization_id) {
			headers["X-organizationId"] = filters.organization_id;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},

	addClientsToCategory: async (
		categoryId: string | number,
		clientIds: number[],
		organizationId: number
	): Promise<AddClientsToCategoryResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId,
		};

		const data: AddClientsToCategory = {
			client_ids: clientIds,
		};

		const response = await apiClient.post(
			CATEGORIES_ENDPOINTS.clients(categoryId),
			data,
			{ headers }
		);
		return response.data;
	},

	detachClientsFromCategory: async (
		categoryId: string | number,
		clientIds: number[],
		organizationId: number
	): Promise<void> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId,
		};

		const data: AddClientsToCategory = {
			client_ids: clientIds,
		};

		await apiClient.delete(CATEGORIES_ENDPOINTS.clients(categoryId), {
			headers,
			data,
		});
	},
};
