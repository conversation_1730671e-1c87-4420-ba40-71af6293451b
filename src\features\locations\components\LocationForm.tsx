import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { useCreateLocation, useUpdateLocation } from "../hooks";
import type {
	Location,
	CreateLocationRequest,
	UpdateLocationRequest,
} from "../types";

const locationSchema = z.object({
	name: z.string().min(1, "Location name is required"),
	street: z.string().min(1, "Street address is required"),
	city: z.string().min(1, "City is required"),
	state: z.string().min(1, "State is required"),
	zipCode: z.string().min(1, "ZIP code is required"),
	country: z.string().min(1, "Country is required"),
	phone: z.string().optional(),
	email: z
		.string()
		.email("Invalid email address")
		.optional()
		.or(z.literal("")),
	description: z.string().optional(),
	timezone: z.string().min(1, "Timezone is required"),
	capacity: z.number().min(1, "Capacity must be at least 1").optional(),
});

type LocationFormData = z.infer<typeof locationSchema>;

export interface LocationFormProps {
	location?: Location;
	onClose: () => void;
	onSuccess: (location: Location) => void;
}

export const LocationForm: React.FC<LocationFormProps> = ({
	location,
	onClose,
	onSuccess,
}) => {
	const isEdit = !!location;
	const createLocation = useCreateLocation();
	const updateLocation = useUpdateLocation();

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
	} = useForm<LocationFormData>({
		resolver: zodResolver(locationSchema),
		defaultValues: {
			name: location?.name || "",
			street: location?.address.street || "",
			city: location?.address.city || "",
			state: location?.address.state || "",
			zipCode: location?.address.zipCode || "",
			country: location?.address.country || "United States",
			phone: location?.phone || "",
			email: location?.email || "",
			description: location?.description || "",
			timezone: location?.timezone || "America/New_York",
			capacity: location?.capacity || undefined,
		},
	});

	const onSubmit = async (data: LocationFormData) => {
		try {
			const requestData = {
				name: data.name,
				address: {
					street: data.street,
					city: data.city,
					state: data.state,
					zipCode: data.zipCode,
					country: data.country,
				},
				phone: data.phone || undefined,
				email: data.email || undefined,
				description: data.description || undefined,
				timezone: data.timezone,
				capacity: data.capacity || undefined,
			};

			if (isEdit && location) {
				const result = await updateLocation.mutateAsync({
					id: location.id,
					...requestData,
				} as UpdateLocationRequest);
				onSuccess(result);
			} else {
				const result = await createLocation.mutateAsync(
					requestData as CreateLocationRequest
				);
				onSuccess(result);
			}
		} catch (error) {
			// Error handling is done in the mutation hooks
			console.error("Form submission error:", error);
		}
	};

	return (
		<Dialog open onOpenChange={onClose}>
			<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
				<DialogHeader>
					<DialogTitle>
						{isEdit ? "Edit Location" : "Create New Location"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
					{/* Basic Information */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">
							Basic Information
						</h3>

						<div>
							<Label htmlFor="name">Location Name *</Label>
							<Input
								id="name"
								{...register("name")}
								placeholder="Enter location name"
							/>
							{errors.name && (
								<p className="mt-1 text-sm text-red-600">
									{errors.name.message}
								</p>
							)}
						</div>

						<div>
							<Label htmlFor="description">Description</Label>
							<Textarea
								id="description"
								{...register("description")}
								placeholder="Brief description of the location"
								rows={3}
							/>
						</div>

						<div>
							<Label htmlFor="capacity">Capacity</Label>
							<Input
								id="capacity"
								type="number"
								{...register("capacity", {
									valueAsNumber: true,
								})}
								placeholder="Maximum capacity"
							/>
							{errors.capacity && (
								<p className="mt-1 text-sm text-red-600">
									{errors.capacity.message}
								</p>
							)}
						</div>
					</div>

					{/* Address Information */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">Address</h3>

						<div>
							<Label htmlFor="street">Street Address *</Label>
							<Input
								id="street"
								{...register("street")}
								placeholder="Enter street address"
							/>
							{errors.street && (
								<p className="mt-1 text-sm text-red-600">
									{errors.street.message}
								</p>
							)}
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div>
								<Label htmlFor="city">City *</Label>
								<Input
									id="city"
									{...register("city")}
									placeholder="Enter city"
								/>
								{errors.city && (
									<p className="mt-1 text-sm text-red-600">
										{errors.city.message}
									</p>
								)}
							</div>

							<div>
								<Label htmlFor="state">State *</Label>
								<Input
									id="state"
									{...register("state")}
									placeholder="Enter state"
								/>
								{errors.state && (
									<p className="mt-1 text-sm text-red-600">
										{errors.state.message}
									</p>
								)}
							</div>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div>
								<Label htmlFor="zipCode">ZIP Code *</Label>
								<Input
									id="zipCode"
									{...register("zipCode")}
									placeholder="Enter ZIP code"
								/>
								{errors.zipCode && (
									<p className="mt-1 text-sm text-red-600">
										{errors.zipCode.message}
									</p>
								)}
							</div>

							<div>
								<Label htmlFor="country">Country *</Label>
								<Input
									id="country"
									{...register("country")}
									placeholder="Enter country"
								/>
								{errors.country && (
									<p className="mt-1 text-sm text-red-600">
										{errors.country.message}
									</p>
								)}
							</div>
						</div>
					</div>

					{/* Contact Information */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">
							Contact Information
						</h3>

						<div className="grid grid-cols-2 gap-4">
							<div>
								<Label htmlFor="phone">Phone Number</Label>
								<Input
									id="phone"
									{...register("phone")}
									placeholder="Enter phone number"
								/>
							</div>

							<div>
								<Label htmlFor="email">Email Address</Label>
								<Input
									id="email"
									type="email"
									{...register("email")}
									placeholder="Enter email address"
								/>
								{errors.email && (
									<p className="mt-1 text-sm text-red-600">
										{errors.email.message}
									</p>
								)}
							</div>
						</div>

						<div>
							<Label htmlFor="timezone">Timezone *</Label>
							<Input
								id="timezone"
								{...register("timezone")}
								placeholder="e.g., America/New_York"
							/>
							{errors.timezone && (
								<p className="mt-1 text-sm text-red-600">
									{errors.timezone.message}
								</p>
							)}
						</div>
					</div>

					{/* Actions */}
					<div className="flex items-center justify-end gap-3 border-t pt-6">
						<Button
							type="button"
							variant="outline"
							onClick={onClose}
						>
							Cancel
						</Button>
						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting
								? "Saving..."
								: isEdit
									? "Update Location"
									: "Create Location"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
