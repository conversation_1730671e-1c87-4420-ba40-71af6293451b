import * as React from "react";
import { cn } from "@/lib/utils";
import { Checkbox } from "../common/Checkbox";
import {
	BellRing,
	Calendar,
	CalendarClock,
	Clock,
	Info,
	MessageSquareText,
	QrCode,
	Tag,
	Trash2,
	UserRound,
} from "lucide-react";

export interface PatientCardProps extends React.HTMLAttributes<HTMLDivElement> {
	patientName: string;
	doctorName: string;
	appointmentType: string;
	date: string;
	time: string;
	variant?: "default" | "selected" | "disabled";
	showActions?: boolean;
	onEdit?: () => void;
	onDelete?: () => void;
	onShare?: () => void;
	onMore?: () => void;
}

const PatientCard = React.forwardRef<HTMLDivElement, PatientCardProps>(
	(
		{
			className,
			patientName,
			doctorName,
			appointmentType,
			date,
			time,
			variant = "default",
			showActions = true,
			onEdit,
			onDelete,
			onShare,
			onMore,
			...props
		},
		ref
	) => {
		return (
			<div
				ref={ref}
				className={cn(
					"inline-flex w-[270px] flex-col items-start justify-start gap-1.5 rounded-lg border border-gray-200 bg-[#FAFAFA] p-2 shadow-sm",
					variant === "selected" && "border-[#005893] shadow-md",
					variant === "disabled" && "bg-white",
					className
				)}
				{...props}
			>
				<div className="flex flex-col items-start justify-start gap-1 self-stretch">
					<div className="inline-flex items-center justify-between self-stretch">
						<div className="inline-flex h-5 items-center justify-start gap-1.5">
							<Checkbox className="h-3 w-3" />
							<div className="justify-start text-xs leading-none font-medium text-[#27272A]">
								{patientName}
							</div>
						</div>
						<div className="flex items-center justify-start gap-2">
							<Tag className="h-3 w-3 text-[#71717A]" />
							<Info className="h-3 w-3 text-[#27272A]" />
						</div>
					</div>
					<div className="inline-flex items-center justify-start gap-1.5 text-[#52525B]">
						<QrCode className="h-3 w-3" />
						<div className="justify-center text-[8px] leading-3 font-normal text-gray-500">
							{doctorName}
						</div>
					</div>
					<div className="inline-flex items-center justify-start gap-1.5 text-[#042C4D]">
						<UserRound className="h-3 w-3" />
						<div className="justify-center text-[8px] leading-3 font-normal">
							{appointmentType}
						</div>
					</div>
					<div className="inline-flex items-center justify-start gap-3 self-stretch text-[#27272A]">
						<div className="flex items-center justify-start gap-1.5">
							<Calendar className="h-3 w-3" />
							<div className="justify-center text-[8px] leading-3 font-normal">
								{date}
							</div>
						</div>
						<div className="flex items-center justify-start gap-1.5">
							<Clock className="h-3 w-3" />

							<div className="justify-center text-[8px] leading-3 font-normal">
								{time}
							</div>
						</div>
					</div>
				</div>
				<div className="h-0 self-stretch border-t border-[#E4E4E7]" />
				{showActions && (
					<div className="inline-flex items-center justify-start gap-2 self-stretch">
						<div className="flex flex-1 items-center justify-start gap-1.5">
							<button
								onClick={onEdit}
								className="flex h-6 w-6 items-center justify-center rounded-md px-1 py-1 opacity-50"
								aria-label="Edit"
							>
								<Trash2 className="h-3 w-3" />
							</button>
							<button
								onClick={onDelete}
								className="flex h-6 w-6 items-center justify-center rounded-md px-1 py-1 opacity-50"
								aria-label="Delete"
							>
								<MessageSquareText className="h-3 w-3" />
							</button>
							<button
								onClick={onShare}
								className="flex h-6 w-6 items-center justify-center rounded-md px-1 py-1 opacity-50"
								aria-label="Share"
							>
								<CalendarClock className="h-3 w-3" />
							</button>
						</div>
						<button
							className={cn(
								"flex h-6 w-6 items-center justify-center gap-2 rounded-md px-1 py-1 transition-colors",
								variant === "selected"
									? "bg-[#005893] text-white"
									: "border border-[#E4E4E7] text-[#005893]"
							)}
							aria-label="Primary action"
						>
							<BellRing className="h-3 w-3" />
						</button>
					</div>
				)}
			</div>
		);
	}
);

PatientCard.displayName = "PatientCard";

export { PatientCard };
