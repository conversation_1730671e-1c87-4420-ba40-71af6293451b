import * as React from "react";
import * as SelectPrimitive from "@radix-ui/react-select";
import { CheckIcon, ChevronDownIcon, ChevronUpIcon, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface MultiSelectContextType {
	isMultiSelect: boolean;
	selectedValues: string[];
	onValueChange: (value: string) => void;
	onRemoveValue: (value: string) => void;
	placeholder?: string;
}

const MultiSelectContext = React.createContext<MultiSelectContextType | null>(
	null
);

interface SelectProps
	extends Omit<
		React.ComponentProps<typeof SelectPrimitive.Root>,
		"value" | "onValueChange"
	> {
	multiSelect?: boolean;
	value?: string | string[];
	onValueChange?: (value: string | string[]) => void;
	placeholder?: string;
	maxSelected?: number;
}

function Select({
	multiSelect = false,
	value,
	onValueChange,
	placeholder,
	maxSelected,
	children,
	...props
}: SelectProps) {
	const [open, setOpen] = React.useState(false);

	const selectedValues = React.useMemo(() => {
		if (multiSelect) {
			return Array.isArray(value) ? value : [];
		}
		return [];
	}, [value, multiSelect]);

	const handleValueChange = React.useCallback(
		(newValue: string) => {
			if (!onValueChange) return;

			if (multiSelect) {
				const currentValues = Array.isArray(value) ? value : [];
				const isSelected = currentValues.includes(newValue);

				let updatedValues: string[];
				if (isSelected) {
					updatedValues = currentValues.filter((v) => v !== newValue);
				} else {
					if (maxSelected && currentValues.length >= maxSelected) {
						return; // so it doesn't add if max limit reached
					}
					updatedValues = [...currentValues, newValue];
				}

				onValueChange(updatedValues);
			} else {
				onValueChange(newValue);
				setOpen(false);
			}
		},
		[multiSelect, value, onValueChange, maxSelected]
	);

	const handleRemoveValue = React.useCallback(
		(valueToRemove: string) => {
			if (!onValueChange || !multiSelect) return;

			const currentValues = Array.isArray(value) ? value : [];
			const updatedValues = currentValues.filter(
				(v) => v !== valueToRemove
			);
			onValueChange(updatedValues);
		},
		[multiSelect, value, onValueChange]
	);

	const contextValue: MultiSelectContextType = {
		isMultiSelect: multiSelect,
		selectedValues,
		onValueChange: handleValueChange,
		onRemoveValue: handleRemoveValue,
		placeholder,
	};

	if (multiSelect) {
		return (
			<MultiSelectContext.Provider value={contextValue}>
				<SelectPrimitive.Root
					data-slot="select"
					open={open}
					onOpenChange={setOpen}
					{...props}
				>
					{children}
				</SelectPrimitive.Root>
			</MultiSelectContext.Provider>
		);
	}

	return (
		<SelectPrimitive.Root
			data-slot="select"
			value={typeof value === "string" ? value : undefined}
			onValueChange={onValueChange as (value: string) => void}
			{...props}
		>
			{children}
		</SelectPrimitive.Root>
	);
}

function SelectGroup({
	...props
}: React.ComponentProps<typeof SelectPrimitive.Group>) {
	return <SelectPrimitive.Group data-slot="select-group" {...props} />;
}

interface SelectValueProps
	extends React.ComponentProps<typeof SelectPrimitive.Value> {
	showSelectedCount?: boolean;
	maxDisplayed?: number;
}

function SelectValue({
	showSelectedCount = true,
	maxDisplayed = 3,
	placeholder,
	...props
}: SelectValueProps) {
	const context = React.useContext(MultiSelectContext);

	if (context?.isMultiSelect) {
		const { selectedValues, placeholder: contextPlaceholder } = context;
		const displayPlaceholder =
			placeholder || contextPlaceholder || "Select options...";

		if (selectedValues.length === 0) {
			return (
				<span className="text-muted-foreground">
					{displayPlaceholder}
				</span>
			);
		}

		if (showSelectedCount && selectedValues.length > maxDisplayed) {
			return <span>{selectedValues.length} selected</span>;
		}

		return (
			<div className="flex flex-wrap gap-1">
				{selectedValues.slice(0, maxDisplayed).map((value) => (
					<Badge
						key={value}
						variant="secondary"
						className="h-5 px-2 py-0.5 text-xs"
					>
						{value}
						<button
							type="button"
							className="hover:bg-secondary-foreground/20 ml-1 rounded-full p-0.5"
							onClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								context.onRemoveValue(value);
							}}
						>
							<X className="h-2.5 w-2.5" />
						</button>
					</Badge>
				))}
				{selectedValues.length > maxDisplayed && (
					<span className="text-muted-foreground text-sm">
						+{selectedValues.length - maxDisplayed} more
					</span>
				)}
			</div>
		);
	}

	return (
		<SelectPrimitive.Value
			data-slot="select-value"
			placeholder={placeholder}
			{...props}
		/>
	);
}

function SelectTrigger({
	className,
	size = "default",
	children,
	...props
}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {
	size?: "sm" | "default";
}) {
	return (
		<SelectPrimitive.Trigger
			data-slot="select-trigger"
			data-size={size}
			className={cn(
				"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
				className
			)}
			{...props}
		>
			{children}
			<SelectPrimitive.Icon asChild>
				<ChevronDownIcon className="size-4 opacity-50" />
			</SelectPrimitive.Icon>
		</SelectPrimitive.Trigger>
	);
}

function SelectContent({
	className,
	children,
	position = "popper",
	...props
}: React.ComponentProps<typeof SelectPrimitive.Content>) {
	return (
		<SelectPrimitive.Portal>
			<SelectPrimitive.Content
				data-slot="select-content"
				className={cn(
					"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-[1104] max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",
					position === "popper" &&
						"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
					className
				)}
				position={position}
				{...props}
			>
				<SelectScrollUpButton />
				<SelectPrimitive.Viewport
					className={cn(
						"p-1",
						position === "popper" &&
							"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"
					)}
				>
					{children}
				</SelectPrimitive.Viewport>
				<SelectScrollDownButton />
			</SelectPrimitive.Content>
		</SelectPrimitive.Portal>
	);
}

function SelectLabel({
	className,
	...props
}: React.ComponentProps<typeof SelectPrimitive.Label>) {
	return (
		<SelectPrimitive.Label
			data-slot="select-label"
			className={cn(
				"text-muted-foreground px-2 py-1.5 text-xs",
				className
			)}
			{...props}
		/>
	);
}

interface SelectItemProps
	extends React.ComponentProps<typeof SelectPrimitive.Item> {
	value: string;
}

function SelectItem({ className, children, value, ...props }: SelectItemProps) {
	const context = React.useContext(MultiSelectContext);

	if (context?.isMultiSelect) {
		const isSelected = context.selectedValues.includes(value);

		return (
			<div
				data-slot="select-item"
				className={cn(
					"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground hover:bg-accent hover:text-accent-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
					className
				)}
				onClick={() => context.onValueChange(value)}
				{...props}
			>
				<span className="absolute right-2 flex size-3.5 items-center justify-center">
					{isSelected && <CheckIcon className="size-4" />}
				</span>
				<span>{children}</span>
			</div>
		);
	}

	return (
		<SelectPrimitive.Item
			data-slot="select-item"
			className={cn(
				"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",
				className
			)}
			value={value}
			{...props}
		>
			<span className="absolute right-2 flex size-3.5 items-center justify-center">
				<SelectPrimitive.ItemIndicator>
					<CheckIcon className="size-4" />
				</SelectPrimitive.ItemIndicator>
			</span>
			<SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
		</SelectPrimitive.Item>
	);
}

function SelectSeparator({
	className,
	...props
}: React.ComponentProps<typeof SelectPrimitive.Separator>) {
	return (
		<SelectPrimitive.Separator
			data-slot="select-separator"
			className={cn(
				"bg-border pointer-events-none -mx-1 my-1 h-px",
				className
			)}
			{...props}
		/>
	);
}

function SelectScrollUpButton({
	className,
	...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {
	return (
		<SelectPrimitive.ScrollUpButton
			data-slot="select-scroll-up-button"
			className={cn(
				"flex cursor-default items-center justify-center py-1",
				className
			)}
			{...props}
		>
			<ChevronUpIcon className="size-4" />
		</SelectPrimitive.ScrollUpButton>
	);
}

function SelectScrollDownButton({
	className,
	...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {
	return (
		<SelectPrimitive.ScrollDownButton
			data-slot="select-scroll-down-button"
			className={cn(
				"flex cursor-default items-center justify-center py-1",
				className
			)}
			{...props}
		>
			<ChevronDownIcon className="size-4" />
		</SelectPrimitive.ScrollDownButton>
	);
}

export {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectLabel,
	SelectScrollDownButton,
	SelectScrollUpButton,
	SelectSeparator,
	SelectTrigger,
	SelectValue,
};

export type { SelectProps, SelectValueProps, SelectItemProps };
