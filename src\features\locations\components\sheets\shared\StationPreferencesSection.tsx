import { Info } from "lucide-react";
import { Switch } from "@/components/ui/switch";

interface StationPreferencesSectionProps {
	autoApprove: boolean;
	serviceVisibility: boolean;
	serviceAvailability: boolean;
	onPreferenceChange: (field: string, value: boolean) => void;
}

export function StationPreferencesSection({
	autoApprove,
	serviceVisibility,
	serviceAvailability,
	onPreferenceChange,
}: StationPreferencesSectionProps) {
	return (
		<div className="space-y-2">
			<h3 className="text-sm font-medium text-[#323539]">
				Select Station Preferences
			</h3>
			<div className="space-y-0">
				{/* Auto Approve */}
				<div className="flex items-center justify-between border-b-[0.5px] border-[#bfbfbf] px-6 py-3">
					<div className="flex items-center gap-2">
						<span className="text-sm text-zinc-900">
							Auto Approve
						</span>
						<Info className="h-3 w-3 text-zinc-400" />
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={autoApprove}
							onCheckedChange={(checked) =>
								onPreferenceChange("autoApprove", checked)
							}
						/>
						<span className="text-xs text-zinc-500">
							{autoApprove ? "On" : "Off"}
						</span>
					</div>
				</div>

				{/* Service Visibility */}
				<div className="flex items-center justify-between border-b border-[#bfbfbf] px-6 py-3">
					<div className="flex items-center gap-2">
						<span className="text-sm text-zinc-900">
							Station Visibility
						</span>
						<Info className="h-3 w-3 text-zinc-400" />
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={serviceVisibility}
							onCheckedChange={(checked) =>
								onPreferenceChange("serviceVisibility", checked)
							}
						/>
						<span className="text-xs text-zinc-500">
							{serviceVisibility ? "On" : "Off"}
						</span>
					</div>
				</div>

				{/* Service Availability */}
				<div className="flex items-center justify-between px-6 py-3">
					<div className="flex items-center gap-2">
						<span className="text-sm text-zinc-900">
							Station Availability
						</span>
						<Info className="h-3 w-3 text-zinc-400" />
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={serviceAvailability}
							onCheckedChange={(checked) =>
								onPreferenceChange(
									"serviceAvailability",
									checked
								)
							}
						/>
						<span className="text-xs text-zinc-500">
							{serviceAvailability ? "On" : "Off"}
						</span>
					</div>
				</div>
			</div>
		</div>
	);
}
