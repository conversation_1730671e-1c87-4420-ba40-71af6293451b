export type AvailabilityCountQueryParams = {
    date: string;
    location_id: string;
    service_id: string;
    station_id: string;
}

export type AvailabilityCountResponse = {
    success: boolean;
    message: string;
    data: {
        available_slots: number;
        total_slots: number;
        has_availability: boolean;
        availability_percentage: number;
        next_available: string;
        last_available: string;
    }
}

export type TimeRangesQueryParams = {
    date: string;
    location_id: string;
    service_id: string;
    station_id: string;
}

export type TimeRangesResponse = {
    success: boolean;
    message: string;
    data: {
        ranges: {
            start_time: string;
            end_time: string;
            duration_minutes: number;
            slots_available: number;
        }[],
        gaps: {
            start_time: string;
            end_time: string;
            duration_minutes: number;
            reason: string;
        }[],
    }
}

export type TimeSlotsQueryParams = {
    available: boolean;
    client_id: string;
    date: string;
    duration: string;
    exclude: string;
    include: string;
    service_id: string;
    station_id: string;
    location_id: string;
}

export type TimeSlotsResponseData = TimeSlotsResponse[]

export type TimeSlotsResponse =
    {
        success: boolean;
        message: string;
        data: {
            slots:
            {
                start_time: string;
                end_time: string;
                available: boolean;
                duration_minutes: number;
                slot_id: string;
                booking_url: string;
                instant_book: boolean;
            }[],
        },
        meta: {
            date: string;
            location: {
                id: number;
                name: string;
            },
            station: {
                id: number;
                name: string;
            },
            summary: {
                total_slots: number;
                available_slots: number;
                availability_percentage: number;
            },
            request_info: {
                url: string;
                parameters: {
                    date: string;
                },
                generated_at: string;
            }
        }
    }