import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import React, { useState } from "react";

const SamlSsoSettings: React.FC = () => {
	const [enabledSAMLSSO, setEnableSAMLSSO] = useState(false);
	return (
		<div>
			<h2 className="mb-4 text-xl font-semibold">
				SAML Single Sign-On (SSO)
			</h2>
			<div className="my-6 md:max-w-2xl">
				<Separator />
				<h3 className="my-2 font-medium">Patient Validation</h3>
				<div className="mb-2 flex items-center justify-between gap-2">
					<span>Enable Patients Validation</span>
					<div>
						<Switch
							checked={enabledSAMLSSO}
							onCheckedChange={setEnableSAMLSSO}
							className="ml-2"
						/>
						<span className="ml-2 font-semibold text-blue-600">
							{enabledSAMLSSO ? "On" : "Off"}
						</span>
					</div>
				</div>
				<p className="mb-2 text-xs text-gray-500">
					This will enable the validation process. Patients who are
					not validated will not be allowed to continue.
				</p>
			</div>
		</div>
	);
};

export default SamlSsoSettings;
