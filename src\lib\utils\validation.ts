import { z } from "zod";
import { VALIDATION } from "./constants";

// Auth Schemas
export const signInSchema = z.object({
	email: z.string().min(1, "Email is required").email("Invalid email format"),
	password: z
		.string()
		.min(1, "Password is required")
		.min(
			VALIDATION.MIN_PASSWORD_LENGTH,
			`Password must be at least ${VALIDATION.MIN_PASSWORD_LENGTH} characters`
		),
});

export const signUpSchema = z
	.object({
		firstName: z
			.string()
			.min(1, "First name is required")
			.min(2, "First name must be at least 2 characters"),
		lastName: z
			.string()
			.min(1, "Last name is required")
			.min(2, "Last name must be at least 2 characters"),
		email: z
			.string()
			.min(1, "Email is required")
			.email("Invalid email format"),
		password: z
			.string()
			.min(1, "Password is required")
			.min(
				VALIDATION.MIN_PASSWORD_LENGTH,
				`Password must be at least ${VALIDATION.MIN_PASSWORD_LENGTH} characters`
			),
		confirmPassword: z.string().min(1, "Password confirmation is required"),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"],
	});

// Customer Schemas
export const customerSchema = z.object({
	firstName: z
		.string()
		.min(1, "First name is required")
		.min(2, "First name must be at least 2 characters"),
	lastName: z
		.string()
		.min(1, "Last name is required")
		.min(2, "Last name must be at least 2 characters"),
	email: z.string().min(1, "Email is required").email("Invalid email format"),
	phone: z
		.string()
		.min(1, "Phone number is required")
		.regex(VALIDATION.PHONE_REGEX, "Invalid phone number format"),
	dateOfBirth: z.string().min(1, "Date of birth is required"),
	address: z.object({
		street: z.string().min(1, "Street address is required"),
		city: z.string().min(1, "City is required"),
		state: z.string().min(1, "State is required"),
		zipCode: z.string().min(1, "ZIP code is required"),
		country: z.string().min(1, "Country is required"),
	}),
});

// Settings Schemas
export const profileSchema = z.object({
	firstName: z
		.string()
		.min(1, "First name is required")
		.min(2, "First name must be at least 2 characters"),
	lastName: z
		.string()
		.min(1, "Last name is required")
		.min(2, "Last name must be at least 2 characters"),
	email: z.string().min(1, "Email is required").email("Invalid email format"),
	bio: z.string().optional(),
});

export const changePasswordSchema = z
	.object({
		currentPassword: z.string().min(1, "Current password is required"),
		newPassword: z
			.string()
			.min(1, "New password is required")
			.min(
				VALIDATION.MIN_PASSWORD_LENGTH,
				`Password must be at least ${VALIDATION.MIN_PASSWORD_LENGTH} characters`
			),
		confirmPassword: z.string().min(1, "Password confirmation is required"),
	})
	.refine((data) => data.newPassword === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"],
	});

export const timeSlotSchema = z.object({
	id: z.string(),
	startTime: z.string().min(1, "Start time is required"),
	endTime: z.string().min(1, "End time is required"),
});

export const dayScheduleSchema = z.object({
	enabled: z.boolean(),
	slots: z.array(timeSlotSchema),
});

export const weeklyScheduleSchema = z.object({
	monday: dayScheduleSchema,
	tuesday: dayScheduleSchema,
	wednesday: dayScheduleSchema,
	thursday: dayScheduleSchema,
	friday: dayScheduleSchema,
	saturday: dayScheduleSchema,
	sunday: dayScheduleSchema,
});

export type TimeSlot = z.infer<typeof timeSlotSchema>;
export type DaySchedule = z.infer<typeof dayScheduleSchema>;
export type WeeklyScheduleData = z.infer<typeof weeklyScheduleSchema>;

// Helper function to convert 12-hour time to minutes for comparison
export function timeToMinutes(timeString: string): number {
	const [time, period] = timeString.split(" ");
	const [hours, minutes] = time.split(":").map(Number);

	let totalMinutes = minutes;
	if (period === "AM") {
		totalMinutes += hours === 12 ? 0 : hours * 60;
	} else {
		totalMinutes += hours === 12 ? 12 * 60 : (hours + 12) * 60;
	}

	return totalMinutes;
}

// Validation function for time slots
export function validateTimeSlots(slots: TimeSlot[]): string[] {
	const errors: string[] = [];

	for (let i = 0; i < slots.length; i++) {
		const slot = slots[i];
		const startMinutes = timeToMinutes(slot.startTime);
		const endMinutes = timeToMinutes(slot.endTime);

		// Check if end time is after start time
		if (endMinutes <= startMinutes) {
			errors.push(`Slot ${i + 1}: End time must be after start time`);
		}

		// Check for overlaps with other slots
		for (let j = i + 1; j < slots.length; j++) {
			const otherSlot = slots[j];
			const otherStartMinutes = timeToMinutes(otherSlot.startTime);
			const otherEndMinutes = timeToMinutes(otherSlot.endTime);

			// Check for overlap
			if (
				(startMinutes < otherEndMinutes &&
					endMinutes > otherStartMinutes) ||
				(otherStartMinutes < endMinutes &&
					otherEndMinutes > startMinutes)
			) {
				errors.push(`Slots ${i + 1} and ${j + 1} overlap`);
			}

			// Check for duplicates
			if (
				slot.startTime === otherSlot.startTime &&
				slot.endTime === otherSlot.endTime
			) {
				errors.push(`Slots ${i + 1} and ${j + 1} are duplicates`);
			}
		}
	}

	return errors;
}

// Type exports
export type SignInFormData = z.infer<typeof signInSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type CustomerFormData = z.infer<typeof customerSchema>;
export type ProfileFormData = z.infer<typeof profileSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
