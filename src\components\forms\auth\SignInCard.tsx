import VerifyEmail from "@/components/VerifyEmail";
import RequestIsLoading from "@/components/RequestIsLoading";
// import SignInWithMicrosoft from "@/components/forms/auth/social/SignInWithMicrosoft";
// import SigninWIthGoogle from "@/components/forms/auth/social/SigninWIthGoogle";
import { Checkbox } from "@/components/common/Checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import { useLoginUser, useGoogleLogin, useMicrosoftLogin } from '@/stores/slices/authSlice';

import useUserStore from "@/stores/useUserStore";
import { UserLoginSchema, type UserLoginType } from "@/types/signup";
import { LANDING_ENVIRONMENT_LINK } from "@/lib/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Key } from "lucide-react";
import React from "react";
import { Form<PERSON><PERSON><PERSON>, type SubmitHandler, useForm } from "react-hook-form";
import { LuEye, LuEyeOff } from "react-icons/lu";
import { Button } from "@/components/ui/button";
import { Link } from "react-router";

interface SignInCardProps {
	onSwitchToSSO?: () => void;
}

const SignInCard: React.FC<SignInCardProps> = ({ onSwitchToSSO }) => {
	const [passwordType, setPasswordType] = React.useState("password");
	const rememberAuth = useUserStore((s: any) => s.rememberAuth);
	const setRememberAuth = useUserStore((s: any) => s.setRememberAuth);
	const setMfaUser = useUserStore((s: any) => s.setMfaUser);
	const setUser = useUserStore((s: any) => s.setUser);
	const [showVerifyEmailModal, setShowVerifyEmailModal] =
		React.useState(false);
	const [showCreateAccountOnSSO, setShowCreateAccountOnSSO] =
		React.useState(false);
	const [SSOToken, setSSOToken] = React.useState<{
		type: "microsoft" | "google";
		token: string | null;
	} | null>(null);

	const formMethods = useForm<UserLoginType>({
		resolver: zodResolver(UserLoginSchema),
	});

	const loginUserMutaion = useLoginUser(formMethods.setError);
	const handleLoginSuccess = useHandleLoginSuccess();
	const googleLoginMutation = useGoogleLogin();
	const microsoftLoginMutation = useMicrosoftLogin();

	const onSubmit: SubmitHandler<UserLoginType> = async (data) => {
		try {
			setMfaUser(null);
			setUser(null);
			loginUserMutaion.mutate(
				{
					email: data.email,
					password: data.password,
				},
				{
					onSuccess: (data) => {
						handleLoginSuccess(data);
						setShowVerifyEmailModal(
							Boolean(
								!("twoFactor" in data.data) &&
									!data.data.is_email_verified
							)
						);
					},
				}
			);
		} catch (error) {
			formMethods.setError("root", {
				message: "An error occured kindly try again later",
			});
		}
	};

	return (
		<div className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]">
			<div className="flex flex-col space-y-2 px-8">
				<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
					Sign in to my account
				</h3>
				<a
					href={LANDING_ENVIRONMENT_LINK + "/sign-up"}
					className="font-normal tracking-[-1%] text-[#858C95]"
				>
					Don&apos;t have an account?{" "}
					<span className="text-[#195388]">Sign up</span>
				</a>
			</div>
			<FormProvider {...formMethods}>
				<form
					onSubmit={formMethods.handleSubmit(onSubmit)}
					className="flex flex-col space-y-6"
				>
					<div className="flex flex-col space-y-6 px-8">
						<div className="space-y-1.5">
							<Label htmlFor="email" className="text-[#323539]">
								Email Address{" "}
								<span className="text-[#c9312c]">*</span>
							</Label>
							<Input
								id="email"
								max={254}
								{...formMethods.register("email")}
								aria-describedby={
									formMethods.formState.errors.email
										? "emailError"
										: undefined
								}
								placeholder="Enter your email address"
							/>
							{formMethods.formState.errors.email?.message && (
								<small
									id="emailError"
									role="alert"
									className="mt-1.5 text-sm text-[#c9312c]"
								>
									{
										formMethods.formState.errors.email
											?.message
									}
								</small>
							)}
						</div>
						<div className="space-y-1.5">
							<Label
								htmlFor="password"
								className="text-[#323539]"
							>
								Password{" "}
								<span className="text-[#c9312c]">*</span>
							</Label>
							<div className="relative">
								<Input
									id="password"
									type={passwordType}
									{...formMethods.register("password")}
									aria-describedby={
										formMethods.formState.errors.password
											? "passwordHelp passwordError"
											: "passwordHelp"
									}
									placeholder="Enter your password"
								/>
								<button
									type="button"
									onClick={() =>
										setPasswordType(
											passwordType === "password"
												? "text"
												: "password"
										)
									}
									className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer text-main-1"
								>
									{passwordType === "password" ? (
										<LuEyeOff size={16} />
									) : (
										<LuEye size={16} />
									)}
								</button>
							</div>

							{formMethods.formState.errors.password?.message && (
								<small
									id="passwordError"
									role="alert"
									className="text-sm text-[#c9312c]"
								>
									{
										formMethods.formState.errors.password
											?.message
									}
								</small>
							)}
						</div>
					</div>
					{formMethods.formState.errors.root?.message && (
						<p className="px-8 text-sm tracking-[-0.1px] text-red-500">
							{formMethods.formState.errors.root?.message}
						</p>
					)}
					<div className="flex items-center justify-between space-x-3 px-8">
						<div
							className="flex items-center space-x-1.5"
							onClick={() =>
								setRememberAuth({
									...rememberAuth,
									rememberMe: !rememberAuth?.rememberMe,
								})
							}
						>
							<Checkbox
								onCheckedChange={() =>
									setRememberAuth({
										...rememberAuth,
										rememberMe:
											!rememberAuth?.rememberMe,
									})
								}
								checked={rememberAuth?.rememberMe}
								id="remember-me"
								className="h-4 w-4 rounded-sm border-[#323539]"
							/>
							<Label>Remember me for the next 30 days</Label>
						</div>
						<Link
							to={"/forgot-password"}
							className="font-normal tracking-[-1%] underline"
						>
							Forgot Password?
						</Link>
					</div>

					<div className="flex flex-col items-stretch space-y-4 bg-[#FAFBFC] px-8 pb-4 pt-[18px]">
						<Button
							disabled={loginUserMutaion.isPending}
							loading={loginUserMutaion.isPending}
							// loaderSize={20}
							className="h-10 w-full text-white"
							type="submit"
						>
							Sign in
						</Button>
						<p className="text-center text-sm text-[#858C95]">
							Or Sign in With
						</p>
						<div className="flex sm:space-x-2 msm:flex-col msm:space-y-2">
							{/* <SigninWIthGoogle
								type="sign-in"
								setSSOToken={setSSOToken}
								setShowCreateAccountOnSSO={
									setShowCreateAccountOnSSO
								}
							/>
							<SignInWithMicrosoft
								type="sign-in"
								setSSOToken={setSSOToken}
								setShowCreateAccountOnSSO={
									setShowCreateAccountOnSSO
								}
							/> */}
							<button
								type="button"
								onClick={onSwitchToSSO}
								className="flex h-10 w-full items-center justify-center gap-x-2 rounded-md border border-[#E9EAEB] bg-white px-3 text-sm font-medium text-[#323539] hover:bg-gray-50 md:w-fit"
							>
								<Key
									size={14}
									className="rotate-[-270] scale-x-[-1]"
								/>
								SSO
							</button>
						</div>
					</div>
				</form>
			</FormProvider>
			<RequestIsLoading
				isWhite
				size={20}
				isLoading={googleLoginMutation.isPending}
			/>
			<VerifyEmail
				email={formMethods.watch("email")}
				showVerifyEmailModal={showVerifyEmailModal}
				setShowVerifyEmailModal={setShowVerifyEmailModal}
			/>
		</div>
	);
};

export default SignInCard;
