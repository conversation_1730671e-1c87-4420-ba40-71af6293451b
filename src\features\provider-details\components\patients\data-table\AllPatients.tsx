import { type FC } from "react";
import { Checkbox } from "@/components/common/Checkbox";
import type { Location } from "@/features/locations/types";
import { Button } from "@/components/ui/button";
import { MapPin } from "lucide-react";
import { Plus } from "lucide-react";
import { PatientsCard } from "../PatientsCard";
import type { PatientResponse } from "@/features/provider-details/types";

interface AllPatientsProps {
	className?: string;
	selectedServices: string[];
	locationsData: PatientResponse;
	handleSelectAll: (checked: boolean) => void;
	handleLocationSelection: (locationId: string, selected: boolean) => void;
	handleViewLocation: (location: Location) => void;
	handlePageChange: (page: number) => void;
	setShowAddServiceForm: (show: boolean) => void;
	setShowSendBookingLinkSheet: (show: boolean) => void;
}

export const AllPatients: FC<AllPatientsProps> = ({
	className,
	selectedServices,
	locationsData,
	handleSelectAll,
	handleLocationSelection,
	handleViewLocation,
	handlePageChange,
	setShowAddServiceForm,
	setShowSendBookingLinkSheet,
}) => {
	return (
		<div className="w-full">
			<div className="flex w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
				<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4">
					<div className="flex items-center pr-4">
						<Checkbox
							label=""
							checked={
								selectedServices.length ===
									locationsData?.data?.length &&
								locationsData?.data?.length > 0
							}
							className="cursor-pointer"
							onCheckedChange={handleSelectAll}
						/>
					</div>
					<div className="flex flex-2 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Name</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Status</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Email</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Phone</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Last Visit</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p></p>
						</div>
					</div>
				</div>

				{/* Locations Grid */}
				{locationsData && (
					<>
						{locationsData?.data?.length === 0 ? (
							<div className="py-12 text-center">
								<MapPin className="mx-auto h-12 w-12 text-gray-400" />
								<h3 className="mt-2 text-sm font-medium text-gray-900">
									No service found
								</h3>
								<p className="mt-1 text-sm text-gray-500">
									Get started by creating your first service.
								</p>
								<Button
									className="mt-4"
									onClick={() => setShowAddServiceForm(true)}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add a Service
								</Button>
							</div>
						) : (
							<div className="flex flex-col gap-0.5">
								{locationsData?.data?.map((patient: any) => (
									<PatientsCard
										key={patient.id}
										patient={patient}
										isSelected={selectedServices.includes(
											patient.id
										)}
										onSelectionChange={(selected) =>
											handleLocationSelection(
												patient.id,
												selected
											)
										}
										onEdit={() =>
											console.log(
												"Edit location:",
												patient.id
											)
										}
										onView={() =>
											handleViewLocation(patient)
										}
										onSendBookingLink={() =>
											setShowSendBookingLinkSheet(true)
										}
									/>
								))}
							</div>
						)}

						{/* Pagination */}
						{locationsData?.pagination?.totalPages > 1 && (
							<div className="mt-8 flex items-center justify-center gap-2">
								<Button
									variant="outline"
									disabled={
										locationsData?.pagination?.page === 1
									}
									onClick={() =>
										handlePageChange(
											locationsData?.pagination?.page - 1
										)
									}
								>
									Previous
								</Button>

								<span className="text-sm text-gray-600">
									Page {locationsData.pagination.page} of{" "}
									{locationsData?.pagination?.totalPages}
								</span>

								<Button
									variant="outline"
									disabled={
										locationsData?.pagination?.page ===
										locationsData?.pagination?.totalPages
									}
									onClick={() =>
										handlePageChange(
											locationsData?.pagination?.page + 1
										)
									}
								>
									Next
								</Button>
							</div>
						)}
					</>
				)}
			</div>
		</div>
	);
};
