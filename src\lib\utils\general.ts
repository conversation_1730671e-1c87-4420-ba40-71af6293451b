/**
 * Helper function to partially unmask an email based on certain conditions
 * @param maskedEmail - The masked email address
 * @param revealChars - Number of characters to reveal from the start
 * @returns Partially unmasked email
 */
export const partiallyUnmaskEmail = (
	maskedEmail: string,
	revealChars: number
): string => {
	const [localPart, domain] = maskedEmail.split("@");
	const unmaskedPart = localPart.slice(0, revealChars);
	const maskedPart = localPart.slice(revealChars).replace(/[^*]/g, "*");
	return `${unmaskedPart}${maskedPart}@${domain}`;
};