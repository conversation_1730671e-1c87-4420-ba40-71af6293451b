import React, { useRef, useCallback, useEffect, useState } from "react";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { mergeRegister } from "@lexical/utils";
import {
	$getSelection,
	$isRangeSelection,
	CAN_REDO_COMMAND,
	CAN_UNDO_COMMAND,
	FORMAT_TEXT_COMMAND,
	FORMAT_ELEMENT_COMMAND,
	REDO_COMMAND,
	UNDO_COMMAND,
	SELECTION_CHANGE_COMMAND,
} from "lexical";
import {
	FaBold,
	FaItalic,
	FaUnderline,
	FaStrikethrough,
	FaAlignLeft,
	FaAlignCenter,
	FaAlignRight,
	FaAlignJustify,
	FaUndo,
	FaRedo,
} from "react-icons/fa";

const LowPriority = 1;

type TextFormat = "bold" | "italic" | "underline" | "strikethrough";
type AlignmentFormat = "left" | "center" | "right" | "justify";

const ToolbarPlugin: React.FC = () => {
	const toolbarRef = useRef<HTMLDivElement>(null);
	const [editor] = useLexicalComposerContext();
	const [canUndo, setCanUndo] = useState(false);
	const [canRedo, setCanRedo] = useState(false);
	const [activeFormats, setActiveFormats] = useState(new Set<TextFormat>());
	const [activeAlignment, setActiveAlignment] =
		useState<AlignmentFormat>("left");

	const updateToolbar = useCallback(() => {
		const selection = $getSelection();
		if ($isRangeSelection(selection)) {
			// Update text formats
			const formats = new Set<TextFormat>();
			if (selection.hasFormat("bold")) formats.add("bold");
			if (selection.hasFormat("italic")) formats.add("italic");
			if (selection.hasFormat("underline")) formats.add("underline");
			if (selection.hasFormat("strikethrough"))
				formats.add("strikethrough");
			setActiveFormats(formats);

			// Update alignment
			const node = selection.anchor.getNode();
			const element = node.getParent();
			const elementKey = element?.getKey();
			const elementDOM = elementKey
				? editor.getElementByKey(elementKey)
				: null;

			if (elementDOM) {
				const textAlign = window.getComputedStyle(elementDOM)
					.textAlign as AlignmentFormat;
				setActiveAlignment(textAlign || "left");
			}
		}
	}, [editor]);

	useEffect(() => {
		return mergeRegister(
			editor.registerUpdateListener(({ editorState }) => {
				editorState.read(() => {
					updateToolbar();
				});
			}),
			editor.registerCommand(
				SELECTION_CHANGE_COMMAND,
				() => {
					updateToolbar();
					return false;
				},
				LowPriority
			),
			editor.registerCommand(
				CAN_UNDO_COMMAND,
				(payload) => {
					setCanUndo(payload);
					return false;
				},
				LowPriority
			),
			editor.registerCommand(
				CAN_REDO_COMMAND,
				(payload) => {
					setCanRedo(payload);
					return false;
				},
				LowPriority
			)
		);
	}, [editor, updateToolbar]);

	const formatText = useCallback(
		(format: TextFormat) => {
			editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
		},
		[editor]
	);

	const formatAlignment = useCallback(
		(alignment: AlignmentFormat) => {
			editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, alignment);
		},
		[editor]
	);

	const ToolbarButton: React.FC<{
		icon: React.ReactNode;
		active?: boolean;
		onClick: () => void;
		disabled?: boolean;
		title?: string;
	}> = ({ icon, active, onClick, disabled = false, title }) => (
		<button
			type="button"
			onClick={(e) => {
				e.preventDefault();
				e.stopPropagation();
				onClick();
			}}
			onMouseDown={(e) => {
				// Prevent the button from stealing focus
				e.preventDefault();
			}}
			disabled={disabled}
			title={title}
			className={`rounded p-2 transition-colors duration-200 hover:bg-gray-100 ${active ? "bg-gray-200" : ""} ${disabled ? "cursor-not-allowed opacity-50" : "cursor-pointer"}`}
		>
			{icon}
		</button>
	);

	return (
		<div
			className="flex items-center space-x-2 border-b border-gray-200 p-2"
			ref={toolbarRef}
		>
			<ToolbarButton
				icon={<FaUndo />}
				onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
				disabled={!canUndo}
				title="Undo"
			/>
			<ToolbarButton
				icon={<FaRedo />}
				onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
				disabled={!canRedo}
				title="Redo"
			/>
			<div className="mx-2 h-6 w-px bg-gray-300" />
			<ToolbarButton
				icon={<FaBold />}
				active={activeFormats.has("bold")}
				onClick={() => formatText("bold")}
				title="Bold"
			/>
			<ToolbarButton
				icon={<FaItalic />}
				active={activeFormats.has("italic")}
				onClick={() => formatText("italic")}
				title="Italic"
			/>
			<ToolbarButton
				icon={<FaUnderline />}
				active={activeFormats.has("underline")}
				onClick={() => formatText("underline")}
				title="Underline"
			/>
			<ToolbarButton
				icon={<FaStrikethrough />}
				active={activeFormats.has("strikethrough")}
				onClick={() => formatText("strikethrough")}
				title="Strikethrough"
			/>
			<div className="mx-2 h-6 w-px bg-gray-300" />
			<ToolbarButton
				icon={<FaAlignLeft />}
				active={activeAlignment === "left"}
				onClick={() => formatAlignment("left")}
				title="Align Left"
			/>
			<ToolbarButton
				icon={<FaAlignCenter />}
				active={activeAlignment === "center"}
				onClick={() => formatAlignment("center")}
				title="Align Center"
			/>
			<ToolbarButton
				icon={<FaAlignRight />}
				active={activeAlignment === "right"}
				onClick={() => formatAlignment("right")}
				title="Align Right"
			/>
			<ToolbarButton
				icon={<FaAlignJustify />}
				active={activeAlignment === "justify"}
				onClick={() => formatAlignment("justify")}
				title="Justify"
			/>
		</div>
	);
};

export default ToolbarPlugin;
