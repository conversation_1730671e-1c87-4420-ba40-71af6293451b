import React from 'react';
import { CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  preferenceName: string;
  locationName?: string;
  contextLevel?: 'organization' | 'location' | 'provider';
  onViewAll: () => void;
  onAddAnother: () => void;
}

export const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  preferenceName,
  locationName = 'Organization',
  contextLevel = 'organization',
  onViewAll,
  onAddAnother
}) => {
  const getContextLabel = () => {
    switch (contextLevel) {
      case 'location':
        return 'Location';
      case 'provider':
        return 'Provider';
      default:
        return 'Organization';
    }
  };

  const getSuccessMessage = () => {
    const contextLabel = getContextLabel();
    return `Your custom preference "${preferenceName}" for ${contextLabel} ${locationName} has been successfully added and is now active.`;
  };

  const handleViewAll = () => {
    onClose();
    onViewAll();
  };

  const handleAddAnother = () => {
    onClose();
    onAddAnother();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader className="text-center">
          <DialogTitle className="sr-only">Preference Added Successfully</DialogTitle>
        </DialogHeader>
        
        <div className="text-center py-4">
          {/* Success Icon */}
          <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-12 w-12 text-primary" />
          </div>

          {/* Success Message */}
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Preference Successfully Added!
          </h1>
          
          <p className="text-gray-600 mb-8 leading-relaxed">
            {getSuccessMessage()}
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button 
              variant="link" 
              onClick={handleViewAll}
              className="text-primary hover:text-primary/70 w-full"
            >
              View All Preferences
            </Button>
            
            <Button 
              onClick={handleAddAnother}
            >
              Add Another Preference
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};