export interface Patient {
	id: string;
	name: string;
	email?: string;
	phone?: string;
}

export interface Service {
	id: string;
	name: string;
	description?: string;
	duration?: number;
	price?: number;
}

export interface BookingLinkConfig {
	tab:
		| "organization"
		| "location"
		| "provider"
		| "custom"
		| "waitlist-settings";
	patients: string[];
	services: string[];
	message: string;
	sendViaSMS: boolean;
	sendViaEmail: boolean;
	expirationDays: string;
	useSpecificDate: boolean;
	specificDate?: Date;
}

export interface BookingLink {
	id: string;
	url: string;
	config: BookingLinkConfig;
	createdAt: Date;
	expiresAt: Date;
	isActive: boolean;
}
