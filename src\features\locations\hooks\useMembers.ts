import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
	membersApi,
	type CreateMemberRequest,
	type MemberData,
} from "../api/membersApi";

interface UseMembersOptions {
	organizationId?: string;
	enabled?: boolean;
	locationId?: number;
	stationId?: number;
	filters?: Record<string, any>;
}

interface UseCreateMemberOptions {
	organizationId: string;
	onSuccess?: (data: MemberData) => void;
	onError?: (error: Error) => void;
}

export function useMembers({
	organizationId,
	enabled = true,
	locationId,
	stationId,
	filters = {},
}: UseMembersOptions = {}) {
	return useQuery({
		queryKey: ["members", organizationId, locationId, stationId, filters],
		queryFn: () => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}

			// Build URLSearchParams from filters
			const params = new URLSearchParams();
			Object.entries(filters).forEach(([key, value]) => {
				if (value !== undefined && value !== null && value !== "") {
					if (Array.isArray(value)) {
						value.forEach((item) => {
							if (item !== "") {
								params.append(key, item.toString());
							}
						});
					} else {
						params.append(key, value.toString());
					}
				}
			});

			return membersApi.getMembers(
				organizationId,
				locationId,
				stationId,
				params
			);
		},
		enabled: enabled && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		cacheTime: 10 * 60 * 1000, // 10 minutes
	});
}

export function useCreateMember({
	organizationId,
	onSuccess,
	onError,
}: UseCreateMemberOptions) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: CreateMemberRequest) =>
			membersApi.createMember(data, organizationId),
		onSuccess: (response) => {
			// Invalidate and refetch members list
			queryClient.invalidateQueries({
				queryKey: ["members", organizationId],
			});
			onSuccess?.(response.data);
		},
		onError: (error: Error) => {
			onError?.(error);
		},
	});
}

export function useUpdateMember({
	organizationId,
	onSuccess,
	onError,
}: UseCreateMemberOptions) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			memberId,
			data,
		}: {
			memberId: number;
			data: Partial<CreateMemberRequest>;
		}) => membersApi.updateMember(organizationId, memberId, data),
		onSuccess: (response) => {
			// Invalidate and refetch members list
			queryClient.invalidateQueries({
				queryKey: ["members", organizationId],
			});
			onSuccess?.(response.data);
		},
		onError: (error: Error) => {
			onError?.(error);
		},
	});
}

export function useDeleteMember({
	organizationId,
	onSuccess,
	onError,
}: Omit<UseCreateMemberOptions, "onSuccess"> & { onSuccess?: () => void }) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (memberId: number) =>
			membersApi.deleteMember(organizationId, memberId),
		onSuccess: () => {
			// Invalidate and refetch members list
			queryClient.invalidateQueries({
				queryKey: ["members", organizationId],
			});
			onSuccess?.();
		},
		onError: (error: Error) => {
			onError?.(error);
		},
	});
}
