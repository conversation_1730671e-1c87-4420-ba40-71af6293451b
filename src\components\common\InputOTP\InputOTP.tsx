import React, { forwardRef } from 'react';
import { Controller } from 'react-hook-form';
import type {  Control, FieldValues, Path, RegisterOptions } from 'react-hook-form';
import { cn } from '@/lib/utils';
import {
  InputOTP as BaseInputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Type definitions
interface BaseOTPProps {
  maxLength?: number;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  className?: string;
  containerClassName?: string;
  disabled?: boolean;
}

interface DefaultOTPProps extends BaseOTPProps {
  variant?: 'default';
}

interface WithSeparatorOTPProps extends BaseOTPProps {
  variant: 'with-separator';
  groupSizes?: number[];
  separatorChar?: string;
}

interface PatternOTPProps extends BaseOTPProps {
  variant: 'pattern';
  pattern?: RegExp;
  patternDescription?: string;
}

interface DisabledOTPProps extends Omit<BaseOTPProps, 'disabled'> {
  variant: 'disabled';
}

interface FormOTPProps<T extends FieldValues = FieldValues> {
  variant: 'form';
  name: Path<T>;
  control: Control<T>;
  label?: string;
  description?: string;
  rules?: RegisterOptions<T>;
  maxLength?: number;
  className?: string;
  containerClassName?: string;
}

// Helper type for inferring form types
type InferredFormOTPProps<T extends FieldValues> = FormOTPProps<T>;

type InputOTPProps<T extends FieldValues = FieldValues> = 
  | DefaultOTPProps 
  | WithSeparatorOTPProps 
  | PatternOTPProps 
  | DisabledOTPProps 
  | FormOTPProps<T>;

// Default OTP Implementation
const DefaultOTPInput = forwardRef<
  React.ElementRef<typeof BaseInputOTP>,
  DefaultOTPProps
>(({ 
  maxLength = 6, 
  value, 
  onChange, 
  onBlur,
  className,
  containerClassName,
  disabled,
  ...props 
}, ref) => {
  return (
    <BaseInputOTP
      ref={ref}
      maxLength={maxLength}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      disabled={disabled}
      containerClassName={cn(
        "flex items-center gap-2 has-[:disabled]:opacity-50",
        containerClassName
      )}
      className={cn("disabled:cursor-not-allowed", className)}
      {...props}
    >
      <InputOTPGroup>
        {Array.from({ length: maxLength }, (_, index) => (
          <InputOTPSlot key={index} index={index} />
        ))}
      </InputOTPGroup>
    </BaseInputOTP>
  );
});

// With Separator OTP Implementation
const WithSeparatorOTPInput = forwardRef<
  React.ElementRef<typeof BaseInputOTP>,
  WithSeparatorOTPProps
>(({ 
  maxLength = 6, 
  groupSizes = [3, 3],
  separatorChar = "−",
  value, 
  onChange, 
  onBlur,
  className,
  containerClassName,
  disabled,
  ...props 
}, ref) => {
  // Validate that group sizes add up to maxLength
  const totalSlots = groupSizes.reduce((sum, size) => sum + size, 0);
  if (totalSlots !== maxLength) {
    console.warn(`Group sizes (${totalSlots}) don't match maxLength (${maxLength})`);
  }

  const renderGroups = () => {
    const groups: any = [];
    let currentIndex = 0;

    groupSizes.forEach((groupSize, groupIndex) => {
      // Add separator before each group (except the first)
      if (groupIndex > 0) {
        groups.push(
          <InputOTPSeparator key={`separator-${groupIndex}`}>
            {separatorChar}
          </InputOTPSeparator>
        );
      }

      // Add the group with slots
      groups.push(
        <InputOTPGroup key={`group-${groupIndex}`}>
          {Array.from({ length: groupSize }, (_,) => (
            <InputOTPSlot key={currentIndex} index={currentIndex++} />
          ))}
        </InputOTPGroup>
      );
    });

    return groups;
  };

  return (
    <BaseInputOTP
      ref={ref}
      maxLength={maxLength}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      disabled={disabled}
      containerClassName={cn(
        "flex items-center gap-2 has-[:disabled]:opacity-50",
        containerClassName
      )}
      className={cn("disabled:cursor-not-allowed", className)}
      {...props}
    >
      {renderGroups()}
    </BaseInputOTP>
  );
});

// Pattern OTP Implementation
const PatternOTPInput = forwardRef<
  React.ElementRef<typeof BaseInputOTP>,
  PatternOTPProps
>(({ 
  maxLength = 6, 
  pattern,
  patternDescription,
  value, 
  onChange, 
  onBlur,
  className,
  containerClassName,
  disabled,
  ...props 
}, ref) => {
  // Enhanced onChange handler with pattern validation
  const handlePatternChange = (newValue: string) => {
    if (pattern && newValue && !pattern.test(newValue)) {
      // Optionally prevent invalid input or just pass through
      // For now, we'll pass through and let validation handle it
    }
    onChange?.(newValue);
  };

  return (
    <div className="space-y-2">
      <BaseInputOTP
        ref={ref}
        maxLength={maxLength}
        value={value}
        onChange={handlePatternChange}
        onBlur={onBlur}
        disabled={disabled}
        containerClassName={cn(
          "flex items-center gap-2 has-[:disabled]:opacity-50",
          containerClassName
        )}
        className={cn("disabled:cursor-not-allowed", className)}
        {...props}
      >
        <InputOTPGroup>
          {Array.from({ length: maxLength }, (_, index) => (
            <InputOTPSlot key={index} index={index} />
          ))}
        </InputOTPGroup>
      </BaseInputOTP>
      {patternDescription && (
        <p className="text-xs text-muted-foreground">{patternDescription}</p>
      )}
    </div>
  );
});

// Disabled OTP Implementation - Fixed circular reference
const DisabledOTPInput = forwardRef<
  React.ElementRef<typeof BaseInputOTP>,
  DisabledOTPProps
>(({ 
  maxLength = 6, 
  value, 
  className,
  containerClassName,
  ...props 
}, ref) => {
  return (
    <BaseInputOTP
      ref={ref}
      maxLength={maxLength}
      value={value}
      disabled={true}
      containerClassName={cn(
        "flex items-center gap-2 opacity-50",
        containerClassName
      )}
      className={cn("cursor-not-allowed", className)}
      {...props}
    >
      <InputOTPGroup>
        {Array.from({ length: maxLength }, (_, index) => (
          <InputOTPSlot key={index} index={index} />
        ))}
      </InputOTPGroup>
    </BaseInputOTP>
  );
});

// Form OTP Implementation - Enhanced with better error handling
function FormOTPInput<T extends FieldValues = FieldValues>({
  name,
  control,
  label,
  description,
  rules,
  maxLength = 6,
  className,
  containerClassName,
  ...props
}: FormOTPProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <BaseInputOTP
              maxLength={maxLength}
              value={field.value || ''}
              onChange={field.onChange}
              onBlur={field.onBlur}
              containerClassName={cn(
                "flex items-center gap-2 has-[:disabled]:opacity-50",
                fieldState.error && "has-[:focus]:ring-destructive",
                containerClassName
              )}
              className={cn(
                "disabled:cursor-not-allowed",
                fieldState.error && "focus:ring-destructive",
                className
              )}
              {...props}
            >
              <InputOTPGroup>
                {Array.from({ length: maxLength }, (_, index) => (
                  <InputOTPSlot 
                    key={index} 
                    index={index}
                    className={cn(
                      fieldState.error && "border-destructive"
                    )}
                  />
                ))}
              </InputOTPGroup>
            </BaseInputOTP>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Simplified main InputOTP component without complex generics
export const InputOTP = (props: any) => {
  const variant = 'variant' in props ? props.variant : 'default';

  switch (variant) {
    case 'with-separator':
      return <WithSeparatorOTPInput {...props} />;
    case 'pattern':
      return <PatternOTPInput {...props} />;
    case 'form':
      return <FormOTPInput {...props} />;
    case 'disabled':
      return <DisabledOTPInput {...props} />;
    default:
      return <DefaultOTPInput {...props} />;
  }
};

// Display names
InputOTP.displayName = 'InputOTP';
DefaultOTPInput.displayName = 'DefaultOTPInput';
WithSeparatorOTPInput.displayName = 'WithSeparatorOTPInput';
PatternOTPInput.displayName = 'PatternOTPInput';
DisabledOTPInput.displayName = 'DisabledOTPInput';

// Export types for external use
export type {
  InputOTPProps,
  DefaultOTPProps,
  WithSeparatorOTPProps,
  PatternOTPProps,
  DisabledOTPProps,
  FormOTPProps,
  InferredFormOTPProps,
};