import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, MapPin, User, Users, Calendar, Settings } from 'lucide-react';

interface PlannerDashboardProps {
  onSelectLevel: (level: 'organization' | 'location' | 'provider') => void;
}

export const PlannerDashboard: React.FC<PlannerDashboardProps> = ({ onSelectLevel }) => {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-semibold text-gray-900 mb-2">Welcome to Planner</h1>
          <p className="text-gray-600">
            Here, you can set schedule preferences, restrictions, and time off for your Organization, Locations, and Providers.
          </p>
        </div>

        {/* Three Main Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Organization Card */}
          <Card 
            className="cursor-pointer hover:shadow-lg transition-shadow duration-200 border-gray-200"
            onClick={() => onSelectLevel('organization')}
          >
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <Building2 className="h-12 w-12 text-gray-400 mb-2" />
                  {/* Hierarchical diagram */}
                  <div className="flex justify-center mt-4">
                    <div className="space-y-3">
                      <div className="flex justify-center">
                        <div className="w-8 h-3 bg-gray-300 rounded"></div>
                      </div>
                      <div className="flex justify-center space-x-2">
                        <div className="w-6 h-3 bg-gray-300 rounded"></div>
                        <div className="w-6 h-3 bg-gray-300 rounded"></div>
                        <div className="w-6 h-3 bg-gray-300 rounded"></div>
                      </div>
                      <div className="flex justify-center space-x-1">
                        {[...Array(9)].map((_, i) => (
                          <div key={i} className="w-3 h-3 bg-gray-300 rounded"></div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CardTitle className="text-xl font-semibold">Organization Wide Preferences</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <CardDescription className="text-sm text-gray-600 leading-relaxed">
                Set availability preferences, time off, maximum number of services available and more across the organization.
              </CardDescription>
            </CardContent>
          </Card>

          {/* Location Card */}
          <Card 
            className="cursor-pointer hover:shadow-lg transition-shadow duration-200 border-gray-200"
            onClick={() => onSelectLevel('location')}
          >
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <MapPin className="h-12 w-12 text-gray-400 mb-2" />
                  {/* Location diagram */}
                  <div className="flex justify-center mt-4">
                    <div className="space-y-3">
                      <div className="flex justify-center">
                        <div className="w-8 h-3 bg-gray-300 rounded"></div>
                      </div>
                      <div className="flex justify-center space-x-4">
                        <User className="h-6 w-6 text-gray-400" />
                        <User className="h-6 w-6 text-gray-400" />
                        <User className="h-6 w-6 text-gray-400" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CardTitle className="text-xl font-semibold">Location Wide Preferences</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <CardDescription className="text-sm text-gray-600 leading-relaxed">
                Set availability preferences, time off, maximum number of services available, category preferences at a location.
              </CardDescription>
            </CardContent>
          </Card>

          {/* Provider Card */}
          <Card 
            className="cursor-pointer hover:shadow-lg transition-shadow duration-200 border-gray-200"
            onClick={() => onSelectLevel('provider')}
          >
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <User className="h-12 w-12 text-gray-400 mb-2" />
                  {/* Provider diagram */}
                  <div className="flex justify-center mt-4">
                    <div className="flex space-x-4">
                      <User className="h-8 w-8 text-gray-400" />
                      <User className="h-8 w-8 text-gray-400" />
                      <User className="h-8 w-8 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>
              <CardTitle className="text-xl font-semibold">Provider Preferences</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <CardDescription className="text-sm text-gray-600 leading-relaxed">
                Set availability preferences, time off, maximum number of services available, category preferences for Providers.
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};