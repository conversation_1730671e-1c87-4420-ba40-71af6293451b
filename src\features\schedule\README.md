# Schedule Feature

This feature contains components and functionality related to booking and scheduling.

## Components

### SendBookingLinkSheet

A sheet component for sharing booking links with patients. The sheet slides in from the right side and allows users to configure and send booking links via SMS or email.

#### Features

- **Multi-tab interface**: Organization, Location, Provider, Custom, and Waitlist Settings tabs
- **Patient selection**: Multi-select dropdown with ability to add new patients
- **Service selection**: Multi-select dropdown for available services
- **Message customization**: Textarea for custom messages
- **Delivery options**: Choose to send via SMS, Email, or both
- **Expiration settings**: Configure when the booking link expires
- **Specific date option**: Option to set a specific expiration date

#### Usage

```tsx
import { SendBookingLinkSheet } from "@/features/schedule";

function MyComponent() {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<Button onClick={() => setIsOpen(true)}>Send Booking Link</Button>

			<SendBookingLinkSheet open={isOpen} onOpenChange={setIsOpen} />
		</>
	);
}
```

#### Props

| Prop           | Type                      | Description                            |
| -------------- | ------------------------- | -------------------------------------- |
| `open`         | `boolean`                 | Controls whether the sheet is open     |
| `onOpenChange` | `(open: boolean) => void` | Callback when sheet open state changes |

#### Current Implementation Status

- ✅ **Organization Tab**: Fully implemented with all form fields
- ⏳ **Location Tab**: Placeholder (coming soon)
- ⏳ **Provider Tab**: Placeholder (coming soon)
- ⏳ **Custom Tab**: Placeholder (coming soon)
- ⏳ **Waitlist Settings Tab**: Placeholder (coming soon)

#### Design Reference

This component is based on the Figma design at:
`https://www.figma.com/design/Os4pxQgRL4tPrkjR2w6gjI/Migranium-v3.0?node-id=77-36079`

#### Dependencies

- `@/components/ui/sheet` - Sheet structure that slides from the right
- `@/components/ui/button` - Action buttons
- `@/components/ui/checkbox` - Send via options
- `@/components/ui/textarea` - Message field
- `@/components/ui/input` - Expiration days input
- `@/components/ui/select` - Time unit dropdown
- `@/components/common/Tabs` - Tab navigation
- `@/components/common/MultiAsyncSelect` - Patient and service selection
- `lucide-react` - Icons (UserPlus)

#### Styling

The component uses Tailwind CSS classes and follows the design system color palette:

- Primary color: `#005893` (brand blue)
- Text colors: Various zinc shades for hierarchy
- Consistent spacing and border radius values
