import React from "react";

const SettingsIcon: React.FC = () => {
	return (
		<svg
			width="32"
			height="33"
			viewBox="0 0 32 33"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M28.0145 19.2957C28.7104 19.1081 29.0584 19.0143 29.1957 18.8348C29.3332 18.6555 29.3332 18.3668 29.3332 17.7896V15.2113C29.3332 14.6341 29.3332 14.3455 29.1957 14.1661C29.0582 13.9867 28.7104 13.8928 28.0145 13.7052C25.414 13.0039 23.7864 10.2851 24.4576 7.70157C24.6421 6.99107 24.7344 6.63583 24.6462 6.42747C24.5581 6.21911 24.3053 6.07553 23.7994 5.78836L21.4998 4.48272C21.0036 4.20093 20.7554 4.06004 20.5328 4.09004C20.31 4.12004 20.0588 4.37072 19.5561 4.87205C17.6105 6.81305 14.3913 6.81297 12.4456 4.87193C11.9431 4.37059 11.6918 4.11992 11.4691 4.08991C11.2464 4.05991 10.9982 4.2008 10.5019 4.48259L8.20229 5.78824C7.69654 6.07539 7.44366 6.21896 7.35554 6.42728C7.26741 6.63561 7.35966 6.99091 7.54417 7.70148C8.215 10.2851 6.58613 13.0039 3.9852 13.7052C3.28933 13.8928 2.9414 13.9867 2.80394 14.166C2.6665 14.3455 2.6665 14.6341 2.6665 15.2113V17.7896C2.6665 18.3668 2.6665 18.6555 2.80394 18.8348C2.94137 19.0143 3.28932 19.1081 3.9852 19.2957C6.5857 19.9971 8.21328 22.7159 7.54213 25.2993C7.35756 26.0099 7.26526 26.3651 7.35338 26.5735C7.44152 26.7819 7.6944 26.9255 8.20017 27.2125L10.4998 28.5183C10.9961 28.8 11.2443 28.9409 11.467 28.9109C11.6898 28.8809 11.941 28.6301 12.4435 28.1288C14.3901 26.1863 17.6116 26.1861 19.5584 28.1287C20.0608 28.6301 20.312 28.8808 20.5348 28.9108C20.7574 28.9408 21.0057 28.7999 21.502 28.5181L23.8016 27.2124C24.3074 26.9253 24.5604 26.7817 24.6484 26.5733C24.7365 26.365 24.6442 26.0097 24.4596 25.2992C23.7881 22.7159 25.4144 19.9972 28.0145 19.2957Z"
				fill="#C4F4F6"
				stroke="black"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path
				d="M20.6668 16.4987C20.6668 19.076 18.5775 21.1654 16.0002 21.1654C13.4228 21.1654 11.3335 19.076 11.3335 16.4987C11.3335 13.9214 13.4228 11.832 16.0002 11.832C18.5775 11.832 20.6668 13.9214 20.6668 16.4987Z"
				stroke="black"
				strokeWidth="2"
			/>
		</svg>
	);
};

export default SettingsIcon;
