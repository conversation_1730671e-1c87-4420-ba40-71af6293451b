# 2FA Route Setup Documentation

## Overview

The 2FA (Two-Factor Authentication) route has been properly configured and is fully functional. Users are automatically redirected to the 2FA page after successful login when 2FA is enabled on their account.

## Route Configuration

### Main Route
- **Path**: `/2fa`
- **Component**: `MFAPage`
- **File**: `src/pages/auth/MFAPage.tsx`

### Route Definition
```typescript
// src/main.tsx
{
  path: "/2fa",
  element: <MFAPage />,
}
```

### Route Constants
```typescript
// src/lib/utils/constants.ts
export const ROUTES = {
  // ... other routes
  AUTH: {
    SIGNIN: '/sign-in',
    SIGNUP: '/auth/signup',
    FORGOT_PASSWORD: '/forgot-password',
    RESET_PASSWORD: '/reset-password',
    MFA: '/2fa',  // 2FA route
  },
} as const
```

## Authentication Flow

### 1. Login Process
When a user logs in with 2FA enabled:

1. **Login API Call**: User submits credentials
2. **2FA Check**: Backend returns `two_factor_enable: true`
3. **Token Storage**: MFA token is stored in localStorage
4. **Navigation**: User is redirected to `/2fa`

### 2. 2FA Verification
On the 2FA page:

1. **Token Retrieval**: MFA token is retrieved from localStorage
2. **Code Entry**: User enters 6-digit verification code
3. **Verification**: Code is sent to backend for verification
4. **Success**: User is redirected to dashboard
5. **Cleanup**: MFA token is removed from localStorage

## Components

### MFAPage
- **Location**: `src/pages/auth/MFAPage.tsx`
- **Purpose**: Page wrapper for 2FA functionality
- **Layout**: Centered card layout with proper spacing

### MFACard
- **Location**: `src/components/forms/auth/mfa/MFACard.tsx`
- **Purpose**: Main 2FA component with multiple screens
- **Features**:
  - QR code setup for authenticator apps
  - Manual code entry setup
  - 6-digit OTP verification using common InputOTP component
  - Recovery code entry (21 characters) using common InputOTP component
  - Email OTP sending
  - Recovery code generation and display
- **Components Used**:
  - `InputOTP` from `@/components/common/InputOTP` - Reusable OTP input component

## Navigation Logic

### Automatic Redirects
The following components handle 2FA navigation:

1. **useLoginSuccess Hook** (`src/hooks/useLoginSuccess.tsx`)
   - Detects 2FA requirement
   - Navigates to `ROUTES.AUTH.MFA`

2. **Auth Slice** (`src/stores/slices/authSlice.ts`)
   - Login mutations check for 2FA
   - Navigate to 2FA page when required

### Route Constants Usage
All navigation now uses route constants instead of hardcoded paths:

```typescript
// Before
navigate('/2fa');

// After
navigate(ROUTES.AUTH.MFA);
```

## 2FA Screens

The MFACard component supports multiple screens:

1. **default**: Initial 2FA setup screen
2. **auth-app-setup**: QR code scanning setup
3. **manual-auth-app-setup**: Manual code entry setup
4. **verify-login**: 6-digit OTP verification
5. **recovery-code-login**: Recovery code entry
6. **recovery-code**: Recovery code display and download

## API Integration

### Required Endpoints
- `POST /api/v1/auth/2fa/enable` - Enable 2FA
- `POST /api/v1/auth/2fa/confirm` - Confirm 2FA setup
- `POST /api/v1/auth/2fa/verify` - Verify 2FA code
- `POST /api/v1/auth/2fa/email-otp` - Send email OTP
- `POST /api/v1/auth/2fa/skip` - Skip 2FA setup

### Token Management
- MFA tokens are stored in localStorage as `mfa_token`
- Tokens are automatically cleaned up after successful verification
- Tokens are removed on logout

## Security Features

1. **Token Expiration**: MFA tokens have limited lifespan
2. **Attempt Counting**: Failed attempts are tracked
3. **Recovery Codes**: Backup access method provided
4. **Email OTP**: Alternative verification method
5. **Auto-cleanup**: Tokens removed after use

## Error Handling

The 2FA components use centralized error handling utilities from `src/lib/utils/errorHandling.ts`:

### Error Handling Utilities Used
- `handle2FAError()` - Handles 2FA-specific errors with appropriate messages
- `handleAPIError()` - Handles general API errors with custom default messages
- `logError()` - Logs error details for debugging

### Benefits
- **Consistent Error Messages**: All 2FA errors are handled consistently
- **Better Debugging**: Standardized error logging
- **User-Friendly Messages**: Appropriate fallback messages for different error types
- **Reusable**: Same utilities can be used across other components

### Example Usage
```typescript
import { handle2FAError, logError } from '@/lib/utils/errorHandling';

try {
  await verify2FA(code);
} catch (error) {
  const message = handle2FAError(error);
  logError(error, "2FA Verification Error");
  setError("otp", { message });
}
```

For more details, see `ERROR_HANDLING_UTILS.md`.

## Testing

To test the 2FA flow:

1. **Login with 2FA-enabled account**
2. **Verify redirect to `/2fa`**
3. **Test OTP entry**
4. **Test recovery code entry**
5. **Verify successful navigation to dashboard**

## Troubleshooting

### Common Issues
1. **Navigation not working**: Check route constants are imported
2. **Token not found**: Verify localStorage cleanup
3. **API errors**: Check endpoint configuration
4. **Component not rendering**: Verify MFAPage import

### Debug Steps
1. Check browser console for errors
2. Verify localStorage has `mfa_token`
3. Confirm API endpoints are accessible
4. Test route navigation manually

## Future Enhancements

1. **Route Protection**: Add authentication guards
2. **Session Management**: Improve token handling
3. **Error Recovery**: Better error handling and recovery
4. **Accessibility**: Improve keyboard navigation
5. **Mobile Optimization**: Better mobile experience 