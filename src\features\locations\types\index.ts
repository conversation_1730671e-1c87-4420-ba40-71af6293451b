import type { Station } from "@/types/signup";

export interface Location {
	id: string;
	name: string;
	address: string;
	phone?: string;
	email?: string;
	description?: string;
	isActive: boolean;
	timezone: string;
	coordinates?: {
		latitude: number;
		longitude: number;
	};
	operatingHours?: OperatingHours[];
	services?: string[];
	capacity?: number;
	amenities?: string[];
	organizationId: string;
	createdAt: string;
	updatedAt: string;
	stations: {
		id: string;
		name: string;
	}[];
	service_count: number;
}

export interface OperatingHours {
	dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
	openTime: string; // "09:00"
	closeTime: string; // "17:00"
	isClosed: boolean;
}

export interface Service {
	id: string;
	serviceName: string;
	description?: string;
	autoApprove: boolean;
	serviceVisibility: boolean;
	serviceAvailability: boolean;
	availableMethods: string[];
	serviceDuration: number;
	durationUnit: "minutes" | "hours";
	locationId?: string;
	isActive: boolean;
	createdAt: string;
	updatedAt: string;
}

export interface CreateServiceRequest {
	serviceName: string;
	description?: string;
	autoApprove: boolean;
	serviceVisibility: boolean;
	serviceAvailability: boolean;
	availableMethods: string[];
	serviceDuration: number;
	durationUnit: "minutes" | "hours";
	locationId?: string;
	// Step 2 fields
	applyServiceTo: "all-locations" | "selected-locations";
	selectedLocations?: string[];
}

export interface CreateStationRequest {
	stationName: string;
	description?: string;
	autoApprove: boolean;
	serviceVisibility: boolean;
	serviceAvailability: boolean;
	availableMethods: string[];
	stationDuration: number;
	durationUnit: "minutes" | "hours";
	stationId?: string;
	// Step 2 fields
	applyStationTo: "all-locations" | "selected-locations";
	selectedStations?: string[];
}

export interface UpdateServiceRequest extends Partial<CreateServiceRequest> {
	id: string;
	isActive?: boolean;
}

export interface CreateLocationRequest {
	name: string;
	address: Location["address"];
	phone?: string;
	email?: string;
	description?: string;
	timezone: string;
	coordinates?: Location["coordinates"];
	operatingHours?: OperatingHours[];
	services?: string[];
	capacity?: number;
	amenities?: string[];
}

export interface UpdateLocationRequest extends Partial<CreateLocationRequest> {
	id: string;
	isActive?: boolean;
}

export interface LocationsFilters {
	search?: string;
	isActive?: boolean;
	city?: string;
	state?: string;
	services?: string[];
	page?: number;
	limit?: number;
	sortBy?: "name" | "city" | "createdAt" | "updatedAt";
	sortOrder?: "asc" | "desc";
}

export interface LocationsResponse {
	data: Location[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

export interface LocationProvider {
	id: string;
	name: string;
	email: string;
	phone: string;
	address: Location["address"];
	services: Service[];
}

export interface AppointmentMethod {
	id: number;
	name: string;
}

export interface AppointmentMethodsResponse {
	success: boolean;
	message: string;
	data: AppointmentMethod[];
}

export interface CreateProviderStationRequest {
	name: string;
	image?: string;
	description?: string;
	service_provider_first_name: string;
	service_provider_last_name: string;
	service_provider_email: string;
	service_provider_phone: string;
}
