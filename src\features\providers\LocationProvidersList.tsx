import { useState, type FC } from "react";
import { MapPin } from "lucide-react";
import { useLocations } from "../locations/hooks";
import type { LocationsFilters } from "../locations/types";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { LocationProvidersTab } from "./LocationProvidersTab";
import { ServicesTab } from "../locations/components/ServicesTab";
import { ScheduleSettingsTab } from "../locations/components/ScheduleSettingsTab";
import { WalkInSettingsTab } from "../locations/components/WalkInSettingsTab";
import { TeamMembersTab } from "../locations/components/TeamMemberTabs";

export interface LocationProvidersListProps {
	className?: string;
	onAddStation?: () => void;
	locationId?: string;
	organizationId?: number;
	onProviderCreated?: () => void;
	onRefetchReady?: (refetch: () => void) => void;
}

export const LocationProvidersList: FC<LocationProvidersListProps> = ({
	className,
	onAddStation,
	locationId,
	organizationId,
	onProviderCreated,
	onRefetchReady,
}) => {
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});

	const { error } = useLocations(filters);

	if (error) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<MapPin className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">
						Error loading locations
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						{error instanceof Error
							? error.message
							: "Something went wrong"}
					</p>
				</div>
			</div>
		);
	}

	const items = [
		{ value: "providers", label: "Providers" },
		{ value: "services", label: "Services" },
		{ value: "members", label: "Members" },
		{ value: "schedule-settings", label: "Schedule Settings" },
		{ value: "walk-in-settings", label: "Walk-in Settings" },
		{ value: "organization-settings", label: "Organization Settings" },
	];

	return (
		<div className={cn("flex flex-col gap-0.5 p-2", className)}>
			{/* Tabs */}
			<Tabs
				items={items}
				defaultValue="providers"
				useRouting={true}
				searchParamKey="location-tab"
			>
				<TabsContent value="providers">
					<LocationProvidersTab
						onAddStation={onAddStation}
						locationId={locationId}
						organizationId={organizationId}
						onProviderCreated={onProviderCreated}
						onRefetchReady={onRefetchReady}
					/>
				</TabsContent>
				<TabsContent value="services">
					<ServicesTab />
				</TabsContent>
				<TabsContent value="members">
					<TeamMembersTab locationId={locationId} />
				</TabsContent>
				<TabsContent value="schedule-settings">
					<ScheduleSettingsTab />
				</TabsContent>
				<TabsContent value="walk-in-settings">
					<WalkInSettingsTab />
				</TabsContent>
			</Tabs>
		</div>
	);
};
