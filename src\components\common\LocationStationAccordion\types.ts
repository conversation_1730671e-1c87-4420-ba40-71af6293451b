export interface Station {
	id: string;
	name: string;
}

export interface Location {
	id: string;
	name: string;
	stations: Station[];
}

export type ApplyServiceOption = "all-locations" | "selected-locations";

export interface LocationStationAccordionProps {
	locations: Location[];
	selectedLocationIds: Set<string>;
	selectedStationIds: Set<string>;
	onLocationSelectionChange: (locationId: string, checked: boolean) => void;
	onStationSelectionChange: (stationId: string, checked: boolean) => void;
	onBack?: () => void;

	// Radio group props
	applyServiceTo?: ApplyServiceOption;
	onApplyServiceToChange?: (value: ApplyServiceOption) => void;

	// Text customization
	title?: string;
	subtitle?: string;
	searchPlaceholder?: string;
	allLocationsTitle?: string;
	allLocationsSubtitle?: string;
	selectedLocationsTitle?: string;
	selectedLocationsSubtitle?: string;

	// Display options
	showBackButton?: boolean;
	showRadioOptions?: boolean; // New prop to control whether to show radio options first
}
