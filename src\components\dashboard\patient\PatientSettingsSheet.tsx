import { useState } from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { X, Trash2, Plus } from "lucide-react";

interface PatientSettingsSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSave?: (settings: PatientSettings) => void;
}

interface DropdownOption {
	id: string;
	value: string;
}

interface PatientField {
	id: string;
	label: string;
	type: string;
	isRequired: boolean;
	isValidator: boolean;
	options?: DropdownOption[];
}

export interface PatientSettings {
	customFields: PatientField[];
	systemFields: PatientField[];
}

export function PatientSettingsSheet({
	open,
	onOpenChange,
	onSave,
}: PatientSettingsSheetProps) {
	const [settings, setSettings] = useState<PatientSettings>({
		customFields: [
			{
				id: "gender",
				label: "Gender",
				type: "dropdown",
				isRequired: true,
				isValidator: false,
				options: [
					{ id: "1", value: "Male" },
					{ id: "2", value: "Female" },
					{ id: "3", value: "Other" },
				],
			},
		],
		systemFields: [
			{
				id: "fullName",
				label: "Full Name",
				type: "text",
				isRequired: true,
				isValidator: false,
			},
			{
				id: "phoneNumber",
				label: "Phone Number",
				type: "number",
				isRequired: true,
				isValidator: false,
			},
			{
				id: "email",
				label: "Email",
				type: "text",
				isRequired: true,
				isValidator: false,
			},
			{
				id: "dateOfBirth",
				label: "Date of Birth",
				type: "date",
				isRequired: true,
				isValidator: false,
			},
		],
	});

	const handleFieldUpdate = (
		fieldType: "customFields" | "systemFields",
		fieldId: string,
		updates: Partial<PatientField>
	) => {
		setSettings((prev) => ({
			...prev,
			[fieldType]: prev[fieldType].map((field) =>
				field.id === fieldId ? { ...field, ...updates } : field
			),
		}));
	};

	const handleAddDropdownOption = (fieldId: string, value: string) => {
		setSettings((prev) => ({
			...prev,
			customFields: prev.customFields.map((field) =>
				field.id === fieldId
					? {
							...field,
							options: [
								...(field.options || []),
								{ id: Date.now().toString(), value },
							],
						}
					: field
			),
		}));
	};

	const handleRemoveDropdownOption = (fieldId: string, optionId: string) => {
		setSettings((prev) => ({
			...prev,
			customFields: prev.customFields.map((field) =>
				field.id === fieldId
					? {
							...field,
							options: field.options?.filter(
								(opt) => opt.id !== optionId
							),
						}
					: field
			),
		}));
	};

	const handleAddNewField = () => {
		const newField: PatientField = {
			id: Date.now().toString(),
			label: "New Field",
			type: "text",
			isRequired: false,
			isValidator: false,
		};
		setSettings((prev) => ({
			...prev,
			systemFields: [...prev.systemFields, newField],
		}));
	};

	const handleSave = () => {
		onSave?.(settings);
		onOpenChange(false);
	};

	const handleClose = () => {
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full !max-w-[600px] p-0 [&>button]:hidden">
				<div className="flex h-full max-h-screen flex-col">
					<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
						<div className="flex h-14 items-start justify-start gap-2.5">
							<div className="flex flex-1 flex-col items-start justify-start gap-2">
								<div className="font-inter text-base leading-7 font-semibold text-gray-900">
									Edit Table
								</div>
								<div className="font-inter text-xs leading-4 font-normal text-gray-600">
									Select all the data points to collect from
									Patients. Set which fields are required and
									which two fields will be used as the
									validators from the list below.
								</div>
							</div>
							<div className="flex items-start justify-start gap-2.5">
								<Button
									variant="ghost"
									size="icon"
									onClick={handleClose}
									className="h-9 w-9 rounded-md"
								>
									<X className="h-4 w-4" />
								</Button>
							</div>
						</div>
					</div>
					<div className="min-h-0 flex-1 overflow-y-auto p-6 pt-6">
						<div className="flex flex-col gap-6">
							{settings.customFields.map((field) => (
								<div
									key={field.id}
									className="flex flex-col gap-3 border-t border-b border-gray-200 py-4"
								>
									<div className="flex items-center justify-between">
										<div className="flex w-64 flex-col gap-2">
											<Select
												value={field.type}
												onValueChange={(value) =>
													handleFieldUpdate(
														"customFields",
														field.id,
														{
															type:
																typeof value ===
																"string"
																	? value
																	: value[0],
														}
													)
												}
											>
												<SelectTrigger className="h-9 w-full text-xs">
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="dropdown">
														Dropdown
													</SelectItem>
													<SelectItem value="text">
														Text
													</SelectItem>
													<SelectItem value="number">
														Number
													</SelectItem>
													<SelectItem value="date">
														Date
													</SelectItem>
												</SelectContent>
											</Select>
										</div>
										<div className="flex w-28 items-center justify-center gap-1 py-2">
											<div className="flex items-center gap-1.5">
												<Switch
													checked={field.isRequired}
													onCheckedChange={(
														checked
													) =>
														handleFieldUpdate(
															"customFields",
															field.id,
															{
																isRequired:
																	checked,
															}
														)
													}
												/>
												<div className="text-xs font-normal text-gray-500">
													Required
												</div>
											</div>
										</div>
										<div className="flex w-28 items-center justify-center gap-1 py-2">
											<div className="flex items-center gap-1.5">
												<Switch
													checked={field.isValidator}
													onCheckedChange={(
														checked
													) =>
														handleFieldUpdate(
															"customFields",
															field.id,
															{
																isValidator:
																	checked,
															}
														)
													}
												/>
												<div className="text-xs font-normal text-gray-500">
													Validator
												</div>
											</div>
										</div>
									</div>
									<div className="flex flex-col gap-2">
										<div className="flex h-9 items-center gap-1 overflow-hidden rounded-md border border-gray-200 bg-white px-3 py-2">
											<Input
												value={field.label}
												onChange={(e) =>
													handleFieldUpdate(
														"customFields",
														field.id,
														{
															label: e.target
																.value,
														}
													)
												}
												className="font-inter h-auto flex-1 border-0 p-0 text-xs leading-none font-normal text-gray-900 shadow-none"
											/>
										</div>
									</div>
									{field.type === "dropdown" &&
										field.options && (
											<>
												{field.options.map(
													(option, index) => (
														<div
															key={option.id}
															className="flex w-full items-end gap-2"
														>
															<div className="flex flex-1 flex-col gap-2">
																<div className="font-inter text-xs leading-3 font-medium text-gray-900">
																	Dropdown
																	Option{" "}
																	{index + 1}
																</div>
																<div className="flex flex-col gap-2">
																	<div className="flex h-9 items-center gap-1 overflow-hidden rounded-md border border-gray-200 bg-white px-3 py-2">
																		<Input
																			value={
																				option.value
																			}
																			onChange={(
																				e
																			) => {
																				const updatedOptions =
																					field.options?.map(
																						(
																							opt
																						) =>
																							opt.id ===
																							option.id
																								? {
																										...opt,
																										value: e
																											.target
																											.value,
																									}
																								: opt
																					);
																				handleFieldUpdate(
																					"customFields",
																					field.id,
																					{
																						options:
																							updatedOptions,
																					}
																				);
																			}}
																			className="font-inter h-auto flex-1 justify-start border-0 p-0 text-xs leading-none font-normal text-gray-900 shadow-none"
																		/>
																	</div>
																</div>
															</div>
															<Button
																variant="secondary"
																size="sm"
																onClick={() =>
																	handleRemoveDropdownOption(
																		field.id,
																		option.id
																	)
																}
																className="flex h-6 w-6 items-center justify-center gap-2 rounded-md bg-gray-100 p-2"
															>
																<Trash2 className="h-3 w-3 text-gray-500" />
															</Button>
															<Button
																variant="secondary"
																size="sm"
																className="flex h-6 w-6 items-center justify-center gap-2 rounded-md bg-gray-100 p-2"
															>
																<Plus className="h-3 w-3 text-gray-500" />
															</Button>
														</div>
													)
												)}
											</>
										)}
								</div>
							))}
							<div className="flex flex-col gap-6">
								<div className="flex w-full items-center gap-6">
									<div className="font-inter text-base leading-none font-semibold text-gray-900">
										Table Titles for All Patients
									</div>
								</div>

								{settings.systemFields.map((field) => (
									<div
										key={field.id}
										className="flex flex-col gap-3 border-t border-b border-gray-200 py-4"
									>
										<div className="flex items-center gap-3">
											<div className="flex flex-1 flex-col gap-2">
												<div className="flex flex-col gap-2">
													<div className="flex h-9 items-center gap-1 overflow-hidden rounded-md border border-gray-200 bg-white px-3 py-2">
														<Input
															value={field.label}
															onChange={(e) =>
																handleFieldUpdate(
																	"systemFields",
																	field.id,
																	{
																		label: e
																			.target
																			.value,
																	}
																)
															}
															className="font-inter h-auto flex-1 border-0 p-0 text-xs leading-none font-normal text-gray-900 shadow-none"
														/>
													</div>
												</div>
											</div>
											<Button
												variant="secondary"
												size="sm"
												className="flex h-6 w-6 items-center justify-center gap-2 rounded-md bg-gray-100 p-2"
											>
												<Trash2 className="h-3 w-3 text-gray-500" />
											</Button>
										</div>
										<div className="flex items-center justify-between">
											<div className="flex w-64 flex-col gap-2">
												<Select
													value={field.type}
													onValueChange={(value) =>
														handleFieldUpdate(
															"systemFields",
															field.id,
															{
																type:
																	typeof value ===
																	"string"
																		? value
																		: value[0],
															}
														)
													}
												>
													<SelectTrigger className="h-9 w-full text-xs">
														<SelectValue />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="text">
															Text
														</SelectItem>
														<SelectItem value="number">
															Number
														</SelectItem>
														<SelectItem value="date">
															Date
														</SelectItem>
														<SelectItem value="dropdown">
															Dropdown
														</SelectItem>
													</SelectContent>
												</Select>
											</div>
											<div className="flex w-28 items-center justify-center gap-1 py-2">
												<div className="flex items-center gap-1.5">
													<Switch
														checked={
															field.isRequired
														}
														onCheckedChange={(
															checked
														) =>
															handleFieldUpdate(
																"systemFields",
																field.id,
																{
																	isRequired:
																		checked,
																}
															)
														}
													/>
													<div className="text-xs font-normal text-gray-500">
														Required
													</div>
												</div>
											</div>
											<div className="flex w-28 items-center justify-center gap-1 py-2">
												<div className="flex items-center gap-1.5">
													<Switch
														checked={
															field.isValidator
														}
														onCheckedChange={(
															checked
														) =>
															handleFieldUpdate(
																"systemFields",
																field.id,
																{
																	isValidator:
																		checked,
																}
															)
														}
													/>
													<div className="text-xs font-normal text-gray-500">
														Validator
													</div>
												</div>
											</div>
										</div>
									</div>
								))}
								<div className="flex h-6 items-center justify-end py-0.5">
									<div className="flex items-center gap-3">
										<button
											onClick={handleAddNewField}
											className="flex items-center justify-center overflow-hidden rounded-[1px]"
										>
											<Plus className="h-5 w-5 text-slate-900" />
										</button>
										<div className="font-inter text-xs leading-none font-normal text-slate-900">
											Add New Title
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="flex-shrink-0 bg-white p-6">
						<div className="flex items-center justify-end gap-3">
							<Button
								variant="outline"
								onClick={handleClose}
								className="h-9 min-w-32"
							>
								Cancel
							</Button>
							<Button
								onClick={handleSave}
								className="h-9 min-w-32 bg-[#005893]"
							>
								Edit
							</Button>
						</div>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
