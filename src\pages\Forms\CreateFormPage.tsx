import { useEffect, useState, type <PERSON> } from "react";
import { useUIStore } from "@/stores/uiStore";
import { <PERSON>an<PERSON>ye, Plus, Search, Settings2, Upload } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import {
	createField,
	formBuilderSchema,
	generateUUID,
	type FormDataType,
} from "./types";
import type {
	FieldType,
	FormSection,
	FormTypes,
	GetFormsResponse,
} from "./types";
import { SendFormLinkSheet } from "./sheets";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { LuPlus, LuMinus } from "react-icons/lu";
import { FieldControl, FormFlowSelect, DraggableFormField } from "./components";
import { X } from "lucide-react";
import Loader from "@/components/Loader";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	DndContext,
	DragOverlay,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	sortableKeyboardCoordinates,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Uploader } from "@/components/common/Uploader";

const servicesData = {
	data: [
		{ id: 1, name: "Service 1" },
		{ id: 2, name: "Service 2" },
	],
};

const UploadImageSlice = () => {
	return {
		mutate: () => {},
		isLoading: false,
	};
};

export const CreateFormPage: FC = () => {
	const [activeSection, setActiveSection] = useState<string | null>(null);
	const [activeDragId, setActiveDragId] = useState<string | null>(null);
	const [isLogoUploading, setIsLogoUploading] = useState(false);
	const [isBannerUploading, setIsBannerUploading] = useState(false);
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);

	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		})
	);

	const { mutate: uploadImage, isLoading: isUploading } = UploadImageSlice();

	const [showAddServiceForm, setShowAddServiceForm] = useState(false);
	const [showSendFormLinkSheet, setShowSendFormLinkSheet] = useState(false);

	const form = useForm<FormDataType>({
		resolver: zodResolver(formBuilderSchema as any),
		defaultValues: {
			name: "",
			logo: null,
			banner: null,
			success_message: "We have received your form.",
			block_message: "Access to this form has been restricted.",
			submit_button_title: "Submit",
			service_id: null,
			status: "draft",
			sections: [
				{
					id: crypto.randomUUID(),
					title: "",
					description: null,
					fields: [
						{
							id: crypto.randomUUID(),
							title: "",
							type: "text",
							required: false,
							options: [],
							description: null,
							image: null,
							info_text_value: null,
							approved_formats: [],
						},
					],
					flow: {
						action: "submit",
						targetSection: undefined as string | undefined,
					},
				},
			],
			apply_to_option: {
				apply_to_all: 0,
				locations: [],
			},
		},
		mode: "onChange",
	});

	const {
		fields: sections,
		append: appendSection,
		remove: removeSection,
	} = useFieldArray({
		control: form.control,
		name: "sections",
	});

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Forms",
				href: "/dashboard/forms",
			},
			{
				label: "Create a Form",
				href: "/dashboard/forms/create",
			},
		]);

		setCurrentPageTitle("Form Manager");

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, setCurrentPageTitle]);

	const addField = (sectionIndex: number, type: FieldType) => {
		const currentFields = form.getValues(`sections.${sectionIndex}.fields`);
		const newField = createField(type);

		form.setValue(
			`sections.${sectionIndex}.fields`,
			[...currentFields, newField] as any,
			{
				shouldValidate: false,
				shouldDirty: true,
				shouldTouch: false,
			}
		);
	};

	const addSection = () => {
		const newSectionId = generateUUID();

		const newSection: FormSection = {
			id: newSectionId,
			title: "",
			description: null,
			fields: [
				{
					id: generateUUID(),
					title: "",
					type: "text" as FieldType,
					required: false,
					options: [],
					description: null,
					image: null,
					info_text_value: null,
					approved_formats: [],
				},
			],
			flow: {
				action: "submit",
				targetSection: undefined,
			},
		};

		const currentSections = form.getValues("sections");
		if (currentSections.length > 0) {
			form.setValue(`sections.${currentSections.length - 1}.flow`, {
				action: "continue",
				targetSection: newSectionId,
			});
		}

		appendSection(newSection);
	};

	const moveField = (
		fromSectionIndex: number,
		fromFieldIndex: number,
		toSectionIndex: number,
		toFieldIndex: number
	) => {
		const sections = form.getValues("sections");
		const fromSection = sections[fromSectionIndex];
		const [movedField] = fromSection.fields.splice(fromFieldIndex, 1);
		const toSection = sections[toSectionIndex];
		toSection.fields.splice(toFieldIndex, 0, movedField);

		form.setValue("sections", sections, {
			shouldValidate: true,
			shouldDirty: true,
			shouldTouch: true,
		});
	};

	const handleDeleteSection = (sectionId: string) => {
		const sectionIndex = sections.findIndex(
			(section) => section.id === sectionId
		);

		if (sectionIndex !== -1) {
			const formSections = form.getValues("sections");

			formSections.forEach((section, sIdx) => {
				section.fields.forEach((field: any, fIdx) => {
					if (field.options?.length) {
						const needsUpdate = field.options.some(
							(opt: any) =>
								opt.conditions?.destination === sectionId
						);

						if (needsUpdate) {
							field.options.forEach(
								(option: any, optIdx: number) => {
									if (
										option.conditions?.destination ===
										sectionId
									) {
										form.setValue(
											`sections.${sIdx}.fields.${fIdx}.options.${optIdx}.conditions` as any,
											{
												type: "continue",
												destination: "next",
												logic: option.conditions.logic,
												selected:
													option.conditions.selected,
												conditional_block_message:
													option.conditions
														.conditional_block_message,
											}
										);
									}
								}
							);
						}
					}
				});
			});

			removeSection(sectionIndex);
		}
	};

	const handleDragStart = (event: any) => {
		setActiveDragId(event.active.id);
	};

	const handleDragEnd = (event: any) => {
		const { active, over } = event;
		setActiveDragId(null);

		if (!over) return;

		if (active.id !== over.id) {
			const activeData = active.data.current;
			const overData = over.data.current;

			moveField(
				activeData.sectionIndex,
				activeData.fieldIndex,
				overData.sectionIndex,
				overData.fieldIndex
			);
		}
	};

	const onSubmit = (data: FormDataType) => {
		console.log(data);
	};

	const handleImageUpload = (
		event: React.ChangeEvent<HTMLInputElement>,
		onChange: (value: string | null) => void,
		fieldName: string
	) => {
		const file = event.target.files?.[0];
		if (!file) return;

		const MAX_FILE_SIZE_MB = 5;
		const ACCEPTED_FORMATS = ["jpg", "jpeg", "png", "gif", "svg"];
		const fileFormat = file.type.split("/")[1];

		if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
			// toast.error(`Max file size is ${MAX_FILE_SIZE_MB}MB`);
			return;
		}

		if (!ACCEPTED_FORMATS.includes(fileFormat)) {
			// toast.error(`${fileFormat.toUpperCase()} is not supported`);
			return;
		}

		if (fieldName === "logo") {
			setIsLogoUploading(true);
		} else if (fieldName === "banner") {
			setIsBannerUploading(true);
		}

		const formData = new FormData();
		formData.append("file", file);

		uploadImage();

		// uploadImage(formData, {
		// 	// onSuccess: (response: any) => {
		// 	// 	onChange(response.data.image_url);
		// 	// },
		// 	// onSettled: () => {
		// 	// 	if (fieldName === "logo") {
		// 	// 		setIsLogoUploading(false);
		// 	// 	} else if (fieldName === "banner") {
		// 	// 		setIsBannerUploading(false);
		// 	// 	}
		// 	// },
		// });
	};

	const [descriptionVisibility, setDescriptionVisibility] = useState<{
		[key: string]: boolean;
	}>(
		sections.reduce(
			(acc, section) => {
				acc[section.id] = section.description !== null;
				return acc;
			},
			{} as { [key: string]: boolean }
		)
	);

	const handleToggleDescription = (sectionId: string) => {
		setDescriptionVisibility((prev) => ({
			...prev,
			[sectionId]: !prev[sectionId],
		}));
	};

	return (
		<DndContext
			sensors={sensors}
			collisionDetection={closestCenter}
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
		>
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="space-y-6"
				>
					<div className="flex flex-col gap-4 py-6">
						{/* Header */}
						<div className="flex items-center justify-between pl-4">
							<h1 className="text-2xl font-semibold">
								Create a Form
							</h1>
							<div className="flex items-center gap-3">
								<Button
									variant="outline"
									className="bg-primary hover:bg-primary/90 h-10 cursor-pointer text-white hover:text-white"
									onClick={() => setShowAddServiceForm(true)}
								>
									<ScanEye className="mr-2 h-4 w-4" />
									Preview
								</Button>
							</div>
						</div>

						{/* Form Builder */}
						<div className="grid h-screen max-h-screen w-full max-w-screen grid-cols-[400px_1fr] gap-x-7 divide-x">
							<div className="flex w-full flex-col space-y-6 pr-7.5 pl-4">
								<FormField
									control={form.control}
									name="service_id"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-base font-semibold">
												Form Type{" "}
											</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value || ""}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="no_service">
														No service
													</SelectItem>
													{servicesData?.data.map(
														(service: any) => (
															<SelectItem
																key={service.id}
																value={service.id.toString()}
															>
																{service.name}
															</SelectItem>
														)
													)}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="service_id"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-base font-semibold">
												Services{" "}
											</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value || ""}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="no_service">
														No service
													</SelectItem>
													{servicesData?.data.map(
														(service: any) => (
															<SelectItem
																key={service.id}
																value={service.id.toString()}
															>
																{service.name}
															</SelectItem>
														)
													)}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="service_id"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-base font-semibold">
												Apply To{" "}
											</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value || ""}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="no_service">
														No service
													</SelectItem>
													{servicesData?.data.map(
														(service: any) => (
															<SelectItem
																key={service.id}
																value={service.id.toString()}
															>
																{service.name}
															</SelectItem>
														)
													)}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="w-full max-w-[727px] space-y-3.5">
								<Card className="pt-3 pb-7">
									<CardContent className="space-y-4 px-3">
										<FormField
											control={form.control}
											name="name"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-base font-semibold">
														Form Name{" "}
														<span className="text-red-500">
															*
														</span>
													</FormLabel>
													<FormControl>
														<Input
															placeholder="Form Name"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</CardContent>
								</Card>
								<Card className="pt-3 pb-7">
									<CardContent className="space-y-4 px-3">
										<FormField
											control={form.control}
											name="banner"
											render={({
												field: {
													value,
													onChange,
													...field
												},
											}) => (
												<FormItem>
													<FormLabel className="text-base font-semibold">
														Upload Banner
													</FormLabel>
													<FormControl>
														<div className="flex flex-col gap-2">
															<Uploader
																files={[]}
																onFilesChange={() => {}}
																onFileRemove={() => {}}
																onFileEdit={() => {}}
																descriptionText="Recommended file type: .svg, .png, .jpg (Max of 8 mb)"
																accept=".svg,.png,.jpg,.jpeg"
																maxFileSize={
																	8 *
																	1024 *
																	1024
																}
																multiple={false}
																maxFiles={1}
																size="default"
																uploadText="Click or drag file here to upload file"
																uploadIcon={
																	<Upload className="h-4 w-4 text-black" />
																}
															/>
														</div>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</CardContent>
								</Card>
								<Card className="pt-3 pb-7">
									<CardContent className="space-y-4 px-3">
										<FormField
											control={form.control}
											name="logo"
											render={({
												field: {
													value,
													onChange,
													...field
												},
											}) => (
												<FormItem>
													<FormLabel className="text-base font-semibold">
														Upload Logo
													</FormLabel>
													<FormControl>
														<div className="flex flex-col gap-2">
															<Uploader
																files={[]}
																onFilesChange={() => {}}
																onFileRemove={() => {}}
																onFileEdit={() => {}}
																descriptionText="Recommended file type: .svg, .png, .jpg (Max of 8 mb)"
																accept=".svg,.png,.jpg,.jpeg"
																maxFileSize={
																	8 *
																	1024 *
																	1024
																}
																multiple={false}
																maxFiles={1}
																size="default"
																uploadText="Click or drag file here to upload file"
																uploadIcon={
																	<Upload className="h-4 w-4 text-black" />
																}
															/>
														</div>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</CardContent>
								</Card>

								{sections.map((section, sectionIndex) => {
									const fields = form.watch(
										`sections.${sectionIndex}.fields`
									);
									const hasMultipleFields =
										fields?.length > 1;
									const lastFieldIndex = fields?.length - 1;
									const sortableFields = fields.map(
										(field) => field.id
									);

									return (
										<div
											key={section?.id}
											className="relative space-y-4"
											onMouseEnter={() =>
												setActiveSection(section?.id)
											}
										>
											<Card className="border-t-primary border-t-4 py-0">
												<CardContent className="space-y-6 p-4">
													<div className="flex items-center justify-between">
														<h2 className="text[#0F172A] text-lg font-semibold">
															Section{" "}
															{sectionIndex + 1}
														</h2>
														<Button
															type="button"
															variant="outline"
															onClick={() =>
																handleDeleteSection(
																	section?.id
																)
															}
														>
															<i className="mgc_delete_2_line before:!text-destructive" />
														</Button>
													</div>
													<div>
														<FormField
															control={
																form.control
															}
															name={`sections.${sectionIndex}.title`}
															render={({
																field,
															}) => (
																<FormItem>
																	<FormLabel className="text-base font-semibold">
																		Section
																		Title{" "}
																		<span className="text-red-500">
																			*
																		</span>
																	</FormLabel>
																	<FormControl>
																		<Input
																			placeholder={`Section ${sectionIndex + 1} Title`}
																			{...field}
																		/>
																	</FormControl>
																	<FormMessage />
																</FormItem>
															)}
														/>
														{!descriptionVisibility[
															section.id
														] && (
															<button
																type="button"
																className="mt-4 ml-5 flex items-center gap-2 text-sm font-medium text-[#323539]"
																onClick={() =>
																	handleToggleDescription(
																		section.id
																	)
																}
															>
																<LuPlus
																	color="#fff"
																	className="bg-primary rounded-full"
																	size={12}
																/>
																Add Description
															</button>
														)}
														{descriptionVisibility[
															section.id
														] && (
															<FormField
																control={
																	form.control
																}
																name={`sections.${sectionIndex}.description`}
																render={({
																	field,
																}) => (
																	<FormItem className="mt-4">
																		<FormLabel>
																			Description
																		</FormLabel>
																		<FormControl>
																			<Textarea
																				{...field}
																				placeholder="Add a description for this field"
																				className="mt-2"
																				value={
																					field.value ||
																					""
																				}
																			/>
																		</FormControl>
																		<FormMessage />
																	</FormItem>
																)}
															/>
														)}
														{descriptionVisibility[
															section.id
														] && (
															<button
																type="button"
																className="!mt-1 ml-5 flex items-center gap-2 text-sm font-medium text-[#323539]"
																onClick={() =>
																	handleToggleDescription(
																		section.id
																	)
																}
															>
																<LuMinus
																	color="#fff"
																	className="rounded-full bg-red-600"
																	size={12}
																/>
																Hide Description
															</button>
														)}
													</div>
												</CardContent>
											</Card>

											<SortableContext
												items={sortableFields}
												strategy={
													verticalListSortingStrategy
												}
											>
												{form
													.watch(
														`sections.${sectionIndex}.fields`
													)
													?.map(
														(field, fieldIndex) => (
															<FormField
																key={field.id}
																control={
																	form.control
																}
																name={`sections.${sectionIndex}.fields.${fieldIndex}`}
																render={({
																	field: fieldProps,
																}) => (
																	<DraggableFormField
																		sectionIndex={
																			sectionIndex
																		}
																		fieldIndex={
																			fieldIndex
																		}
																		field={{
																			...fieldProps,
																			name: fieldIndex,
																		}}
																		control={
																			form.control
																		}
																		watch={
																			form.watch
																		}
																		setValue={
																			form.setValue
																		}
																		getValues={
																			form.getValues
																		}
																		clearErrors={
																			form.clearErrors
																		}
																		moveField={
																			moveField
																		}
																		hasMultipleFields={
																			hasMultipleFields
																		}
																		isLastField={
																			fieldIndex ===
																			lastFieldIndex
																		}
																		activeSection={
																			activeSection
																		}
																		onAddField={
																			addField
																		}
																		onAddSection={
																			addSection
																		}
																		sectionId={
																			section.id
																		}
																		form={
																			form
																		}
																	/>
																)}
															/>
														)
													)}
											</SortableContext>

											{!hasMultipleFields && (
												<FieldControl
													sectionIndex={sectionIndex}
													onAddField={addField}
													onAddSection={addSection}
													isVisible={
														activeSection ===
														section?.id
													}
													position="section"
												/>
											)}

											<div className="mt-4">
												<FormFlowSelect
													control={form.control}
													sectionIndex={sectionIndex}
													sections={form.getValues(
														"sections"
													)}
													currentValue={form.watch(
														`sections.${sectionIndex}.flow`
													)}
													onValueChange={(
														value: any
													) => {
														form.setValue(
															`sections.${sectionIndex}.flow`,
															value,
															{
																shouldValidate: true,
															}
														);
													}}
												/>
											</div>
										</div>
									);
								})}

								<Card className="p-0">
									<CardContent className="space-y-4 p-4">
										<h2 className="!mb-2 text-base leading-6 font-semibold">
											Submission{" "}
										</h2>
										<div className="rounded-md border border-gray-200 p-4 pt-4">
											<FormField
												control={form.control}
												name="success_message"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-base font-normal italic">
															Success Message on
															Submission{" "}
															<span className="text-red-500">
																*
															</span>
														</FormLabel>
														<FormControl>
															<Input
																placeholder={`"Enter here"`}
																className="bg-gray-100 shadow-sm"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
										<div className="rounded-md border border-gray-200 p-4 pt-4">
											<FormField
												control={form.control}
												name="block_message"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-base font-normal italic">
															Block Message on
															Submission{" "}
														</FormLabel>
														<FormControl>
															<Input
																placeholder={`"Enter here"`}
																className="bg-gray-100 shadow-sm"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
										<div className="border-t border-gray-200 pt-2">
											<FormField
												control={form.control}
												name="submit_button_title"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-xs font-normal italic">
															Submission
															Successful Button
															Title{" "}
															<span className="text-red-500">
																*
															</span>
														</FormLabel>
														<FormControl>
															<Input
																placeholder="Button title"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
									</CardContent>
								</Card>

								<div className="flex items-center justify-end space-x-2">
									<Button
										type="submit"
										variant={"outline"}
										onClick={() =>
											form.setValue("status", "live")
										}
									>
										{form.watch("status") === "live" &&
										isUploading ? (
											<Loader size={20} />
										) : (
											"Publish"
										)}
									</Button>
									<Button
										type="submit"
										onClick={() =>
											form.setValue("status", "draft")
										}
									>
										{form.watch("status") === "draft" &&
										isUploading ? (
											<Loader size={20} />
										) : (
											"Save as Draft"
										)}
									</Button>
								</div>
							</div>
						</div>

						{/* Send Form Link Sheet */}
						<SendFormLinkSheet
							open={showSendFormLinkSheet}
							onOpenChange={setShowSendFormLinkSheet}
						/>
					</div>
				</form>
			</Form>
		</DndContext>
	);
};
