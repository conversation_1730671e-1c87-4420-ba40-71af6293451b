import { FaAngleLeft, FaAngleRight } from "react-icons/fa6";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { IoCalendar } from "react-icons/io5";
import { Button } from "@/components/ui/button";
import clsx from "clsx";
import { getFormattedHeader, VIEW_OPTIONS } from "../utils";
import { Calendar as ShadCalendarUi } from "@/components/ui/calendar"
import CalendarSvg from "./calendar-svg";
import { Tabs, TabsContent } from "@/components/common/Tabs";

type CustomToolbarProps = {
    onPrevClick: () => void;
    onNextClick: () => void;
    date: Date;
    view: string;
    setDate: (date: Date) => void;
    setView: (view: string) => void;
    open: boolean;
    setOpen: (open: boolean) => void;
}
export default function CustomToolbar({
    onPrevClick,
    onNextClick,
    date,
    view,
    setDate,
    setView,
    open,
    setOpen
}: CustomToolbarProps) {

    const tabs = [
        { value: VIEW_OPTIONS[0].id, label: "Day" },
        { value: VIEW_OPTIONS[1].id, label: "Week" },
        { value: VIEW_OPTIONS[2].id, label: "Month" },
    ]

    return (
        // Header
        <div className="flex gap-x-20 mb-5">

            <div className="flex items-center w-full">
                <Button variant="outline" className="w-9 cursor-pointer rounded-lg border-none shadow-none hover:bg-transparent" onClick={onPrevClick}>
                    <FaAngleLeft />
                </Button>
                <p className="flex-1 flex items-center justify-center text-center gap-x-2 font-semibold">
                    {getFormattedHeader(date, view)}
                    <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                            <span
                                className="bg-[#F4F4F5] ml-1 p-2 rounded-md"
                            >
                                <CalendarSvg />
                            </span>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto overflow-hidden p-0" align="start">
                            <ShadCalendarUi
                                mode="single"
                                selected={date}
                                captionLayout="dropdown"
                                onSelect={(date: Date | undefined) => {
                                    if (!date) return;
                                    setDate(date)
                                    setOpen(false)
                                }}
                            />
                        </PopoverContent>
                    </Popover>
                </p>
                <Button variant="outline" className="w-9 cursor-pointer rounded-lg border-none shadow-none hover:bg-transparent" onClick={onNextClick}>
                    <FaAngleRight />
                </Button>
            </div>

            {/* View Tabs */}
            <Tabs
                items={tabs}
                useRouting={true}
                searchParamKey="view-type"
                defaultTab={VIEW_OPTIONS[0].id}
                onValueChange={(value) => setView(value)}
                className="w-full"
                triggerClassName="bg-[#F4F4F5] hover:bg-[#F4F4F5] data-[state=active]:bg-[#FFF] data-[state=active]:text-[#18181B] text-[#A1A1AA]"
            />

        </div>
    );
};