import React, { createContext, useContext, useEffect, useState } from "react";
import type { ReactNode } from "react";
import { organizationsApi } from "../api/organizationsApi";
import type { Organization } from "../api/organizationsApi";
import { useAuthStore } from "@/stores/authStore";
import useUserStore from "@/stores/useUserStore";
import Loader from "@/components/Loader";
import { useQueryClient } from "@tanstack/react-query";

const ORG_ID_KEY = "current_organization_id";

interface OrganizationContextType {
	organization: Organization | null;
	organizationId: number | null;
	organizations: Organization[];
	isLoading: boolean;
	setCurrentOrganization: (org: Organization) => Promise<void>;
	addOrganization: (org: Omit<Organization, "id">) => Promise<void>;
	refetch: () => Promise<void>;
	invalidateQueries: () => Promise<void>;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(
	undefined
);

export const OrganizationProvider = ({ children }: { children: ReactNode }) => {
	const user = useUserStore((s) => s.user);
	const isAuthenticated = useAuthStore((s) => s.isAuthenticated);
	const queryClient = useQueryClient();
	const [organizations, setOrganizations] = useState<Organization[]>([]);
	const [organization, setOrganization] = useState<Organization | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	const invalidateOrganizationQueries = async (organizationId: number) => {
		console.log(
			`Invalidating queries for organization ID: ${organizationId}`
		);
		console.log(
			'Note: Organization ID is sent as "x-organizationId" header in requests'
		);

		try {
			// Invalidate all queries that depend on organization ID
			// Since organization ID is sent as a header (x-organizationId),
			// we need to invalidate all organization-dependent queries
			await Promise.all([
				// Services queries
				queryClient.invalidateQueries({
					queryKey: ["services"],
					exact: false,
				}),
				// Locations queries
				queryClient.invalidateQueries({
					queryKey: ["locations"],
					exact: false,
				}),
				// Stations queries
				queryClient.invalidateQueries({
					queryKey: ["stations"],
					exact: false,
				}),
				// All stations queries
				queryClient.invalidateQueries({
					queryKey: ["all-stations"],
					exact: false,
				}),
				// Members queries
				queryClient.invalidateQueries({
					queryKey: ["members"],
					exact: false,
				}),
				// Appointment methods queries
				queryClient.invalidateQueries({
					queryKey: ["appointment-methods"],
					exact: false,
				}),
				// Provider details queries
				queryClient.invalidateQueries({
					queryKey: ["provider-details"],
					exact: false,
				}),
				// Clients queries
				queryClient.invalidateQueries({
					queryKey: ["clients"],
					exact: false,
				}),
				// Categories queries
				queryClient.invalidateQueries({
					queryKey: ["categories"],
					exact: false,
				}),
				// Organization-specific queries with broader pattern matching
				queryClient.invalidateQueries({
					predicate: (query) => {
						// Invalidate any query that has the organization ID in its key
						const hasOrgInKey = query.queryKey.some(
							(key) =>
								(typeof key === "string" &&
									key === organizationId.toString()) ||
								(typeof key === "number" &&
									key === organizationId) ||
								// Check if the query key includes organization-related patterns
								(typeof key === "string" &&
									(key.includes("organization") ||
										key.includes("org")))
						);

						// Also invalidate queries that typically depend on organization context
						// These are queries that would use the x-organizationId header
						const isOrgDependentQuery = query.queryKey.some(
							(key) =>
								typeof key === "string" &&
								(key.includes("services") ||
									key.includes("locations") ||
									key.includes("stations") ||
									key.includes("members") ||
									key.includes("appointment-methods") ||
									key.includes("provider") ||
									key.includes("teams") ||
									key.includes("schedules") ||
									key.includes("bookings") ||
									key.includes("clients") ||
									key.includes("categories"))
						);

						return hasOrgInKey || isOrgDependentQuery;
					},
				}),
			]);

			console.log(
				`Successfully invalidated queries for organization ID: ${organizationId}`
			);
		} catch (error) {
			console.error(
				`Error invalidating queries for organization ID: ${organizationId}`,
				error
			);
		}
	};

	const fetchOrganizations = async () => {
		setIsLoading(true);
		try {
			const orgs = await organizationsApi.getOrganizations();
			setOrganizations(orgs);

			let savedOrgId = localStorage.getItem(ORG_ID_KEY);
			let currentOrg = orgs.find((o) => o.id === parseInt(savedOrgId!));

			if (!currentOrg && orgs.length > 0) {
				currentOrg = orgs[0];
				localStorage.setItem(
					ORG_ID_KEY,
					JSON.stringify(currentOrg.id!)
				);
			}

			setOrganization(currentOrg ?? null);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		console.log(isAuthenticated, "here");
		if (isAuthenticated) {
			fetchOrganizations();
		} else {
			setIsLoading(false);
		}
	}, [user]);

	// Effect to handle organization changes and ensure data consistency
	useEffect(() => {
		if (organization?.id) {
			console.log(
				`Organization changed to: ${organization.name} (ID: ${organization.id})`
			);
			// Additional logic can be added here if needed for organization change handling
		}
	}, [organization?.id]);

	const setCurrentOrganization = async (org: Organization) => {
		const previousOrgId = organization?.id;

		// Update localStorage first to ensure the API client interceptor picks up the new org ID
		localStorage.setItem(ORG_ID_KEY, JSON.stringify(org.id));

		// Update state
		setOrganization(org);

		// If organization changed, invalidate all organization-dependent queries
		if (previousOrgId !== org.id && org.id) {
			// Small delay to ensure localStorage is updated and available to API client
			await new Promise((resolve) => setTimeout(resolve, 50));
			await invalidateOrganizationQueries(org.id);
		}
	};

	const addOrganization = async (org: Omit<Organization, "id">) => {
		setIsLoading(true);
		try {
			const newOrg = await organizationsApi.createOrganization(
				org as Organization
			);
			await fetchOrganizations();
			await setCurrentOrganization(newOrg);
		} finally {
			setIsLoading(false);
		}
	};

	if (!organization?.id && isLoading) {
		return <Loader />;
	}

	return (
		<OrganizationContext.Provider
			value={{
				organization,
				organizationId: organization?.id ?? null,
				organizations,
				isLoading,
				setCurrentOrganization,
				addOrganization,
				refetch: fetchOrganizations,
				invalidateQueries: () =>
					invalidateOrganizationQueries(organization?.id || 0),
			}}
		>
			{children}
		</OrganizationContext.Provider>
	);
};

export function useOrganizationContext() {
	const ctx = useContext(OrganizationContext);
	if (!ctx)
		throw new Error(
			"useOrganizationContext must be used within OrganizationProvider"
		);
	return ctx;
}
