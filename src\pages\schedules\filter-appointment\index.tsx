import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
    Sheet,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON>D<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"
import { zodResolver } from "@hookform/resolvers/zod"
import { LuSettings2 } from "react-icons/lu"
import { filterSchema } from "../schema/filter"
import type { z } from "zod"
import { useForm } from "react-hook-form"
import { FiUser } from "react-icons/fi";
import { GoCheckCircleFill } from "react-icons/go";
import { PiSpeakerHighThin } from "react-icons/pi";
import { MdOutlineVideocam } from "react-icons/md";
import { DatePicker } from "@/components/common/Datepicker/DatePicker"
import { RefactorMultiSelect } from "../components/custom-select"
import { locations, providers, services } from "../db"
import { ToggleButton } from "@/components/common/ToggleButton"
import { useState } from "react"

export default function FilterAppointment() {
    const form = useForm<z.infer<typeof filterSchema>>({
        resolver: zodResolver(filterSchema),
        defaultValues: {
            location: [],
            providers: [],
            services: [],
            appointmentType: "in-person",
            dateRange: "",
        }
    })

    const meetingTypes = [
        { id: "in-person", label: "In-Person", icon: FiUser },
        { id: "audio", label: "Audio", icon: PiSpeakerHighThin },
        { id: "video", label: "Video", icon: MdOutlineVideocam },
    ];

    const [selectedTypes, setSelectedTypes] = useState<string[]>([]);

    const toggleSelection = (type: string) => {
        setSelectedTypes((prev) =>
            prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
        );
    };

    return (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="outline" className="py-5 cursor-pointer">
                    <LuSettings2 />
                </Button>
            </SheetTrigger>
            <SheetContent className="z-[1003] py-5 px-2 sm:max-w-[610px]">
                <SheetHeader>
                    <SheetTitle className="text-2xl mb-1">Filter Appointments</SheetTitle>
                    <SheetDescription>Select options below to help filter your search</SheetDescription>
                </SheetHeader>
                <div className="px-4 space-y-7">

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="location" className="text-[#18181B] text-base">Location</Label>

                        <RefactorMultiSelect
                            value={form.watch("location")}
                            setValue={(value) => form.setValue("location", value as string[])}
                            placeholder="Select Location"
                            label="Location"
                            id="location"
                            options={locations}
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="location" className="text-[#18181B] text-base">Providers</Label>

                        <RefactorMultiSelect
                            value={form.watch("providers")}
                            setValue={(value) => form.setValue("providers", value as string[])}
                            placeholder="Select Providers"
                            label="Providers"
                            id="providers"
                            options={providers}
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="location" className="text-[#18181B] text-base">Services</Label>

                        <RefactorMultiSelect
                            value={form.watch("services")}
                            setValue={(value) => form.setValue("services", value as string[])}
                            placeholder="Select Services"
                            label="Services"

                            options={services}
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="appointmentType" className="text-[#18181B] text-base">Appointment Method</Label>

                        <div className="flex flex-wrap gap-2">
                            {meetingTypes.map((type) => (
                                <ToggleButton
                                    key={type.id}
                                    label={type.label}
                                    icon={type.icon as any}
                                    isSelected={selectedTypes.includes(type.id)}
                                    onClick={() => toggleSelection(type.id)}
                                    className="py-5"
                                />
                            ))}
                        </div>

                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="dateRange" className="text-[#18181B] text-base">
                            Select a Date or Range
                        </Label>

                        <div className="w-full">
                            <style>
                                {`
                                div[data-radix-popper-content-wrapper] {
                                    z-index: 3000 !important;
                                }
                                `}
                            </style>
                            <DatePicker
                                onChange={() => { }}
                                placeholder="Pick a date"
                                size="md"
                                variant="default"
                            />
                        </div>
                    </div>
                </div>
                <SheetFooter>
                    <div className="flex items-center justify-between">
                        <Button
                            variant={"ghost"}
                            className="cursor-pointer opacity-60 font-medium"
                            type="button"
                            onClick={() => form.reset()}
                        >
                            Reset
                        </Button>
                        <div className="space-x-4">
                            <Button variant="outline" className="cursor-pointer py-5">
                                Cancel
                            </Button>
                            <Button className="cursor-pointer py-5">
                                Apply
                            </Button>
                        </div>
                    </div>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    )
}