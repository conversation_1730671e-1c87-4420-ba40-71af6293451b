import React from "react";
import {
	MapPin,
	Users,
	Wand2,
	SquarePen,
	QrCode,
	ChevronRight,
	Image,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import type { Location } from "../types";

export interface LocationCardProps {
	location: Location;
	onEdit?: (location: Location) => void;
	onView?: (location: Location) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
}

export const LocationCard: React.FC<LocationCardProps> = ({
	location,
	onEdit,
	onView,
	isSelected = false,
	onSelectionChange,
}) => {
	const providersCount = location.stations.length; // TODO: Get actual providers count
	const servicesCount = location?.service_count || 0;

	return (
		<div
			className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start rounded-lg border border-zinc-200 bg-white"
			onClick={() => onView?.(location)}
		>
			{/* Checkbox Section */}
			<div
				className="flex h-16 items-center px-4"
				onClick={(e) => e.stopPropagation()}
			>
				<Checkbox
					checked={isSelected}
					onCheckedChange={onSelectionChange}
					className="border-[#005893]"
				/>
			</div>

			{/* Location Info Section */}
			<div className="flex min-w-[435px] flex-1 items-center px-3">
				<div className="flex items-center gap-3">
					{/* Location Icon */}
					<div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[rgba(9,36,75,0.04)]">
						<Image size={14} />
					</div>

					{/* Location Details */}
					<div className="flex flex-1 flex-col gap-0.5">
						<div className="text-base leading-6 font-medium">
							{location.name}
						</div>
						<div className="flex items-center gap-1">
							<MapPin className="h-3.5 w-3.5 text-zinc-600" />
							<span className="text-xs leading-4 text-zinc-600">
								{location.address}
							</span>
						</div>
					</div>
				</div>
			</div>

			{/* Providers Section */}
			<div className="flex h-16 w-[85px] min-w-[85px] flex-2 items-center px-3">
				<div className="flex items-center gap-2.5">
					<Users className="h-4 w-4 text-zinc-600" />
					<span className="text-sm leading-5 text-zinc-600">
						Providers
					</span>
					<span className="flex-1 text-sm leading-5 font-semibold text-zinc-800">
						{providersCount}
					</span>
				</div>
			</div>

			{/* Services Section */}
			<div className="flex h-16 w-[85px] min-w-[85px] flex-2 items-center px-3">
				<div className="flex items-center gap-2.5">
					<Wand2 className="h-4 w-4 text-zinc-600" />
					<span className="text-sm leading-5 text-zinc-600">
						Services
					</span>
					<span className="flex-1 text-sm leading-5 font-semibold text-zinc-800">
						{servicesCount}
					</span>
				</div>
			</div>

			{/* Actions Section */}
			<div className="flex h-16 min-w-[72px] items-center justify-end px-3">
				<div className="flex items-center gap-2.5">
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onEdit?.(location);
						}}
					>
						<SquarePen className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							console.log("QR Code action for:", location.name);
						}}
					>
						<QrCode className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(location);
						}}
					>
						<ChevronRight className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
};
