import { useQuery } from "@tanstack/react-query";
import * as HttpQuery from "../../http";
import * as LocationTypes from "../../types/location";

/**
 * Get availability count
 * @param params - Query parameters
 * @returns Availability count
 */

export function useGetAvailabilityCount(
    params: LocationTypes.AvailabilityCountQueryParams
) {
    return useQuery({
        queryKey: ['availability-count', params],
        queryFn: () => HttpQuery.APIVersion3GetAvailabilityCount(params),
    })
}

/**
 * Get time ranges
 * @param params - Query parameters
 * @returns Time ranges
 */

export function useGetTimeRanges(
    params: LocationTypes.TimeRangesQueryParams
) { 
    return useQuery({
        queryKey: ['time-ranges', params],
        queryFn: () => HttpQuery.APIVersion3GetTimeRanges(params),
    })
}

/**
 * Get time slots
 * @param params - Query parameters
 * @returns Time slots
 */

export function useGetTimeSlots(
    params: LocationTypes.TimeSlotsQueryParams
) {
    return useQuery({
        queryKey: ['time-slots', params],
        queryFn: () => HttpQuery.APIVersion3GetTimeSlots(params),
    })
}