import { useState } from "react";
import {
	Mail,
	MessageSquareText,
	Smartphone,
	GripVertical,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { DualRangeSlider } from "@/components/ui/dual-range-slider";
import { ExpirationSettings } from "@/components/common/ExpirationSettings";
import {
	DndContext,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
	type DragEndEvent,
} from "@dnd-kit/core";
import {
	arrayMove,
	SortableContext,
	sortableKeyboardCoordinates,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface GeneralSettings {
	scheduleVisibility: boolean;
	appointmentMethods: {
		inPerson: boolean;
		video: boolean;
		audio: boolean;
	};
	autoApprove: boolean;
	chat: boolean;
	chatMethod: "one-way" | "both-parties";
	scheduleBlock: boolean;
	scheduleBlockDate: Date | null;
	scheduleBlockWeeks: number;
	scheduleBlockSpecificDates: boolean;
}

interface Recipient {
	id: string;
	type: string;
	isSelected: boolean;
	isActive: boolean;
	minValue: number;
	maxValue: number;
	timeUnit: string;
	range: [number, number];
}

interface SortableRecipientItemProps {
	recipient: Recipient;
	onUpdate: (id: string, updates: Partial<Recipient>) => void;
}

const SortableRecipientItem = ({
	recipient,
	onUpdate,
}: SortableRecipientItemProps) => {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: recipient.id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	const recipientTypes = [
		{ value: "walk-in", label: "Walk-in" },
		{ value: "high-priority", label: "High Priority" },
		{ value: "upcoming-appointments", label: "Upcoming Appointments" },
		{ value: "cancelled-appointments", label: "Cancelled Appointments" },
		{ value: "no-show", label: "No Show" },
	];

	const timeUnits = [
		{ value: "hours", label: "Hours away" },
		{ value: "days", label: "Days away" },
		{ value: "weeks", label: "Weeks away" },
		{ value: "months", label: "Months away" },
	];

	const handleRangeChange = (values: number[]) => {
		onUpdate(recipient.id, {
			range: [values[0], values[1]],
			minValue: values[0],
			maxValue: values[1],
		});
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={`relative w-full rounded-[10px] border border-gray-200 bg-white transition-all ${
				isDragging ? "opacity-50 shadow-lg" : ""
			} ${
				recipient.isActive
					? "shadow-[0px_0px_4px_0px_rgba(53,154,222,0.25)]"
					: "bg-gray-50"
			}`}
		>
			<div className="relative flex size-full flex-col justify-center">
				<div className="relative box-border flex w-full flex-col content-stretch items-start justify-center gap-4 px-[15px] py-2.5">
					<div className="relative box-border flex w-full flex-row content-stretch items-center justify-between gap-2.5 p-0">
						{/* Drag Handle */}
						<div
							{...attributes}
							{...listeners}
							className={`relative size-6 shrink-0 cursor-grab overflow-clip active:cursor-grabbing ${
								!recipient.isActive ? "opacity-50" : ""
							}`}
						>
							<GripVertical className="size-6 text-gray-400" />
						</div>

						{/* Checkbox */}
						<div className="relative box-border flex shrink-0 flex-row content-stretch items-center justify-center px-0 py-1">
							<Checkbox
								checked={recipient.isSelected}
								onCheckedChange={(checked) =>
									onUpdate(recipient.id, {
										isSelected: !!checked,
									})
								}
								className="size-4"
								// disabled={!recipient.isActive}
							/>
						</div>

						{/* Recipient Type Dropdown */}
						<span className="w-[185px] text-sm font-medium capitalize">
							{recipient.type}
						</span>

						{/* Min Value Input */}
						<div className="relative flex h-10 shrink-0 rounded-md bg-white">
							<Input
								type="number"
								value={recipient.minValue}
								onChange={(e) =>
									onUpdate(recipient.id, {
										minValue: parseInt(e.target.value) || 0,
										range: [
											parseInt(e.target.value) || 0,
											recipient.maxValue,
										],
									})
								}
								className="h-10 w-[42px] [appearance:textfield] border-gray-200 px-0 py-2 text-center text-sm [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
								disabled={!recipient.isActive}
							/>
						</div>

						{/* Range Slider */}
						<div className="relative h-2.5 flex-2 shrink-0">
							<DualRangeSlider
								value={recipient.range}
								onValueChange={handleRangeChange}
								max={100}
								min={0}
								step={1}
								className="w-full px-2"
								disabled={!recipient.isActive}
							/>
						</div>

						{/* Max Value Input */}
						<div className="relative h-10 shrink-0 rounded-md bg-white">
							<Input
								type="number"
								value={recipient.maxValue}
								onChange={(e) =>
									onUpdate(recipient.id, {
										maxValue: parseInt(e.target.value) || 0,
										range: [
											recipient.minValue,
											parseInt(e.target.value) || 0,
										],
									})
								}
								className="h-10 w-[42px] [appearance:textfield] border-gray-200 px-1.5 py-2 text-center text-sm [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
								disabled={!recipient.isActive}
							/>
						</div>

						{/* Time Unit Dropdown */}
						{recipient.type === "upcoming-appointments" && (
							<div className="flex flex-row items-center self-stretch">
								<Select
									multiSelect={false}
									value={recipient.timeUnit}
									onValueChange={(
										value: string | string[]
									) => {
										const stringValue = Array.isArray(value)
											? value[0]
											: value;
										onUpdate(recipient.id, {
											timeUnit: stringValue,
										});
									}}
									disabled={!recipient.isActive}
								>
									<SelectTrigger className="h-full w-[140px]">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										{timeUnits.map((unit) => (
											<SelectItem
												key={unit.value}
												value={unit.value}
											>
												{unit.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

export const ScheduleOptimizerContent = () => {
	const [settings, setSettings] = useState<GeneralSettings>({
		scheduleVisibility: true,
		appointmentMethods: {
			inPerson: true,
			video: false,
			audio: false,
		},
		autoApprove: true,
		chat: true,
		chatMethod: "both-parties",
		scheduleBlock: true,
		scheduleBlockDate: new Date("2025-08-04"),
		scheduleBlockWeeks: 4,
		scheduleBlockSpecificDates: true,
	});

	const [recipients, setRecipients] = useState<Recipient[]>([
		{
			id: "1",
			type: "walk-in",
			isSelected: true,
			isActive: true,
			minValue: 10,
			maxValue: 30,
			timeUnit: "hours",
			range: [10, 30],
		},
		{
			id: "2",
			type: "high-priority",
			isSelected: true,
			isActive: true,
			minValue: 10,
			maxValue: 30,
			timeUnit: "hours",
			range: [10, 30],
		},
		{
			id: "3",
			type: "upcoming-appointments",
			isSelected: false,
			isActive: false,
			minValue: 2,
			maxValue: 4,
			timeUnit: "weeks",
			range: [2, 4],
		},
	]);

	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		})
	);

	const updateSettings = (updates: Partial<GeneralSettings>) => {
		setSettings((prev) => ({ ...prev, ...updates }));
	};

	const updateAppointmentMethod = (
		method: keyof GeneralSettings["appointmentMethods"],
		value: boolean
	) => {
		setSettings((prev) => ({
			...prev,
			appointmentMethods: {
				...prev.appointmentMethods,
				[method]: value,
			},
		}));
	};

	const updateRecipient = (id: string, updates: Partial<Recipient>) => {
		setRecipients((prev) =>
			prev.map((recipient) =>
				recipient.id === id ? { ...recipient, ...updates } : recipient
			)
		);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (active.id !== over?.id) {
			setRecipients((items) => {
				const oldIndex = items.findIndex(
					(item) => item.id === active.id
				);
				const newIndex = items.findIndex(
					(item) => item.id === over?.id
				);

				return arrayMove(items, oldIndex, newIndex);
			});
		}
	};

	return (
		<div className="space-y-4">
			<p className="border-b border-gray-200 pb-4 text-sm text-gray-600">
				When this feature is on, we alert selected patients of open time
				slots
			</p>

			{/* Schedule Optimizer Trigger(s) */}
			<div className="space-y-3 border-b border-gray-200 pb-4">
				<div className="flex flex-1 flex-col items-start justify-between space-y-4">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">
							Schedule Optimizer Trigger(s)
						</h3>
						<p className="text-sm text-gray-600">
							Select which actions would cause the Schedule
							Optimizer to be actived
						</p>
					</div>
					<div className="w-full flex-1 space-y-3 pl-6">
						<div className="flex items-center justify-between">
							<div className="space-y-1">
								<h3 className="text-lg font-medium">
									New Cancellation
								</h3>
								<p className="text-sm text-gray-600">
									If there is a cancellation, selected
									patients will be notified of an open time
									slot.
								</p>
							</div>
							<div className="flex items-center gap-1.5">
								<Switch
									checked={settings.autoApprove}
									onCheckedChange={(checked) =>
										updateSettings({ autoApprove: checked })
									}
								/>
								<span className="text-muted">
									{settings.autoApprove ? "On" : "Off"}
								</span>
							</div>
						</div>
						<div className="flex flex-col items-end gap-2.5">
							<div className="flex items-center gap-2.5">
								<span className="text-sm font-medium">
									Within
								</span>
								<ExpirationSettings
									value={settings.scheduleBlockWeeks.toString()}
									onValueChange={(value) =>
										updateSettings({
											scheduleBlockWeeks: parseInt(value),
										})
									}
									onUnitChange={() => {}}
									unit="weeks"
									useSpecificDate={false}
									label=""
									units={[
										{ value: "hours", label: "Hours" },
										{
											value: "minutes",
											label: "Minutes",
										},
										{ value: "weeks", label: "Weeks" },
										{ value: "days", label: "Days" },
									]}
									showAlternativeOption={false}
									containerWidth="w-[145px]"
								/>
							</div>
						</div>
					</div>
					<div className="w-full flex-1 space-y-3 pl-6">
						<div className="flex items-center justify-between">
							<div className="space-y-1">
								<h3 className="text-lg font-medium">
									Open Time Slot
								</h3>
								<p className="text-sm text-gray-600">
									If there is still an open time slot, send
									out an email
								</p>
							</div>
							<div className="flex items-center gap-1.5">
								<Switch
									checked={settings.autoApprove}
									onCheckedChange={(checked) =>
										updateSettings({ autoApprove: checked })
									}
								/>
								<span className="text-muted">
									{settings.autoApprove ? "On" : "Off"}
								</span>
							</div>
						</div>
						<div className="flex flex-col items-end gap-2.5">
							<div className="flex items-center gap-2.5">
								<span className="text-sm font-medium">
									Select Time
								</span>
								<ExpirationSettings
									value={settings.scheduleBlockWeeks.toString()}
									onValueChange={(value) =>
										updateSettings({
											scheduleBlockWeeks: parseInt(value),
										})
									}
									onUnitChange={() => {}}
									unit="weeks"
									useSpecificDate={false}
									label=""
									units={[
										{ value: "hours", label: "Hours" },
										{
											value: "minutes",
											label: "Minutes",
										},
										{ value: "weeks", label: "Weeks" },
										{ value: "days", label: "Days" },
									]}
									showAlternativeOption={false}
									containerWidth="w-[145px]"
								/>
								<span className="text-sm font-medium">
									Before
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Select Recipients */}
			<div className="space-y-4 border-b border-gray-200 pb-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">
							Select Recipients
						</h3>
						<p className="text-sm text-gray-600">
							[description here]....Select order to send by
							priority.
						</p>
					</div>
				</div>

				<div className="ml-0">
					<DndContext
						sensors={sensors}
						collisionDetection={closestCenter}
						onDragEnd={handleDragEnd}
					>
						<SortableContext
							items={recipients.map((r) => r.id)}
							strategy={verticalListSortingStrategy}
						>
							<div className="space-y-2">
								{recipients.map((recipient) => (
									<SortableRecipientItem
										key={recipient.id}
										recipient={recipient}
										onUpdate={updateRecipient}
									/>
								))}
							</div>
						</SortableContext>
					</DndContext>
				</div>
			</div>

			{/* Method of Delivery */}
			<div className="space-y-4 pb-4">
				<h3 className="text-lg font-medium">Method of Delivery</h3>
				<div className="flex gap-6">
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="in-person"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<Mail className="h-4 w-4" />
								Email
							</Label>
							<Checkbox
								id="in-person"
								checked={settings.appointmentMethods.inPerson}
								onCheckedChange={(checked) =>
									updateAppointmentMethod(
										"inPerson",
										!!checked
									)
								}
							/>
						</div>
					</Button>
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="video"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<MessageSquareText className="h-4 w-4" />
								SMS
							</Label>
							<Checkbox
								id="video"
								checked={settings.appointmentMethods.video}
								onCheckedChange={(checked) =>
									updateAppointmentMethod("video", !!checked)
								}
							/>
						</div>
					</Button>
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="audio"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<Smartphone className="h-4 w-4" />
								In-App
							</Label>
							<Checkbox
								id="audio"
								checked={settings.appointmentMethods.audio}
								onCheckedChange={(checked) =>
									updateAppointmentMethod("audio", !!checked)
								}
							/>
						</div>
					</Button>
				</div>
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end gap-3 pt-6">
				<Button variant="outline">Cancel</Button>
				<Button>Save</Button>
			</div>
		</div>
	);
};
