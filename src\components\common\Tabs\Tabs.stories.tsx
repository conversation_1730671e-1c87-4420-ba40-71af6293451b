import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { Tabs, TabsContent } from "./Tabs";
// import type { TabsProps } from "./types";

const meta: Meta<typeof Tabs> = {
	title: "Components/Common/Tabs",
	component: Tabs,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		defaultValue: {
			control: "text",
			description:
				"The value of the tab that should be active when initially rendered.",
		},
		onValueChange: {
			action: "tab-changed",
			description: "Event handler called when the value changes.",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleItems = [
	{ value: "organization", label: "Organization", count: 12 },
	{ value: "location", label: "Location" },
	{ value: "provider", label: "Provider" },
	{ value: "custom", label: "Custom", count: 12 },
];

export const Default: Story = {
	args: {
		items: sampleItems,
		defaultValue: "custom",
	},
	render: (args) => (
		<div className="w-full max-w-md">
			<Tabs {...args}>
				<TabsContent value="organization">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Organization Content
						</h3>
						<p>
							This is the organization tab content with count: 12
						</p>
					</div>
				</TabsContent>
				<TabsContent value="location">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Location Content
						</h3>
						<p>This is the location tab content without count</p>
					</div>
				</TabsContent>
				<TabsContent value="provider">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Provider Content
						</h3>
						<p>This is the provider tab content</p>
					</div>
				</TabsContent>
				<TabsContent value="custom">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Custom Content
						</h3>
						<p>This is the custom tab content with count: 12</p>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	),
};

export const WithoutCounts: Story = {
	args: {
		items: [
			{ value: "overview", label: "Overview" },
			{ value: "analytics", label: "Analytics" },
			{ value: "reports", label: "Reports" },
			{ value: "settings", label: "Settings" },
		],
		defaultValue: "overview",
	},
	render: (args) => (
		<div className="w-full max-w-md">
			<Tabs {...args}>
				<TabsContent value="overview">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">Overview</h3>
						<p>Overview tab content</p>
					</div>
				</TabsContent>
				<TabsContent value="analytics">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">Analytics</h3>
						<p>Analytics tab content</p>
					</div>
				</TabsContent>
				<TabsContent value="reports">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">Reports</h3>
						<p>Reports tab content</p>
					</div>
				</TabsContent>
				<TabsContent value="settings">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">Settings</h3>
						<p>Settings tab content</p>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	),
};

export const DisabledTab: Story = {
	args: {
		items: [
			{ value: "active", label: "Active", count: 5 },
			{ value: "disabled", label: "Disabled", count: 0, disabled: true },
			{ value: "another", label: "Another" },
		],
		defaultValue: "active",
	},
	render: (args) => (
		<div className="w-full max-w-md">
			<Tabs {...args}>
				<TabsContent value="active">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">Active Tab</h3>
						<p>This tab is active and has a count</p>
					</div>
				</TabsContent>
				<TabsContent value="disabled">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">Disabled Tab</h3>
						<p>This tab is disabled</p>
					</div>
				</TabsContent>
				<TabsContent value="another">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">Another Tab</h3>
						<p>This is another tab without count</p>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	),
};

export const ControlledExample: Story = {
	args: {
		items: sampleItems,
		value: "organization",
	},
	render: (args) => (
		<div className="w-full max-w-md">
			<Tabs {...args}>
				<TabsContent value="organization">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Controlled Tab
						</h3>
						<p>This demonstrates controlled tab behavior</p>
					</div>
				</TabsContent>
				<TabsContent value="location">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Location Content
						</h3>
						<p>Switch tabs to see the controlled behavior</p>
					</div>
				</TabsContent>
				<TabsContent value="provider">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Provider Content
						</h3>
						<p>Provider tab content</p>
					</div>
				</TabsContent>
				<TabsContent value="custom">
					<div className="rounded-md border p-4">
						<h3 className="text-lg font-semibold">
							Custom Content
						</h3>
						<p>Custom tab content with count badge</p>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	),
};
