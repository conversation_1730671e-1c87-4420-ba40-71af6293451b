// For more info, see https://github.com/storybookjs/eslint-plugin-storybook#configuration-flat-config-format
import storybook from "eslint-plugin-storybook";

import js from "@eslint/js";
import globals from "globals";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import tseslint from "typescript-eslint";

export default tseslint.config(
	{ ignores: ["dist"] },
	{
		extends: [
			js.configs.recommended,
			...tseslint.configs.recommended,
			"plugin:prettier/recommended",
		],
		files: ["**/*.{ts,tsx}"],
		languageOptions: {
			ecmaVersion: 2020,
			globals: globals.browser,
		},
		plugins: {
			"react-hooks": reactHooks,
			"react-refresh": reactRefresh,
			prettier: prettier,
		},
		rules: {
			...reactHooks.configs.recommended.rules,
			"react-refresh/only-export-components": [
				"warn",
				{ allowConstantExport: true },
			],
			"prettier/prettier": [
				"warn",
				{ endOfLine: "auto", useTabs: true },
				{ usePrettierrc: true },
			],
			"react-hooks/rules-of-hooks": "error", // Checks rules of Hooks
			"react-hooks/exhaustive-deps": "warn",
			"@typescript-eslint/no-unused-vars": ["warn", { 
				"argsIgnorePattern": "^_",
				"varsIgnorePattern": "^_",
				"ignoreRestSiblings": true,
				"args": "after-used"
			}],
			"@typescript-eslint/no-unused-imports": "warn",
			"@typescript-eslint/no-unused-expressions": "warn",
			"@typescript-eslint/no-explicit-any": "warn",
		},
	},
	storybook.configs["flat/recommended"]
);
