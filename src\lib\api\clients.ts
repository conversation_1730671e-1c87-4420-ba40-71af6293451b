import axios from "axios";
import { useAuthStore } from "@/stores/authStore";
import { useUIStore } from "@/stores/uiStore";
import useUserStore from "@/stores/useUserStore";
import { refreshToken } from "./auth";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const apiClient = axios.create({
	baseURL: API_BASE_URL,
	timeout: 10000,
	headers: {
		"Content-Type": "application/json",
	},
});

apiClient.interceptors.request.use(
	(config) => {
		const token = useAuthStore.getState().token;
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}
		return config;
	},
	(error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
	(response) => response,
	async (error) => {
		const { addToast } = useUIStore.getState();
		const { logout, setAuth } = useAuthStore.getState();

		const status = error.response?.status;
		const errorData = error.response?.data;

		switch (status) {
			case 401:
				// Authentication Error - Try to refresh token and retry request
				try {
					const refreshResponse = await refreshToken();
					if (refreshResponse.success) {
						// Update token in store
						setAuth(undefined, refreshResponse.data.token);

						// Update the Authorization header for the failed request
						error.config.headers.Authorization = `Bearer ${refreshResponse.data.token}`;

						// Retry the original request
						return apiClient.request(error.config);
					}
				} catch (refreshError) {
					console.error("Token refresh failed:", refreshError);
				}

				// If refresh failed or token refresh wasn't successful, logout
				logout();
				addToast({
					type: "error",
					title: "Session Expired",
					message: "Please log in again",
				});
				window.location.href = "/sign-in";
				break;

			case 403:
				// Authorization Error
				addToast({
					type: "error",
					title: "Access Denied",
					message:
						errorData?.message ||
						"You do not have permission to perform this action",
				});
				break;

			case 404:
				// Not Found Error
				addToast({
					type: "error",
					title: "Not Found",
					message:
						errorData?.message ||
						"The requested resource was not found",
				});
				break;

			case 409:
				// Conflict Error
				addToast({
					type: "error",
					title: "Conflict",
					message:
						errorData?.message ||
						"Resource already exists or cannot be updated",
				});
				break;

			case 429:
				// Rate Limit Error
				addToast({
					type: "warning",
					title: "Too Many Requests",
					message:
						errorData?.message || "Please slow down and try again",
					duration: 8000,
				});
				break;

			case 400:
				// Bad Request
				addToast({
					type: "error",
					title: "Bad Request",
					message:
						errorData?.message ||
						"Invalid request. Please check your input",
				});
				break;

			case 405:
				// Method Not Allowed
				addToast({
					type: "error",
					title: "Method Not Allowed",
					message: "This action is not supported",
				});
				break;

			case 503:
				// Service Unavailable
				addToast({
					type: "error",
					title: "Service Unavailable",
					message:
						"Service is temporarily unavailable. Please try again later",
					duration: 8000,
				});
				break;

			case 500:
			default:
				// Server Error or Unknown Error
				if (status >= 500) {
					addToast({
						type: "error",
						title: "Server Error",
						message:
							"Something went wrong on our end. Please try again",
					});
				} else if (status !== 422) {
					// Don't show toast for validation errors - handle them in forms
					addToast({
						type: "error",
						title: "Error",
						message:
							errorData?.message ||
							"An unexpected error occurred",
					});
				}
				break;
		}

		return Promise.reject(error);
	}
);
