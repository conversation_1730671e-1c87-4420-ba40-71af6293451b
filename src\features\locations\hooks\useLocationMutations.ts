import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { locationsApi } from "../api";
import { queryKeys } from "@/lib/query/keys";
import type { CreateLocationRequest, UpdateLocationRequest } from "../types";

export const useCreateLocation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: CreateLocationRequest) =>
			locationsApi.createLocation(data),
		onSuccess: (newLocation) => {
			// Invalidate and refetch locations list
			queryClient.invalidateQueries({
				queryKey: queryKeys.locations.lists(),
			});

			// Add the new location to existing cache if it exists
			queryClient.setQueryData(
				queryKeys.locations.detail(newLocation.id),
				newLocation
			);

			toast.success("Location created successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to create location");
		},
	});
};

export const useUpdateLocation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: UpdateLocationRequest) =>
			locationsApi.updateLocation(data),
		onSuccess: (updatedLocation) => {
			// Update specific location cache
			queryClient.setQueryData(
				queryKeys.locations.detail(updatedLocation.id),
				updatedLocation
			);

			// Invalidate locations list to reflect changes
			queryClient.invalidateQueries({
				queryKey: queryKeys.locations.lists(),
			});

			toast.success("Location updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update location");
		},
	});
};

export const useDeleteLocation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (id: string) => locationsApi.deleteLocation(id),
		onSuccess: (_, deletedId) => {
			// Remove from cache
			queryClient.removeQueries({
				queryKey: queryKeys.locations.detail(deletedId),
			});

			// Invalidate locations list
			queryClient.invalidateQueries({
				queryKey: queryKeys.locations.lists(),
			});

			toast.success("Location deleted successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to delete location");
		},
	});
};

export const useToggleLocationStatus = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ id, isActive }: { id: string; isActive: boolean }) =>
			locationsApi.toggleLocationStatus(id, isActive),
		onSuccess: (updatedLocation) => {
			// Update specific location cache
			queryClient.setQueryData(
				queryKeys.locations.detail(updatedLocation.id),
				updatedLocation
			);

			// Invalidate locations list
			queryClient.invalidateQueries({
				queryKey: queryKeys.locations.lists(),
			});

			toast.success(
				`Location ${updatedLocation.isActive ? "activated" : "deactivated"} successfully`
			);
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update location status");
		},
	});
};
