export interface StarRatingProps {
	/**
	 * The current rating value
	 */
	rating: number;
	/**
	 * Maximum number of stars to display
	 * @default 5
	 */
	maxRating?: number;
	/**
	 * Size of the stars
	 * @default "default"
	 */
	size?: "small" | "default" | "large";
	/**
	 * Custom className for styling
	 */
	className?: string;
	/**
	 * Color of filled stars
	 * @default "yellow"
	 */
	color?: "yellow" | "blue" | "green" | "red" | "purple";
	/**
	 * Whether to show rating as interactive (clickable)
	 * @default false
	 */
	interactive?: boolean;
	/**
	 * Callback when rating changes (only works when interactive is true)
	 */
	onRatingChange?: (rating: number) => void;
}
