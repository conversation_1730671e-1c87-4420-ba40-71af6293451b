import { useState } from "react";
import { <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

interface CancellationSettings {
	preventCancelling: boolean;
	timeValue: number;
	timeUnit: string;
	customMessage: string;
	sendVia: {
		inApp: boolean;
		sms: boolean;
		email: boolean;
	};
	customConfirmation: boolean;
}

export const CancellationRules = () => {
	const [settings, setSettings] = useState<CancellationSettings>({
		preventCancelling: true,
		timeValue: 10,
		timeUnit: "minutes",
		customMessage: `As you're cancelling on the same day....hours" before your selected time, charges may apply. Our billing team will get in touch with you if applicable.

You may choose to reschedule for another time. Thank you!`,
		sendVia: {
			inApp: true,
			sms: false,
			email: false,
		},
		customConfirmation: false,
	});

	const updateSettings = (updates: Partial<CancellationSettings>) => {
		setSettings((prev) => ({ ...prev, ...updates }));
	};

	const updateSendVia = (
		method: keyof CancellationSettings["sendVia"],
		value: boolean
	) => {
		setSettings((prev) => ({
			...prev,
			sendVia: {
				...prev.sendVia,
				[method]: value,
			},
		}));
	};

	const timeUnits = [
		{ value: "minutes", label: "Minutes" },
		{ value: "hours", label: "Hours" },
		{ value: "days", label: "Days" },
		{ value: "weeks", label: "Weeks" },
	];

	return (
		<div className="flex flex-col">
			<p className="border-b border-gray-200 pb-4 text-sm text-gray-600">
				Set cancellation rules and notification preferences. All times
				are relative to the appointment start time.
			</p>
			{/* Prevent Patients from Cancelling */}
			<div className="border-b border-gray-100 py-3">
				<div className="mb-1 flex items-start justify-between">
					<h3 className="text-base leading-[30px] font-semibold text-[#323539]">
						Prevent Patients from Cancelling
					</h3>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.preventCancelling}
							onCheckedChange={(checked) =>
								updateSettings({ preventCancelling: checked })
							}
						/>
						<span className="text-muted-foreground text-xs">
							{settings.preventCancelling ? "On" : "Off"}
						</span>
					</div>
				</div>

				<p className="text-xs leading-4 text-[#060d25]">
					Limit cancellations within a set time before the
					appointment.
				</p>

				{settings.preventCancelling && (
					<div className="space-y-6 pt-3">
						{/* Time Configuration Row */}
						<div className="flex items-center gap-3">
							<div className="rounded-lg bg-gray-100 p-2">
								<Bell className="h-[18px] w-[18px] text-gray-500" />
							</div>
							<span className="text-[13px] leading-5 font-medium tracking-[-0.1px] text-[#323539]">
								Prevent Patients from Cancelling
							</span>
							<div className="h-10 w-[145px] rounded-md border border-zinc-200 bg-white">
								<div className="flex h-full items-center justify-between px-3 py-2">
									<Input
										type="number"
										value={settings.timeValue}
										onChange={(e) =>
											updateSettings({
												timeValue:
													parseInt(e.target.value) ||
													0,
											})
										}
										className="h-auto border-0 bg-transparent p-0 text-xs text-zinc-800 focus-visible:ring-0 [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
										style={{ width: "20px" }}
									/>
									<Select
										value={settings.timeUnit}
										onValueChange={(
											value: string | string[]
										) => {
											const stringValue = Array.isArray(
												value
											)
												? value[0]
												: value;
											updateSettings({
												timeUnit: stringValue,
											});
										}}
									>
										<SelectTrigger className="h-auto border-0 bg-transparent p-0 text-xs text-zinc-800 focus:ring-0">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											{timeUnits.map((unit) => (
												<SelectItem
													key={unit.value}
													value={unit.value}
												>
													{unit.label}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
							</div>
							<span className="text-[13px] leading-5 font-medium tracking-[-0.1px] text-[#323539]">
								Before the appointment
							</span>
						</div>

						{/* Custom Message Section */}
						<div className="space-y-2">
							<label className="text-sm font-medium text-zinc-900">
								Custom Message
							</label>
							<div className="space-y-2">
								<Textarea
									value={settings.customMessage}
									onChange={(e) =>
										updateSettings({
											customMessage: e.target.value,
										})
									}
									className="min-h-[140px] resize-none text-sm text-zinc-500"
									placeholder="Enter your custom cancellation message..."
								/>
								<div className="text-sm text-zinc-500">
									<span className="font-bold">
										{settings.customMessage.length}
									</span>
									/250
								</div>
							</div>
						</div>

						{/* Send Via Section */}
						<div className="flex items-center gap-5">
							<span className="text-sm leading-5 font-medium tracking-[-0.1px] text-[#323539]">
								Send via
							</span>
							<div className="flex items-center gap-3">
								<div className="flex items-center gap-2">
									<Checkbox
										checked={settings.sendVia.inApp}
										onCheckedChange={(checked) =>
											updateSendVia("inApp", !!checked)
										}
									/>
									<span className="text-xs font-medium text-zinc-800">
										In-App
									</span>
								</div>
								<div className="flex items-center gap-2">
									<Checkbox
										checked={settings.sendVia.sms}
										onCheckedChange={(checked) =>
											updateSendVia("sms", !!checked)
										}
										className="h-3 w-3"
									/>
									<span className="text-xs font-medium text-zinc-800">
										SMS
									</span>
								</div>
								<div className="flex items-center gap-2">
									<Checkbox
										checked={settings.sendVia.email}
										onCheckedChange={(checked) =>
											updateSendVia("email", !!checked)
										}
										className="h-3 w-3"
									/>
									<span className="text-xs font-medium text-zinc-800">
										Email
									</span>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Custom Cancellation Confirmation */}
			<div className="border-b border-gray-100 pt-3 pb-6">
				<div className="mb-1 flex items-start justify-between">
					<h3 className="text-base leading-[30px] font-semibold text-[#323539]">
						Custom Cancellation Confirmation
					</h3>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.customConfirmation}
							onCheckedChange={(checked) =>
								updateSettings({ customConfirmation: checked })
							}
						/>
						<span className="text-muted-foreground text-xs">
							{settings.customConfirmation ? "On" : "Off"}
						</span>
					</div>
				</div>

				<p className="text-xs leading-4 text-[#060d25]">
					Set cancellation message preferences. All times are relative
					to the appointment start time.
				</p>
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end gap-3">
				<Button variant="outline">Cancel</Button>
				<Button>Save</Button>
			</div>
		</div>
	);
};
