import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { BlockPicker, type ColorResult } from "react-color";
import { Separator } from "@/components/ui/separator";
import { Eye, PaintBucket } from "lucide-react";

const ThemeSettings: React.FC = () => {
	const [primaryColor, setPrimaryColor] = useState("#2563eb");

	return (
		<div className="max-w-lg">
			<h2 className="mb-4 text-xl font-semibold">Theme</h2>
			<div className="mb-2 flex flex-col gap-2">
				<span className="text-sm font-medium">Assign Color</span>
				<Separator className="mb-4" />
				<div className="flex flex-col justify-center gap-8">
					<div className="group relative flex w-fit gap-1.5 rounded bg-[#F4F4F5] p-2">
						<div>
							<PaintBucket className="h-5 w-5" />
						</div>
						<div
							style={{ backgroundColor: primaryColor }}
							className="h-5 w-5 rounded-sm"
						></div>{" "}
						<div className="absolute top-full left-full hidden group-hover:flex">
							<BlockPicker
								color={primaryColor}
								onChange={(color: ColorResult) =>
									setPrimaryColor(color.hex)
								}
								triangle="hide"
							/>
						</div>
					</div>

					{/* Example swatches, you can replace with your own if needed */}
					<div className="flex flex-col gap-2">
						<p className="text-sm">Recents</p>
						<div className="flex gap-2">
							<span className="inline-block h-6 w-6 rounded border border-gray-300 bg-black" />
							<span className="inline-block h-6 w-6 rounded border border-gray-300 bg-white" />
							<span className="inline-block h-6 w-6 rounded border border-gray-300 bg-[#e91e63]" />
							<span className="inline-block h-6 w-6 rounded border border-gray-300 bg-[#9c27b0]" />
							<span className="inline-block h-6 w-6 rounded border border-gray-300 bg-[#00bcd4]" />
							<span className="inline-block h-6 w-6 rounded border border-gray-300 bg-[#ffc107]" />
							<span className="inline-block h-6 w-6 rounded border border-gray-300 bg-[#795548]" />
						</div>
					</div>
				</div>
				<div className="mt-6 flex gap-3 self-end">
					<Button
						variant="outline"
						className="flex items-center gap-2"
					>
						<Eye className="h-4 w-4" /> Preview
					</Button>
					<Button className="bg-blue-600 text-white">Update</Button>
				</div>
			</div>
		</div>
	);
};

export default ThemeSettings;
