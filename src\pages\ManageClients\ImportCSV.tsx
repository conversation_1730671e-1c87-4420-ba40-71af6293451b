import { useUIStore } from "@/stores/uiStore";
import { useEffect, useState, useCallback, useMemo } from "react";
import { useNavigate } from "react-router";
import { Check } from "lucide-react";
import * as XLSX from "xlsx";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import type { BusinessAttribute } from "@/types/businessAttributes";
import ImportCSVUpload from "./ImportCSVUpload";
import ImportCSVMapping from "./ImportCSVMapping";
import ImportCSVValidation from "./ImportCSVValidation";

interface MappingRow {
	id: number;
	excel: string;
	migranium: string;
	type: string;
	isCreatingNew?: boolean;
	newFieldLabel?: string;
}

interface MappingData {
	mappingRows: MappingRow[];
	selectedSheet: string;
	availableSheets: string[];
	clearStoredCustomFields?: () => void;
}

export default function ImportCSV() {
	const navigate = useNavigate();
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);

	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useBusinessAttributesForForm();

	const businessAttributes: BusinessAttribute[] = useMemo(() => {
		const attributes = (businessAttributesData as any)?.data || [];
		return attributes;
	}, [businessAttributesData, isLoadingAttributes]);

	const [uploadedFile, setUploadedFile] = useState<File | null>(null);
	const [currentStep, setCurrentStep] = useState(1);
	const [isImportSuccessful, setIsImportSuccessful] = useState(false);
	const [mappingData, setMappingData] = useState<MappingData | null>(null);

	const handleFileUpload = useCallback((file: File) => {
		setUploadedFile(file);
	}, []);

	const handleRemoveFile = useCallback(() => {
		setUploadedFile(null);
	}, []);

	const handleChangeFile = useCallback(() => {
		const input = document.createElement("input");
		input.type = "file";
		input.accept = ".csv,.xlsx,.xls";
		input.onchange = (e) => {
			const target = e.target as HTMLInputElement;
			if (target.files && target.files[0]) {
				setUploadedFile(target.files[0]);
			}
		};
		input.click();
	}, []);

	const handleDownloadSample = useCallback(() => {
		if (isLoadingAttributes) {
			return;
		}
		const allFields = businessAttributes.map((attr) => ({
			key: attr.key,
			label: attr.label,
		}));
		const headers = allFields.map((field) => field.label);
		const worksheetData = [headers];
		const workbook = XLSX.utils.book_new();
		const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
		XLSX.utils.book_append_sheet(workbook, worksheet, "Patient Data");
		const currentDate = new Date().toISOString().split("T")[0];
		const filename = `patient_import_template_${currentDate}.xlsx`;
		XLSX.writeFile(workbook, filename);
	}, [businessAttributes, isLoadingAttributes]);

	const handleUploadNext = useCallback(() => {
		if (uploadedFile) {
			console.log("Uploading file:", uploadedFile.name);
			setCurrentStep(2);
		}
	}, [uploadedFile]);

	const handleMappingBack = useCallback(() => {
		setCurrentStep(1);
	}, []);

	const handleMappingNext = useCallback(() => {
		setCurrentStep(3);
	}, []);

	const handleMappingComplete = useCallback((data: MappingData) => {
		setMappingData(data);
	}, []);

	const handleMappingTest = useCallback(() => {
		console.log("Testing mapping");
	}, []);

	const handleValidationBack = useCallback(() => {
		setCurrentStep(2);
	}, []);

	const handleValidationTest = useCallback(() => {
		console.log("Testing validation");
	}, []);

	const handleValidationImport = useCallback(() => {
		console.log("Importing data");
	}, []);

	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/patients",
			},
			{
				label: "All Patients",
				href: "/dashboard/patients",
			},
			{
				label: "Import CSV",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<h1 className="text-foreground text-2xl font-bold">
					Import Bulk Patient
				</h1>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [setPageHeaderContent]);

	return (
		<div className="mx-auto flex w-full flex-col items-center justify-start gap-4 py-8">
			{!isImportSuccessful && (
				<div className="inline-flex w-96 items-start justify-start gap-2 p-2">
					<div className="flex items-center justify-start gap-1">
						<div className="flex h-4 w-4 items-center justify-between">
							<div className="flex h-4 w-4 items-center justify-center gap-2.5 overflow-hidden rounded-lg bg-[#005893] p-1 outline-1 outline-offset-[-1px] outline-[#005893]">
								<div className="relative h-2 w-2 overflow-hidden">
									<Check className="h-2 w-2 text-white" />
								</div>
							</div>
						</div>
						<div className="text-[10px] leading-3 font-normal text-gray-500">
							Upload File
						</div>
					</div>
					<div className="inline-flex h-4 flex-1 flex-col items-start justify-center gap-2.5 py-1.5">
						<div
							className={`h-px self-stretch ${currentStep >= 2 ? "bg-[#005893]" : "bg-gray-300"}`}
						/>
					</div>
					<div className="flex items-center justify-start gap-1">
						<div className="flex h-4 w-4 items-center justify-between">
							{currentStep >= 2 ? (
								<div className="flex h-4 w-4 items-center justify-center gap-2.5 overflow-hidden rounded-lg bg-[#005893] p-1 outline-1 outline-offset-[-1px] outline-[#005893]">
									<div className="relative h-2 w-2 overflow-hidden">
										<Check className="h-2 w-2 text-white" />
									</div>
								</div>
							) : (
								<div className="inline-flex h-4 w-4 flex-col items-center justify-center gap-2.5 overflow-hidden rounded-lg bg-white p-0.5 outline-1 outline-offset-[-1px] outline-gray-300">
									<div className="h-3 w-3 justify-center text-center text-[8px] leading-3 font-medium text-gray-700">
										02
									</div>
								</div>
							)}
						</div>
						<div className="text-[10px] leading-3 font-normal text-gray-500">
							Map Column
						</div>
					</div>
					<div className="inline-flex h-4 flex-1 flex-col items-start justify-center gap-2.5 py-1.5">
						<div
							className={`h-px self-stretch ${currentStep >= 3 ? "bg-[#005893]" : "bg-gray-300"}`}
						/>
					</div>
					<div className="flex items-center justify-start gap-1">
						<div className="flex h-4 w-4 items-center justify-between">
							{currentStep >= 3 ? (
								<div className="flex h-4 w-4 items-center justify-center gap-2.5 overflow-hidden rounded-lg bg-[#005893] p-1 outline-1 outline-offset-[-1px] outline-[#005893]">
									<div className="relative h-2 w-2 overflow-hidden">
										<Check className="h-2 w-2 text-white" />
									</div>
								</div>
							) : (
								<div className="inline-flex h-4 w-4 flex-col items-center justify-center gap-2.5 overflow-hidden rounded-lg bg-white p-0.5 outline-1 outline-offset-[-1px] outline-gray-300">
									<div className="h-3 w-3 justify-center text-center text-[8px] leading-3 font-medium text-gray-700">
										03
									</div>
								</div>
							)}
						</div>
						<div className="text-[10px] leading-3 font-normal text-gray-500">
							Upload Data
						</div>
					</div>
				</div>
			)}

			{currentStep === 1 && (
				<ImportCSVUpload
					uploadedFile={uploadedFile}
					onFileUpload={handleFileUpload}
					onRemoveFile={handleRemoveFile}
					onChangeFile={handleChangeFile}
					onDownloadSample={handleDownloadSample}
					onNext={handleUploadNext}
				/>
			)}
			{currentStep === 2 && (
				<ImportCSVMapping
					uploadedFile={uploadedFile}
					onBack={handleMappingBack}
					onNext={handleMappingNext}
					onTest={handleMappingTest}
					onMappingComplete={handleMappingComplete}
				/>
			)}
			{currentStep === 3 && (
				<ImportCSVValidation
					uploadedFile={uploadedFile}
					mappingData={mappingData}
					onBack={handleValidationBack}
					onTest={handleValidationTest}
					onImport={handleValidationImport}
					onImportAgain={() => {
						setCurrentStep(1);
						setIsImportSuccessful(false);
						setMappingData(null);
					}}
					onDone={() => {
						navigate("/dashboard/patients");
					}}
					onImportSuccess={setIsImportSuccessful}
				/>
			)}
		</div>
	);
}
