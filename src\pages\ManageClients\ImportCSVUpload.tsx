import { useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Download, Upload, FileText } from "lucide-react";
import { UploadCard } from "@/components/ui-components/Upload";

interface ImportCSVUploadProps {
	uploadedFile: File | null;
	onFileUpload: (file: File) => void;
	onRemoveFile: () => void;
	onChangeFile: () => void;
	onDownloadSample: () => void;
	onNext: () => void;
}

export default function ImportCSVUpload({
	uploadedFile,
	onFileUpload,
	onRemoveFile,
	onChangeFile,
	onDownloadSample,
	onNext,
}: ImportCSVUploadProps) {
	const handleFileUpload = useCallback(() => {
		const input = document.createElement("input");
		input.type = "file";
		input.accept = ".csv,.xlsx,.xls";
		input.onchange = (e) => {
			const target = e.target as HTMLInputElement;
			if (target.files && target.files[0]) {
				onFileUpload(target.files[0]);
			}
		};
		input.click();
	}, [onFileUpload]);

	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
	}, []);

	const handleDrop = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			if (e.dataTransfer.files && e.dataTransfer.files[0]) {
				onFileUpload(e.dataTransfer.files[0]);
			}
		},
		[onFileUpload]
	);

	return (
		<div className="flex w-full flex-col items-start justify-start gap-2">
			{/* Info Card */}
			<div className="flex w-full items-start justify-between gap-6 overflow-hidden rounded-lg bg-white p-4 outline-1 outline-offset-[-1px] outline-gray-300">
				<div className="flex flex-1 items-start justify-start gap-2.5">
					<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 p-2.5">
						<Upload className="h-3.5 w-3.5 text-gray-700" />
					</div>
					<div className="inline-flex flex-1 flex-col items-start justify-start gap-1">
						<div className="justify-start text-sm leading-tight font-semibold text-gray-900">
							Upload Patient Information
						</div>
						<div className="justify-start text-[10px] leading-3 font-normal text-gray-500">
							Download the excel template, populate the document
							and then upload that file below.
						</div>
					</div>
				</div>
				<Button
					onClick={onDownloadSample}
					className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 hover:bg-[#004a7a]"
				>
					<Download className="h-3 w-3 text-white" />
					<span className="text-xs leading-none font-medium text-white">
						Download Sample CSV
					</span>
				</Button>
			</div>

			{/* Upload Area or File Display */}
			{!uploadedFile ? (
				<UploadCard
					variant="centered"
					width="w-full"
					title="Click or drag file here to upload file"
					description="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
					buttonText="Browse Files"
					accept=".csv,.xlsx,.xls"
					onBrowseClick={handleFileUpload}
					onDragOver={handleDragOver}
					onDrop={handleDrop}
					className="flex min-h-[400px] w-full flex-col items-center justify-center gap-6 rounded-lg p-6 outline-1 outline-offset-[-1px] outline-gray-300 [&>*:last-child]:w-fit [&>*:last-child]:self-center"
				/>
			) : (
				<div className="flex w-full flex-col items-start justify-start gap-2.5">
					<div className="flex h-[400px] w-full flex-col items-center justify-center gap-6 rounded-lg bg-white p-6 outline-1 outline-offset-[-1px] outline-gray-300">
						<div className="relative h-10 w-10 overflow-hidden">
							<FileText className="h-9 w-7 text-gray-700" />
						</div>
						<div className="flex flex-col items-center justify-start gap-1 self-stretch">
							<div className="justify-start self-stretch text-center text-sm leading-tight font-semibold text-gray-900">
								{uploadedFile.name}
							</div>
							<div className="justify-start self-stretch text-center text-[10px] leading-3 font-normal text-gray-500">
								File size:{" "}
								{(uploadedFile.size / 1024 / 1024).toFixed(2)}mb
							</div>
						</div>
						<div className="inline-flex items-center justify-start gap-2">
							<Button
								onClick={onRemoveFile}
								className="flex h-9 w-28 items-center justify-center gap-2 rounded-md bg-red-600 px-4 py-2 hover:bg-red-700"
							>
								<span className="text-xs leading-none font-medium text-white">
									Remove
								</span>
							</Button>
							<Button
								onClick={onChangeFile}
								variant="outline"
								className="flex h-9 w-28 items-center justify-center gap-2 rounded-md bg-white px-4 py-2 outline-1 outline-offset-[-1px] outline-gray-300"
							>
								<span className="text-xs leading-none font-medium text-gray-900">
									Change
								</span>
							</Button>
						</div>
					</div>
				</div>
			)}

			<div className="flex w-full items-center justify-end gap-3 px-6 py-3">
				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						onClick={onNext}
						disabled={!uploadedFile}
						className={`inline-flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2 ${
							uploadedFile
								? "bg-[#005893] text-white hover:bg-[#004a7a]"
								: "cursor-not-allowed bg-[#005893] text-white opacity-50"
						}`}
					>
						<span className="text-xs leading-none font-medium">
							Upload
						</span>
					</Button>
				</div>
			</div>
		</div>
	);
}
