import type { CategoryData } from "@/lib/api/categoriesApi";
import type { Category } from "@/components/ui-components/CategoryListCard";

export const transformCategoryDataToCategory = (
	categoryData: CategoryData
): Category => {
	return {
		id: categoryData.id.toString(),
		name: categoryData.name,
		color: categoryData.color,
		status: categoryData.is_active ? "Active" : "Inactive",
		clients: categoryData.client_count,
		stations: categoryData.station_count,
		dateAdded: categoryData.created_at
			? new Date(categoryData.created_at).toLocaleDateString("en-US", {
					day: "2-digit",
					month: "short",
					year: "numeric",
					hour: "2-digit",
					minute: "2-digit",
					hour12: true,
				})
			: "N/A",
	};
};

export const transformCategoriesDataToCategories = (
	categoriesData: CategoryData[]
): Category[] => {
	return categoriesData.map(transformCategoryDataToCategory);
};

export const transformCategoryToCategoryData = (
	category: Category
): CategoryData => {
	return {
		id: parseInt(category.id),
		name: category.name,
		color: category.color,
		is_conditional: false, 
		description: "", 
		type: "manual", 
		client_count: category.clients,
		station_count: category.stations,
		is_active: category.status === "Active",
		business_id: 0, 
		created_at: category.dateAdded,
		updated_at: category.dateAdded,
	};
};
