import { Sheet, SheetContent } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Cell } from "recharts";

const SCALE_COLORS = [
	"#2A9D90",
	"#005893",
	"#274754",
	"#E8C468",
	"#E76E50",
	"#2A9D90",
	"#005893",
	"#274754",
	"#E8C468",
	"#E76E50",
];

interface ScaleResponse {
	id: string;
	label: string;
	count: number;
	percentage: number;
	color: string;
}

interface ScaleSurveyQuestionSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	questionData?: {
		question: string;
		type: string;
		totalResponses: number;
		responses: ScaleResponse[];
	};
}

const defaultQuestionData = {
	question:
		"On a scale of 1 - 10, 1 being the lowest, how much pain are you in right now?",
	type: "1 - 10 Scale",
	totalResponses: 925,
	responses: [
		{
			id: "1",
			label: "1",
			count: 275,
			percentage: 29.7,
			color: "#2A9D90",
		},
		{
			id: "2",
			label: "2",
			count: 90,
			percentage: 9.7,
			color: "#005893",
		},
		{
			id: "3",
			label: "3",
			count: 90,
			percentage: 9.7,
			color: "#274754",
		},
		{
			id: "4",
			label: "4",
			count: 173,
			percentage: 18.7,
			color: "#E8C468",
		},
		{
			id: "5",
			label: "5",
			count: 100,
			percentage: 10.8,
			color: "#E76E50",
		},
		{
			id: "6",
			label: "6",
			count: 80,
			percentage: 8.6,
			color: "#2A9D90",
		},
		{
			id: "7",
			label: "7",
			count: 187,
			percentage: 20.2,
			color: "#005893",
		},
		{
			id: "8",
			label: "8",
			count: 96,
			percentage: 10.4,
			color: "#274754",
		},
		{
			id: "9",
			label: "9",
			count: 104,
			percentage: 11.2,
			color: "#E8C468",
		},
		{
			id: "10",
			label: "10",
			count: 98,
			percentage: 10.6,
			color: "#E76E50",
		},
	],
};

export function ScaleSurveyQuestionSheet({
	open,
	onOpenChange,
	questionData = defaultQuestionData,
}: ScaleSurveyQuestionSheetProps) {
	const handleClose = () => {
		onOpenChange(false);
	};

	const chartData = questionData.responses.map((response) => ({
		name: response.label,
		value: response.count,
		color: response.color,
	}));

	const RADIAN = Math.PI / 180;
	const renderCustomizedLabel = ({
		cx,
		cy,
		midAngle,
		innerRadius,
		outerRadius,
		value,
		index,
	}: any) => {
		const radius = innerRadius + (outerRadius - innerRadius) * 1.2;
		const x = cx + radius * Math.cos(-midAngle * RADIAN);
		const y = cy + radius * Math.sin(-midAngle * RADIAN);

		return (
			<text
				x={x}
				y={y}
				fill="#374151"
				textAnchor={x > cx ? "start" : "end"}
				dominantBaseline="central"
				fontSize="14"
				fontWeight="500"
			>
				{value}
			</text>
		);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full !max-w-[600px] overflow-y-auto p-4 [&>button]:hidden">
				<div className="inline-flex w-full flex-col items-center justify-start gap-3 bg-white">
					<div className="inline-flex w-full items-start justify-between">
						<div className="flex-1 text-base leading-7 font-semibold text-gray-900">
							{questionData.question}
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleClose}
							className="h-4 w-4 p-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
					<div className="inline-flex w-full items-center justify-center gap-2.5">
						<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
							<div className="text-[10px] leading-3 font-medium text-gray-900">
								{questionData.type}
							</div>
						</div>
						<div className="flex-1 text-xs leading-none font-normal text-gray-500">
							{questionData.totalResponses} Responses
						</div>
					</div>
					<div className="h-px w-full border-t border-gray-200" />
					<div className="inline-flex w-full flex-wrap content-start items-start justify-center gap-3 py-3">
						{questionData.responses.map((response, index) => (
							<div
								key={response.id}
								className="flex items-center justify-start gap-1.5"
							>
								<div
									className="h-3 w-3 rounded-full"
									style={{
										backgroundColor: SCALE_COLORS[index],
									}}
								/>
								<div className="text-xs leading-none font-normal text-gray-900">
									{response.label}
								</div>
							</div>
						))}
					</div>
					<div className="h-96 w-full">
						<ResponsiveContainer width="100%" height="100%">
							<PieChart>
								<Pie
									data={chartData}
									cx="50%"
									cy="50%"
									labelLine={false}
									label={renderCustomizedLabel}
									outerRadius={120}
									dataKey="value"
								>
									{chartData.map((_, index) => (
										<Cell
											key={`cell-${index}`}
											fill={SCALE_COLORS[index]}
										/>
									))}
								</Pie>
								<Tooltip
									formatter={(value, name) => [
										`${value} responses`,
										name,
									]}
									labelStyle={{ color: "#374151" }}
									contentStyle={{
										backgroundColor: "white",
										border: "1px solid #d1d5db",
										borderRadius: "6px",
										boxShadow:
											"0 4px 6px -1px rgba(0, 0, 0, 0.1)",
									}}
								/>
							</PieChart>
						</ResponsiveContainer>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
