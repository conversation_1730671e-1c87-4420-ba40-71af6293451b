import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	ChevronLeft,
	ChevronRight,
	Clock,
	Store,
	CalendarClock,
	MoreHorizontal,
	Trash2,
	Edit,
	FileText,
	RefreshCcw,
	ChevronDown,
	Zap,
	Smartphone,
	MapPin,
	Map,
} from "lucide-react";

const visitHistoryData = [
	{
		id: 1,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. <PERSON>",
		date: "12 Aug 2024",
		time: "12:30am - 1:00am",
		status: "Upcoming",
	},
	{
		id: 2,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. <PERSON>",
		date: "13 Aug 2024",
		time: "2:00pm - 2:30pm",
		status: "No Show",
	},
	{
		id: 3,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. <PERSON>",
		date: "14 Aug 2024",
		time: "9:00am - 9:30am",
		status: "In Progress",
	},
	{
		id: 4,
		location: "Loaction Name",
		service: "Service name",
		doctor: "<PERSON>. <PERSON>",
		date: "15 Aug 2024",
		time: "10:00am - 10:30am",
		status: "Cancelled (Admin)",
	},
	{
		id: 5,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. <PERSON>",
		date: "16 Aug 2024",
		time: "11:00am - 11:30am",
		status: "Completed",
	},
];

const getStatusBadgeStyle = (status: string) => {
	switch (status) {
		case "Upcoming":
			return "bg-cyan-100 text-cyan-900";
		case "No Show":
			return "bg-red-100 text-red-600";
		case "In Progress":
			return "bg-amber-100 text-amber-700";
		case "Cancelled (Admin)":
			return "bg-cyan-900/10 text-cyan-900";
		case "Completed":
			return "bg-green-100 text-green-700";
		default:
			return "bg-gray-100 text-gray-700";
	}
};

interface PatientAppointmentsTabProps {
}

export const PatientAppointmentsTab: React.FC<
	PatientAppointmentsTabProps
> = () => {
	const [selectedAppointmentId, setSelectedAppointmentId] = useState<
		number | null
	>(null);

	return (
		<div className="flex w-full flex-1 flex-col">
			{selectedAppointmentId === null ? (
				<div className="flex w-full flex-1 flex-col">
					<div className="overflow-hidden rounded-xl border border-gray-200">
						{visitHistoryData.map((visit) => (
							<div
								key={visit.id}
								className="cursor-pointer border-t border-gray-200 first:border-t-0 hover:bg-gray-50"
								onClick={() =>
									setSelectedAppointmentId(visit.id)
								}
							>
								<div className="flex items-center justify-between p-3">
									<div className="flex min-w-[80px] flex-1 flex-col gap-1">
										<div className="text-xs font-medium">
											{visit.location}
										</div>
										<div className="flex flex-col gap-0.5">
											<div className="text-[10px] text-gray-600">
												{visit.service}
											</div>
											<div className="text-[10px] text-gray-500">
												{visit.doctor}
											</div>
										</div>
									</div>
									<div className="flex w-36 min-w-[80px] gap-2">
										<div className="p-0.5">
											<CalendarClock className="h-3 w-3 text-gray-500" />
										</div>
										<div className="flex flex-col gap-0.5">
											<div className="text-xs">
												{visit.date}
											</div>
											<div className="text-[10px] text-gray-500">
												{visit.time}
											</div>
										</div>
									</div>
									<div className="flex w-40 min-w-[80px] items-center justify-end gap-2">
										<div
											className={`rounded-md px-2 py-1 ${getStatusBadgeStyle(visit.status)}`}
										>
											<div className="text-[10px] font-medium">
												{visit.status}
											</div>
										</div>
										<ChevronRight className="h-4 w-4 text-gray-400" />
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			) : (
				<div className="flex w-full flex-1 flex-col">
					<div className="rounded-lg border border-gray-200 bg-white p-4">
						<div className="mb-4 flex items-center gap-2">
							<Button
								variant="ghost"
								size="sm"
								className="h-auto p-0 text-xs text-gray-600 hover:text-gray-900"
								onClick={() => setSelectedAppointmentId(null)}
							>
								<ChevronLeft className="mr-1 h-3 w-3" />
								Back to All Appointments
							</Button>
						</div>

						<div className="mb-3 flex items-center justify-between">
							<div className="flex items-center gap-2">
								<Store className="h-4 w-4 text-gray-400" />
								<span className="text-sm font-medium text-gray-900">
									[Provider Name]
								</span>
							</div>
							<div className="flex items-center gap-2">
								<div className="rounded-md bg-cyan-100 px-2 py-1">
									<span className="text-xs font-medium text-cyan-900">
										Upcoming
									</span>
								</div>
								<Button
									variant="ghost"
									size="icon"
									className="h-6 w-6 p-0"
								>
									<MoreHorizontal className="h-4 w-4 text-gray-400" />
								</Button>
							</div>
						</div>
						<div className="mb-3 flex items-center justify-between">
							<div className="flex items-center gap-2">
								<Zap className="h-4 w-4 text-gray-400" />
								<span className="text-sm font-medium text-gray-900">
									[Service Name]
								</span>
							</div>
							<div className="rounded-md border border-blue-200 bg-blue-50 px-2 py-1">
								<div className="flex items-center gap-1">
									<div className="h-2 w-2 rounded-full bg-blue-600" />
									<span className="text-xs font-medium text-blue-800">
										In Person
									</span>
								</div>
							</div>
						</div>

						<div className="mb-3 flex items-center gap-2">
							<Clock className="h-4 w-4 text-gray-400" />
							<span className="text-sm text-gray-900">
								[00:00] [AM] - [00:00] [AM]
							</span>
						</div>

						<div className="mb-3 flex items-center gap-2">
							<Smartphone className="h-4 w-4 text-gray-400" />
							<span className="text-sm text-gray-600">
								Booked via QR Code
							</span>
						</div>

						<div className="mb-3 flex items-center gap-2">
							<MapPin className="h-4 w-4 text-gray-400" />
							<span className="text-sm font-medium text-gray-900">
								Location Name
							</span>
						</div>

						<div className="mb-4 flex items-start gap-2">
							<Map className="mt-0.5 h-4 w-4 text-gray-400" />
							<span className="text-sm leading-relaxed text-gray-600">
								5 Park Home Ave #130, Toronto, ON M2N 6L4,
								Toronto, Ontario, Canada
							</span>
						</div>

						<div className="mb-4 border-t border-gray-200 pt-4">
							<div className="flex items-center justify-between">
								<div className="flex-1">
									<div className="mb-3 text-sm font-medium text-gray-900">
										[Form Name]
									</div>
									<div className="flex items-center gap-4">
										<div className="rounded-md bg-gray-100 px-2 py-1">
											<span className="text-xs font-medium text-gray-700">
												[Service Name]
											</span>
										</div>
										<div className="flex items-center gap-2 text-gray-500">
											<Store className="h-3 w-3" />
											<span className="text-xs">
												[Provider Name]
											</span>
										</div>
										<div className="flex items-center gap-2 text-gray-500">
											<CalendarClock className="h-3 w-3" />
											<span className="text-xs">
												12 Aug 2024
											</span>
										</div>
									</div>
								</div>
								<Button
									variant="ghost"
									size="icon"
									className="h-8 w-8"
								>
									<ChevronDown className="h-4 w-4 text-gray-400" />
								</Button>
							</div>
						</div>

						<div className="flex items-center justify-between border-t border-gray-200 pt-4">
							<div className="flex items-center gap-2">
								<Button
									variant="outline"
									size="icon"
									className="h-8 w-8"
								>
									<Trash2 className="h-4 w-4 text-gray-600" />
								</Button>
								<Button
									variant="outline"
									size="icon"
									className="h-8 w-8"
								>
									<Edit className="h-4 w-4 text-gray-600" />
								</Button>
								<Button
									variant="outline"
									size="icon"
									className="h-8 w-8"
								>
									<FileText className="h-4 w-4 text-gray-600" />
								</Button>
							</div>
							<Button className="border border-gray-200 bg-white text-gray-900 hover:bg-gray-50">
								<RefreshCcw className="mr-2 h-4 w-4 text-green-600" />
								Reschedule
							</Button>
						</div>						
					</div>
				</div>
			)}
		</div>
	);
};
