import type { ReactNode } from "react";

export interface UploadedFile {
	id: string;
	name: string;
	size: number;
	type: string;
	url?: string;
	preview?: string;
}

export interface UploaderProps {
	/**
	 * Current uploaded files
	 */
	files?: UploadedFile[];
	/**
	 * Whether the uploader is in loading state
	 */
	isLoading?: boolean;
	/**
	 * Whether multiple files can be uploaded
	 */
	multiple?: boolean;
	/**
	 * Accepted file types
	 */
	accept?: string;
	/**
	 * Maximum file size in bytes
	 */
	maxFileSize?: number;
	/**
	 * Maximum number of files
	 */
	maxFiles?: number;
	/**
	 * Whether the uploader is disabled
	 */
	disabled?: boolean;
	/**
	 * Custom upload text
	 */
	uploadText?: string;
	/**
	 * Custom description text
	 */
	descriptionText?: string;
	/**
	 * Size variant
	 */
	size?: "sm" | "md" | "lg" | "default";
	/**
	 * Visual variant
	 */
	variant?: "default" | "compact" | "bordered";
	/**
	 * Custom className
	 */
	className?: string;
	/**
	 * Custom icon for upload area
	 */
	uploadIcon?: ReactNode;
	/**
	 * Callback when files are selected/dropped
	 */
	onFilesChange?: (files: File[]) => void;
	/**
	 * Callback when a file is removed
	 */
	onFileRemove?: (fileId: string) => void;
	/**
	 * Callback when a file is edited/renamed
	 */
	onFileEdit?: (fileId: string, newName: string) => void;
	/**
	 * Callback for upload error
	 */
	onError?: (error: string) => void;
}

export interface FileItemProps {
	file: UploadedFile;
	onRemove?: (fileId: string) => void;
	onEdit?: (fileId: string, newName: string) => void;
	className?: string;
}
