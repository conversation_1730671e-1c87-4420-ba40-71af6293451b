import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { forgotPassword, resetPassword } from '../../lib/api/auth';
import type {
	ForgotPasswordRequest,
	ForgotPasswordResponse,
	ResetPasswordRequest,
	ResetPasswordResponse,
} from "@/types/api/auth";

import { useUIStore } from '../uiStore';
import type { AxiosError } from 'axios';

// Forgot password mutation
export const useForgotPasswordSlice = () => {
  const navigate = useNavigate();
  const { addToast } = useUIStore();

  return useMutation<
  ForgotPasswordResponse,
  AxiosError,
  ForgotPasswordRequest
>({
    mutationFn: forgotPassword,
    onSuccess: () => {
      addToast({
        type: 'success',
        title: 'Email Sent',
        message: 'Password reset instructions have been sent to your email'
      });
      // Redirect to sign-in after 3 seconds
      setTimeout(() => {
        navigate('/sign-in');
      }, 3000);
    },
    onError: (error: any) => {
      addToast({
        type: 'error',
        title: 'Failed to Send Email',
        message: error.response?.data?.message || 'Failed to send password reset email'
      });
    },
  });
};

// Reset password mutation
export const useResetPasswordSlice = () => {
  const navigate = useNavigate();
  const { addToast } = useUIStore();

  return useMutation<
  ResetPasswordResponse,
  AxiosError,
  ResetPasswordRequest
>({
    mutationFn: resetPassword,
    onSuccess: () => {
      addToast({
        type: 'success',
        title: 'Password Reset Successful',
        message: 'Your password has been successfully reset'
      });
      // Redirect to sign-in after 2 seconds
      setTimeout(() => {
        navigate('/sign-in');
      }, 2000);
    },
    onError: (error: any) => {
      addToast({
        type: 'error',
        title: 'Password Reset Failed',
        message: error.response?.data?.message || 'Failed to reset password'
      });
    },
  });
}; 

