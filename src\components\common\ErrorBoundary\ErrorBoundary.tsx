import React, { Component } from "react";
import type { ErrorInfo, ReactNode } from "react";
import { Button } from "../../ui/button";

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
	onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
	hasError: boolean;
	error: Error | null;
	errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = { hasError: false, error: null, errorInfo: null };
	}

	static getDerivedStateFromError(error: Error): Partial<State> {
		// Update state so the next render will show the fallback UI
		return { hasError: true, error, errorInfo: null };
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo) {
		// Log the error
		console.error("ErrorBoundary caught an error:", error, errorInfo);

		// Update state with error info
		this.setState({
			error,
			errorInfo,
		});

		// Call optional error handler
		if (this.props.onError) {
			this.props.onError(error, errorInfo);
		}

		// You can also log the error to an error reporting service here
		// logErrorToService(error, errorInfo);
	}

	handleReset = () => {
		this.setState({ hasError: false, error: null, errorInfo: null });
	};

	render() {
		if (this.state.hasError) {
			// Custom fallback UI
			if (this.props.fallback) {
				return this.props.fallback;
			}

			// Default fallback UI
			return (
				<div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
					<div className="w-full max-w-md rounded-lg bg-white p-6 text-center shadow-lg">
						<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
							<svg
								className="h-6 w-6 text-red-600"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5C2.962 18.333 3.924 20 5.464 20z"
								/>
							</svg>
						</div>

						<h2 className="mb-2 text-lg font-semibold text-gray-900">
							Something went wrong
						</h2>

						<p className="mb-6 text-sm text-gray-600">
							We're sorry, but something unexpected happened.
							Please try refreshing the page or contact support if
							the problem persists.
						</p>

						<div className="flex flex-col space-y-3">
							<Button
								onClick={this.handleReset}
								className="w-full"
							>
								Try Again
							</Button>

							<Button
								variant="outline"
								onClick={() => window.location.reload()}
								className="w-full"
							>
								Refresh Page
							</Button>
						</div>

						{process.env.NODE_ENV === "development" &&
							this.state.error && (
								<details className="mt-6 text-left">
									<summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
										Error Details (Development Only)
									</summary>
									<div className="mt-2 max-h-40 overflow-auto rounded bg-gray-100 p-3 font-mono text-xs text-gray-800">
										<div className="mb-2">
											<strong>Error:</strong>{" "}
											{this.state.error.toString()}
										</div>
										{this.state.errorInfo && (
											<div>
												<strong>
													Component Stack:
												</strong>
												<pre className="mt-1 whitespace-pre-wrap">
													{
														this.state.errorInfo
															.componentStack
													}
												</pre>
											</div>
										)}
									</div>
								</details>
							)}
					</div>
				</div>
			);
		}

		return this.props.children;
	}
}

export default ErrorBoundary;
