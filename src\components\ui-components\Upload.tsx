import * as React from "react";
import { cn } from "@/lib/utils";
import { UploadedFileCard } from "./UploadFileCard";
import { Upload } from "lucide-react";

export interface UploadedFile {
	id: string;
	name: string;
	type?: string;
	size: string;
}

export interface UploadCardProps extends React.HTMLAttributes<HTMLDivElement> {
	title?: string;
	description?: string;
	icon?: React.ReactNode;
	buttonText?: string;
	variant?: "centered" | "horizontal" | "horizontal-compact" | "multi-file";
	width?: string;
	onBrowseClick?: () => void;
	onDragOver?: (e: React.DragEvent) => void;
	onDrop?: (e: React.DragEvent) => void;
	accept?: string;
	maxSize?: string;
	//For Single file upload state
	isUploaded?: boolean;
	fileName?: string;
	fileSize?: string;
	filePreview?: string;
	onRemove?: () => void;
	onChange?: () => void;
	//For Multi-file upload state
	uploadedFiles?: UploadedFile[];
	onRemoveFile?: (fileId: string) => void;
}

const UploadCard = React.forwardRef<HTMLDivElement, UploadCardProps>(
	(
		{
			className,
			title = "Click or drag file here to upload file",
			description = "Recommended file type: .svg, .png, .jpg (Max of 10 mb)",
			icon,
			buttonText = "Browse Files",
			variant = "centered",
			width = "w-[480px]",
			onBrowseClick,
			onDragOver,
			onDrop,
			accept,
			maxSize,
			isUploaded = false,
			fileName,
			fileSize,
			filePreview,
			onRemove,
			onChange,
			uploadedFiles = [],
			onRemoveFile,
			...props
		},
		ref
	) => {
		const defaultIcon = <Upload className="h-5 w-5 text-[#27272A]" />;
		const iconElement = (
			<div className="flex h-10 w-10 items-center justify-center gap-2.5 overflow-hidden rounded-full bg-[#FAFAFA] p-2.5">
				{icon || defaultIcon}
			</div>
		);

		const textContent = (
			<div
				className={cn(
					"flex flex-col justify-start gap-1",
					variant === "centered" && "items-center self-stretch",
					variant === "horizontal" && "flex-1 items-center",
					variant === "horizontal-compact" && "flex-1 items-start",
					variant === "multi-file" && "items-center self-stretch"
				)}
			>
				<div
					className={cn(
						"text-sm leading-tight font-semibold text-[#27272A]",
						variant === "centered" &&
							"justify-start self-stretch text-center",
						variant === "horizontal" &&
							"justify-start self-stretch",
						variant === "horizontal-compact" && "justify-start",
						variant === "multi-file" &&
							"justify-start self-stretch text-center"
					)}
				>
					{title}
				</div>
				<div
					className={cn(
						"text-[10px] leading-3 font-normal text-[#71717A]",
						variant === "centered" &&
							"justify-start self-stretch text-center",
						variant === "horizontal" &&
							"justify-start self-stretch",
						variant === "horizontal-compact" &&
							"justify-start self-stretch",
						variant === "multi-file" &&
							"justify-start self-stretch text-center"
					)}
				>
					{description}
				</div>
			</div>
		);

		const browseButton = (
			<div
				data-show-left-icon="false"
				data-show-right-icon="false"
				data-size="default"
				data-state="Default"
				data-variant="Outline"
				className={cn(
					"flex h-9 cursor-pointer items-center justify-center gap-2 rounded-md bg-white px-4 py-2 outline outline-1 outline-offset-[-1px] outline-[#E4E4E7] transition-colors hover:bg-gray-50",
					variant === "centered" && "self-stretch",
					variant === "horizontal-compact" && "flex-shrink-0",
					variant === "multi-file" && "w-28"
				)}
				onClick={onBrowseClick}
			>
				<div className="justify-center text-xs leading-none font-medium text-[#27272A]">
					{buttonText}
				</div>
			</div>
		);

		const renderMultiFileList = () => {
			if (uploadedFiles.length === 0) return null;

			return (
				<div className="flex flex-col items-start justify-start gap-3">
					{uploadedFiles.map((file) => (
						<UploadedFileCard
							key={file.id}
							fileName={file.name}
							fileType={file.type}
							fileSize={file.size}
							onRemove={() => onRemoveFile?.(file.id)}
							className="w-[453px] border-[#E4E4E7]"
						/>
					))}
				</div>
			);
		};

		const renderContent = () => {
			if (variant === "multi-file") {
				return (
					<>
						{renderMultiFileList()}
						<div className="flex flex-col items-center justify-start gap-1 self-stretch">
							{textContent}
						</div>
						{browseButton}
					</>
				);
			}

			if (isUploaded) {
				console.log(
					"UploadCard: isUploaded=true, filePreview=",
					filePreview
				);

				const fileImage = filePreview ? (
					<img
						data-image="True"
						data-round="On"
						className="relative h-10 w-10 rounded-full object-cover"
						src={filePreview}
						alt="File preview"
						onLoad={() =>
							console.log(
								"Image loaded successfully:",
								filePreview
							)
						}
						onError={(e) => {
							console.error(
								"Image failed to load:",
								filePreview,
								e
							);
						}}
					/>
				) : (
					<div className="relative flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
						<span className="text-xs text-gray-500">IMG</span>
					</div>
				);

				const fileInfo = (
					<div
						className={cn(
							"flex flex-col justify-start gap-1",
							variant === "centered" &&
								"items-center self-stretch",
							variant === "horizontal" && "flex-1 items-center",
							variant === "horizontal-compact" &&
								"flex-1 items-center"
						)}
					>
						<div
							className={cn(
								"text-sm leading-tight font-semibold text-[#27272A]",
								variant === "centered" &&
									"justify-start self-stretch text-center",
								(variant === "horizontal" ||
									variant === "horizontal-compact") &&
									"justify-start self-stretch"
							)}
						>
							{fileName || "[filename].[type]"}
						</div>
						<div
							className={cn(
								"text-[10px] leading-3 font-normal text-[#71717A]",
								variant === "centered" &&
									"justify-start self-stretch text-center",
								(variant === "horizontal" ||
									variant === "horizontal-compact") &&
									"justify-start self-stretch"
							)}
						>
							{fileSize || "File size: 10mb"}
						</div>
					</div>
				);

				const actionButtons = (
					<div className="flex items-center justify-start gap-2">
						<div
							data-show-left-icon="false"
							data-show-right-icon="false"
							data-size="default"
							data-state="Default"
							data-variant="Destructive"
							className="flex h-9 w-28 cursor-pointer items-center justify-center gap-2 rounded-md bg-red-600 px-4 py-2 transition-opacity hover:opacity-90"
							onClick={onRemove}
						>
							<div className="justify-center text-xs leading-none font-medium text-white">
								Remove
							</div>
						</div>
						<div
							data-show-left-icon="false"
							data-show-right-icon="false"
							data-size="default"
							data-state="Default"
							data-variant="Outline"
							className="flex h-9 w-28 cursor-pointer items-center justify-center gap-2 rounded-md bg-white px-4 py-2 outline outline-1 outline-offset-[-1px] outline-[#E4E4E7] transition-colors hover:bg-gray-50"
							onClick={onChange}
						>
							<div className="justify-center text-xs leading-none font-medium text-[#27272A]">
								Change
							</div>
						</div>
					</div>
				);

				if (variant === "centered") {
					return (
						<>
							{fileImage}
							{fileInfo}
							{actionButtons}
						</>
					);
				} else {
					return (
						<>
							{fileImage}
							{fileInfo}
							{actionButtons}
						</>
					);
				}
			}

			//For Empty state (main upload UI)
			switch (variant) {
				case "centered":
					return (
						<>
							{iconElement}
							{textContent}
							{browseButton}
						</>
					);
				case "horizontal":
					return (
						<>
							<div className="inline-flex items-center justify-start gap-6 self-stretch">
								{iconElement}
								{textContent}
							</div>
							{browseButton}
						</>
					);
				case "horizontal-compact":
					return (
						<>
							{iconElement}
							{textContent}
							{browseButton}
						</>
					);
				default:
					return null;
			}
		};

		return (
			<div
				ref={ref}
				className={cn(
					"rounded-lg p-6 outline outline-1 outline-offset-[-1px] outline-[#E4E4E7]",
					width,
					!isUploaded &&
						variant === "centered" &&
						"inline-flex flex-col items-center justify-start gap-6",
					!isUploaded &&
						variant === "horizontal" &&
						"inline-flex flex-col items-center justify-start gap-5",
					!isUploaded &&
						variant === "horizontal-compact" &&
						"inline-flex items-start justify-center gap-6",
					variant === "multi-file" &&
						"inline-flex flex-col items-center justify-start gap-6 bg-[#F4F4F5]",
					isUploaded &&
						variant === "centered" &&
						"inline-flex flex-col items-center justify-start gap-6",
					isUploaded &&
						(variant === "horizontal" ||
							variant === "horizontal-compact") &&
						"inline-flex items-start justify-center gap-6",
					className
				)}
				onDragOver={onDragOver}
				onDrop={onDrop}
				{...props}
			>
				{renderContent()}
			</div>
		);
	}
);

UploadCard.displayName = "UploadCard";

export { UploadCard };
