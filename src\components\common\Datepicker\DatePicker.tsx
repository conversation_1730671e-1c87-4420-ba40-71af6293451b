import React, { useState, forwardRef } from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

interface DateRange {
	from: Date | undefined;
	to: Date | undefined;
}

interface DatePickerProps {
	value?: Date | DateRange | Date[];
	onChange?: (date: Date | DateRange | Date[] | undefined) => void;
	placeholder?: string;
	disabled?: boolean;
	className?: string;

	variant?:
		| "default" // Basic date picker with popover
		| "range" // Date range picker
		| "multiple" // Multiple date selection
		| "with-presets" // Date picker with preset options
		| "form" // Form-styled date picker
		| "dropdown-years" // With dropdown year selection
		| "time" // Date and time picker
		| "advanced" // Advanced with text input support
		| "inline" // Always visible calendar
		| "input-only"; // Native date input only

	size?: "sm" | "md" | "lg";
	mode?: "single" | "range" | "multiple";
	selected?: Date | DateRange | Date[];
	onSelect?: (date: Date | DateRange | Date[] | undefined) => void;

	minDate?: Date;
	maxDate?: Date;
	disabledDates?: (date: Date) => boolean;
	fromDate?: Date;
	toDate?: Date;

	numberOfMonths?: number;
	showOutsideDays?: boolean;
	fixedWeeks?: boolean;
	weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;

	presets?: Array<{
		label: string;
		value: Date | DateRange;
	}>;

	// Advanced options
	enableTextInput?: boolean;
	timeFormat?: "12h" | "24h";

	// Accessibility
	"aria-label"?: string;
	"aria-describedby"?: string;
}

// Default presets for range picker
const defaultRangePresets = [
	{
		label: "Today",
		value: { from: new Date(), to: new Date() },
	},
	{
		label: "Yesterday",
		value: {
			from: new Date(Date.now() - 24 * 60 * 60 * 1000),
			to: new Date(Date.now() - 24 * 60 * 60 * 1000),
		},
	},
	{
		label: "Last 7 days",
		value: {
			from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
			to: new Date(),
		},
	},
	{
		label: "Last 30 days",
		value: {
			from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
			to: new Date(),
		},
	},
	{
		label: "This month",
		value: {
			from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
			to: new Date(
				new Date().getFullYear(),
				new Date().getMonth() + 1,
				0
			),
		},
	},
	{
		label: "Last month",
		value: {
			from: new Date(
				new Date().getFullYear(),
				new Date().getMonth() - 1,
				1
			),
			to: new Date(new Date().getFullYear(), new Date().getMonth(), 0),
		},
	},
];

export const DatePicker = forwardRef<HTMLInputElement, DatePickerProps>(
	(
		{
			variant = "default",
			size = "md",
			value,
			onChange,
			placeholder = "Pick a date",
			disabled = false,
			className,
			mode,
			selected,
			onSelect,
			minDate,
			maxDate,
			disabledDates,
			fromDate,
			toDate,
			numberOfMonths = 1,
			showOutsideDays = true,
			fixedWeeks = false,
			weekStartsOn = 0,
			presets,
			enableTextInput = false,
			timeFormat = "12h",
			"aria-label": ariaLabel,
			"aria-describedby": ariaDescribedBy,
			...props
		},
		ref
	) => {
		const [isOpen, setIsOpen] = useState(false);
		const [textInput, setTextInput] = useState("");
		const [selectedYear, setSelectedYear] = useState(
			new Date().getFullYear()
		);
		const [selectedMonth, setSelectedMonth] = useState(
			new Date().getMonth()
		);

		// Determine the actual mode based on variant
		const actualMode =
			mode ||
			(variant === "range" || variant === "with-presets"
				? "range"
				: variant === "multiple"
					? "multiple"
					: "single");

		// Use provided presets or defaults for range picker
		const activePresets =
			variant === "with-presets" ? presets || defaultRangePresets : [];

		// Format display value based on variant
		const getDisplayValue = () => {
			const dateValue = selected || value;

			if (
				(variant === "range" || variant === "with-presets") &&
				dateValue &&
				typeof dateValue === "object" &&
				"from" in dateValue
			) {
				const range = dateValue as DateRange;
				if (range.from && range.to) {
					return `${format(range.from, "LLL dd, y")} - ${format(range.to, "LLL dd, y")}`;
				}
				if (range.from) {
					return format(range.from, "LLL dd, y");
				}
			}

			if (variant === "multiple" && Array.isArray(dateValue)) {
				return dateValue.length > 0
					? `${dateValue.length} dates selected`
					: "";
			}

			if (dateValue instanceof Date) {
				return variant === "time"
					? format(
							dateValue,
							`LLL dd, y 'at' ${timeFormat === "12h" ? "h:mm a" : "HH:mm"}`
						)
					: format(dateValue, "LLL dd, y");
			}

			return "";
		};

		// Handle date selection
		const handleDateSelect = (
			date: Date | DateRange | Date[] | undefined
		) => {
			onChange?.(date);
			onSelect?.(date);

			// Close popover for single date or completed range
			if (
				variant === "default" ||
				variant === "form" ||
				variant === "dropdown-years" ||
				variant === "advanced" ||
				((variant === "range" || variant === "with-presets") &&
					date &&
					typeof date === "object" &&
					"to" in date &&
					date.to)
			) {
				setIsOpen(false);
			}
		};

		// Handle preset selection
		const handlePresetSelect = (preset: {
			label: string;
			value: Date | DateRange;
		}) => {
			handleDateSelect(preset.value);
		};

		// Handle text input parsing (for advanced variant)
		const handleTextInputChange = (
			e: React.ChangeEvent<HTMLInputElement>
		) => {
			const inputValue = e.target.value;
			setTextInput(inputValue);

			// Try to parse natural language dates
			try {
				const parsedDate = new Date(inputValue);
				if (!isNaN(parsedDate.getTime())) {
					onChange?.(parsedDate);
				}
			} catch (error) {
				// Ignore parsing errors
			}
		};

		// Size classes
		const sizeClasses = {
			sm: "h-8 px-2 text-xs",
			md: "h-9 px-3 text-sm",
			lg: "h-10 px-4 text-base",
		};

		// Get calendar props based on mode
		const getCalendarProps = (additionalProps = {}) => {
			const baseProps = {
				mode: actualMode,
				selected: selected || value,
				onSelect: handleDateSelect,
				disabled:
					disabledDates || minDate || maxDate
						? (date: Date) => {
								if (disabledDates && disabledDates(date))
									return true;
								if (minDate && date < minDate) return true;
								if (maxDate && date > maxDate) return true;
								if (fromDate && date < fromDate) return true;
								if (toDate && date > toDate) return true;
								return false;
							}
						: undefined,
				numberOfMonths:
					variant === "range" || variant === "with-presets"
						? Math.max(numberOfMonths, 2)
						: numberOfMonths,
				showOutsideDays,
				fixedWeeks,
				weekStartsOn,
				initialFocus: true,
				...additionalProps,
			};

			return baseProps as any;
		};

		// Generate year options for dropdown
		const currentYear = new Date().getFullYear();
		const yearOptions = Array.from(
			{ length: 21 },
			(_, i) => currentYear - 10 + i
		);

		// Render based on variant
		switch (variant) {
			case "inline":
				return (
					<div className={cn("p-0", className)}>
						<Calendar {...getCalendarProps()} />
					</div>
				);

			case "input-only":
				return (
					<Input
						ref={ref}
						type="date"
						value={
							value instanceof Date
								? format(value, "yyyy-MM-dd")
								: ""
						}
						onChange={(e) => {
							const date = e.target.value
								? new Date(e.target.value)
								: undefined;
							onChange?.(date);
						}}
						placeholder={placeholder}
						disabled={
							typeof disabled === "boolean" ? disabled : false
						}
						className={cn(sizeClasses[size], className)}
						aria-label={ariaLabel}
						aria-describedby={ariaDescribedBy}
						{...props}
					/>
				);

			case "with-presets":
				return (
					<Popover open={isOpen} onOpenChange={setIsOpen}>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-full justify-start text-left font-normal",
									sizeClasses[size],
									!getDisplayValue() &&
										"text-muted-foreground",
									className
								)}
								disabled={
									typeof disabled === "boolean"
										? disabled
										: false
								}
								aria-label={ariaLabel}
								aria-describedby={ariaDescribedBy}
							>
								<CalendarIcon className="mr-2 h-4 w-4" />
								{getDisplayValue() || placeholder}
							</Button>
						</PopoverTrigger>
						<PopoverContent
							className="z-[1050] w-auto p-0"
							align="start"
						>
							<div className="flex">
								<div className="border-r">
									<div className="p-3">
										<div className="space-y-1">
											{activePresets.map(
												(preset, index) => (
													<Button
														key={index}
														variant="ghost"
														className="w-full justify-start font-normal"
														onClick={() =>
															handlePresetSelect(
																preset
															)
														}
													>
														{preset.label}
													</Button>
												)
											)}
										</div>
									</div>
								</div>
								<Calendar {...getCalendarProps()} />
							</div>
						</PopoverContent>
					</Popover>
				);

			case "form":
				return (
					<div className="flex flex-col gap-2">
						<Label htmlFor="date-picker">
							{ariaLabel || "Date"}
						</Label>
						<Popover open={isOpen} onOpenChange={setIsOpen}>
							<PopoverTrigger asChild>
								<Button
									id="date-picker"
									variant="outline"
									className={cn(
										"w-full justify-start text-left font-normal",
										sizeClasses[size],
										!getDisplayValue() &&
											"text-muted-foreground",
										className
									)}
									disabled={
										typeof disabled === "boolean"
											? disabled
											: false
									}
									aria-describedby={ariaDescribedBy}
								>
									<CalendarIcon className="mr-2 h-4 w-4" />
									{getDisplayValue() || placeholder}
								</Button>
							</PopoverTrigger>
							<PopoverContent
								className="z-[1050] w-auto p-0"
								align="start"
							>
								<Calendar {...getCalendarProps()} />
							</PopoverContent>
						</Popover>
					</div>
				);

			case "dropdown-years":
				return (
					<Popover open={isOpen} onOpenChange={setIsOpen}>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-full justify-between text-left font-normal",
									sizeClasses[size],
									!getDisplayValue() &&
										"text-muted-foreground",
									className
								)}
								disabled={
									typeof disabled === "boolean"
										? disabled
										: false
								}
								aria-label={ariaLabel}
								aria-describedby={ariaDescribedBy}
							>
								{getDisplayValue() || placeholder}
								<ChevronDown className="ml-2 h-4 w-4 opacity-50" />
							</Button>
						</PopoverTrigger>
						<PopoverContent
							className="z-[1050] w-auto p-0"
							align="start"
						>
							<div className="flex items-center justify-between border-b p-2">
								{/* // Ensure year is string and research  */}
								<Select
									value={selectedYear.toString()}
									onValueChange={(year: any) =>
										setSelectedYear(parseInt(year))
									}
								>
									<SelectTrigger className="w-24">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										{yearOptions.map((year) => (
											<SelectItem
												key={year}
												value={year.toString()}
											>
												{year}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								{/* // Ensure month is string and research  */}
								<Select
									value={selectedMonth.toString()}
									onValueChange={(month: any) =>
										setSelectedMonth(parseInt(month))
									}
								>
									<SelectTrigger className="w-32">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										{Array.from({ length: 12 }, (_, i) => (
											<SelectItem
												key={i}
												value={i.toString()}
											>
												{format(
													new Date(2024, i, 1),
													"MMMM"
												)}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<Calendar
								{...getCalendarProps({
									month: new Date(
										selectedYear,
										selectedMonth
									),
									onMonthChange: (month: Date) => {
										setSelectedYear(month.getFullYear());
										setSelectedMonth(month.getMonth());
									},
								})}
							/>
						</PopoverContent>
					</Popover>
				);

			case "advanced":
				return (
					<div className="space-y-2">
						{enableTextInput && (
							<Input
								value={textInput}
								onChange={handleTextInputChange}
								placeholder="Type a date (e.g., 'tomorrow', 'next Friday')"
								className={cn(sizeClasses[size])}
							/>
						)}
						<Popover open={isOpen} onOpenChange={setIsOpen}>
							<PopoverTrigger asChild>
								<Button
									variant="outline"
									className={cn(
										"w-full justify-start text-left font-normal",
										sizeClasses[size],
										!getDisplayValue() &&
											"text-muted-foreground",
										className
									)}
									disabled={
										typeof disabled === "boolean"
											? disabled
											: false
									}
									aria-label={ariaLabel}
									aria-describedby={ariaDescribedBy}
								>
									<CalendarIcon className="mr-2 h-4 w-4" />
									{getDisplayValue() || placeholder}
								</Button>
							</PopoverTrigger>
							<PopoverContent
								className="z-[1050] w-auto p-0"
								align="start"
							>
								<Calendar {...getCalendarProps()} />
							</PopoverContent>
						</Popover>
					</div>
				);

			case "time":
				return (
					<Popover open={isOpen} onOpenChange={setIsOpen}>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-full justify-start text-left font-normal",
									sizeClasses[size],
									!getDisplayValue() &&
										"text-muted-foreground",
									className
								)}
								disabled={
									typeof disabled === "boolean"
										? disabled
										: false
								}
								aria-label={ariaLabel}
								aria-describedby={ariaDescribedBy}
							>
								<CalendarIcon className="mr-2 h-4 w-4" />
								{getDisplayValue() || placeholder}
							</Button>
						</PopoverTrigger>
						<PopoverContent
							className="z-[1050] w-auto p-0"
							align="start"
						>
							<div className="p-3">
								<Calendar {...getCalendarProps()} />
								<div className="mt-3 border-t pt-3">
									<Label htmlFor="time">Time</Label>
									<Input
										id="time"
										type="time"
										className="mt-1"
										onChange={(e) => {
											if (
												value instanceof Date &&
												e.target.value
											) {
												const [hours, minutes] =
													e.target.value.split(":");
												const newDate = new Date(value);
												newDate.setHours(
													parseInt(hours),
													parseInt(minutes)
												);
												onChange?.(newDate);
											}
										}}
									/>
								</div>
							</div>
						</PopoverContent>
					</Popover>
				);

			default: // 'default' variant
				return (
					<Popover open={isOpen} onOpenChange={setIsOpen}>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-full justify-start text-left font-normal",
									sizeClasses[size],
									!getDisplayValue() &&
										"text-muted-foreground",
									className
								)}
								disabled={
									typeof disabled === "boolean"
										? disabled
										: false
								}
								aria-label={ariaLabel}
								aria-describedby={ariaDescribedBy}
							>
								<CalendarIcon className="mr-2 h-4 w-4" />
								{getDisplayValue() || placeholder}
							</Button>
						</PopoverTrigger>
						<PopoverContent
							className="z-[1050] w-auto p-0"
							align="start"
						>
							<Calendar {...getCalendarProps()} />
						</PopoverContent>
					</Popover>
				);
		}
	}
);

DatePicker.displayName = "DatePicker";

// Export the component and types
export type { DatePickerProps, DateRange };
