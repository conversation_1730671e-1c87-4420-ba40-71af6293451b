import React, {type JSX} from "react";
import { createEditor } from "lexical";
import { ListNode, ListItemNode } from "@lexical/list";
import { HeadingNode } from "@lexical/rich-text";

interface LexicalNode {
	type: string;
	children?: LexicalNode[];
	format?: number | string;
	text?: string;
	tag?: string;
	detail?: number;
	mode?: string;
	style?: string;
	direction?: string;
	indent?: number;
	version?: number;
	listType?: string;
	start?: number;
	value?: number;
}

interface LexicalEditorState {
	root: {
		children: LexicalNode[];
		direction?: string;
		format?: string;
		indent?: number;
		type: string;
		version?: number;
	};
}

interface LexicalRendererProps {
	value: LexicalEditorState | string;
	className?: string;
}

const LexicalRenderer: React.FC<LexicalRendererProps> = ({
	value,
	className = "",
}) => {
	// if (!value) return null;
	React.useEffect(() => {
		if (editorState) {
			editor.setEditorState(
				editor.parseEditorState(JSON.stringify(editorState))
			);
		}
	}, [value]);

	let editorState: LexicalEditorState;
	try {
		editorState = typeof value === "string" ? JSON.parse(value) : value;
	} catch (e) {
		console.error("Failed to parse editor state:", e);
		return null;
	}

	// Initialize Lexical editor config
	const editor = createEditor({
		namespace: "LexicalRenderer",
		nodes: [HeadingNode, ListNode, ListItemNode],
		onError: (error: Error) => {
			console.error(error);
		},
	});

	const renderTextContent = (node: LexicalNode): JSX.Element => {
		const textClassName =
			node.format === 1 ? "font-bold" : node.format === 2 ? "italic" : "";
		return <span className={textClassName}>{node.text}</span>;
	};

	const renderNode = (
		node: LexicalNode
	): JSX.Element | JSX.Element[] | null => {
		switch (node.type) {
			case "heading":
				return (
					<h3 className="mb-4 text-xl font-bold">
						{node.children?.map((child, index) => (
							<React.Fragment key={index}>
								{renderTextContent(child)}
							</React.Fragment>
						))}
					</h3>
				);

			case "list":
				return (
					<ul className="list-disc space-y-2 pl-6">
						{node.children?.map((listItem, index) => (
							<li key={index}>
								{listItem.children?.map(
									(textNode, textIndex) => (
										<React.Fragment key={textIndex}>
											{renderTextContent(textNode)}
										</React.Fragment>
									)
								)}
							</li>
						))}
					</ul>
				);

			case "text":
				return renderTextContent(node);

			default:
				if (node.children) {
					return node.children.map((child, index) => (
						<React.Fragment key={index}>
							{renderNode(child)}
						</React.Fragment>
					));
				}
				return null;
		}
	};

	return (
		<div className={`mx-auto max-w-2xl p-6 ${className}`.trim()}>
			{editorState && renderNode(editorState.root)}
		</div>
	);
};

export default LexicalRenderer;
