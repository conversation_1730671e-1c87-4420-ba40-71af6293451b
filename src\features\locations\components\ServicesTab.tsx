import { useEffect, useState } from "react";
import { Search, Plus, MapPin, Upload, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { AddServiceSheet, ServiceFilterSheet, ServiceInfoSheet } from "./sheets";
import { useServices } from "../hooks/useServices";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import type { ServiceData } from "../api/servicesApi";
import LocationDetailsSheet from "./sheets/location-details/LocationDetailsSheet";
import { ServiceCard } from "./service/ServiceCard";
import { Skeleton } from "@/components/ui/skeleton";
import { useDebounce } from "@/hooks/useDebounce";

interface ServicesTabProps {
	className?: string;
}

export function ServicesTab({ className }: ServicesTabProps) {
	const { organizationId } = useOrganizationContext();
	const [selectedServices, setSelectedServices] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [appliedFilters, setAppliedFilters] = useState<Record<string, any>>(
		{}
	);
	const [showAddServiceForm, setShowAddServiceForm] = useState(false);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showServiceInfo, setShowServiceInfo] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [selectedService, setSelectedService] = useState<ServiceData | null>(
		null
	);

	// Debounce search term to avoid too many API calls
	const debouncedSearchTerm = useDebounce(searchTerm, 300);

	// Combine search and filters for API call
	const apiFilters = {
		...appliedFilters,
		...(debouncedSearchTerm && { search: debouncedSearchTerm }),
	};

	// Fetch services using React Query with filters and search
	const {
		data: servicesResponse,
		isLoading,
		error: servicesError,
		refetch,
	} = useServices({
		organizationId: organizationId || undefined,
		enabled: !!organizationId,
		filters: apiFilters,
	});

	const services = servicesResponse?.data || [];

	// No need for client-side filtering since search is handled by API
	const filteredServices = services;

	const handleSelectAll = (checked: boolean) => {
		if (checked && filteredServices.length > 0) {
			setSelectedServices(
				filteredServices.map((service) => service.id.toString())
			);
		} else {
			setSelectedServices([]);
		}
	};

	const handleServiceSelection = (serviceId: string, selected: boolean) => {
		if (selected) {
			setSelectedServices((prev) => [...prev, serviceId]);
		} else {
			setSelectedServices((prev) =>
				prev.filter((id) => id !== serviceId)
			);
		}
	};

	const handleAddService = async (data?: any) => {
		// Refetch services after adding a new one
		await refetch();
		setShowAddServiceForm(false);
	};

	const handleViewService = (service: ServiceData) => {
		setSelectedService(service);
		setShowServiceInfo(true);
	};

	const handleEditService = (service: ServiceData) => {
		setSelectedService(service);
		setShowServiceInfo(false);
		// You can open an edit form here
		console.log("Edit service:", service);
	};

	const handleApplyFilters = (filterData: any) => {
		console.log("Applying filters:", filterData);

		// Transform filter data to API query parameters
		const apiFilters: Record<string, any> = {};

		// Add location filters
		if (filterData.locations && filterData.locations.length > 0) {
			apiFilters.location = filterData.locations;
		}

		// Add provider/station filters
		if (filterData.providers && filterData.providers.length > 0) {
			apiFilters.station = filterData.providers;
		}

		// Add method filters
		if (
			filterData.availableMethods &&
			filterData.availableMethods.length > 0
		) {
			apiFilters.methods = filterData.availableMethods;
		}

		// Add status filters
		if (filterData.status && filterData.status.length > 0) {
			apiFilters.status = filterData.status;
		}

		// Add sorting (default to name)
		apiFilters.sort_by = "name";

		// Add date range filters if needed
		// apiFilters.from = "2023-01-01";
		// apiFilters.to = "2023-12-31";

		console.log("Transformed API filters:", apiFilters);
		setAppliedFilters(apiFilters);
	};

	const handleClearFilters = () => {
		setAppliedFilters({});
		setSearchTerm("");
	};

	// Check if any filters or search are applied
	const hasActiveFilters =
		Object.keys(appliedFilters).length > 0 || debouncedSearchTerm;

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-3 pl-4">
				<h1 className="text-2xl font-bold">Services</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className={`cursor-pointer ${
							hasActiveFilters
								? "border-primary bg-primary/10 text-primary"
								: ""
						}`}
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					{hasActiveFilters && (
						<Button
							variant="ghost"
							size="sm"
							onClick={handleClearFilters}
							className="text-gray-500 hover:text-gray-700"
						>
							Clear Filters
						</Button>
					)}
					<Button
						variant="outline"
						className="cursor-pointer"
						onClick={() => setShowAddServiceForm(true)}
					>
						<Upload className="mr-2 h-4 w-4" />
						Import CSV
					</Button>
					<Button
						variant="outline"
						className="bg-primary hover:bg-primary/90 cursor-pointer text-white hover:text-white"
						onClick={() => setShowAddServiceForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add a Service
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="flex w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
				<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4">
					<div className="flex items-center pr-4">
						<Checkbox
							label=""
							checked={
								filteredServices.length > 0 &&
								selectedServices.length ===
									filteredServices.length
							}
							className="cursor-pointer"
							onCheckedChange={handleSelectAll}
							disabled={isLoading}
						/>
					</div>
					<div className="flex flex-2 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Service Name</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Status</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Forms</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Auto Approve</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Time</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p></p>
						</div>
					</div>
				</div>

				{/* Services Content */}
				{isLoading ? (
					<div className="flex flex-col gap-0.5">
						{Array.from({ length: 5 }).map((_, index) => (
							<div
								key={index}
								className="flex h-16 items-center justify-between border-b border-gray-100 py-2 pl-4"
							>
								<div className="flex items-center pr-4">
									<Skeleton className="h-4 w-4" />
								</div>
								<div className="flex flex-2 items-center px-3">
									<div className="space-y-1">
										<Skeleton className="h-3 w-48" />
									</div>
								</div>
								<div className="flex flex-1 items-center px-3">
									<Skeleton className="h-5 w-16 rounded-full" />
								</div>
								<div className="flex flex-1 items-center px-3">
									<Skeleton className="h-4 w-12" />
								</div>
								<div className="flex flex-1 items-center px-3">
									<Skeleton className="h-4 w-16" />
								</div>
								<div className="flex flex-1 items-center px-3">
									<Skeleton className="h-4 w-20" />
								</div>
								<div className="flex flex-1 items-center px-3">
									<Skeleton className="h-8 w-8" />
								</div>
							</div>
						))}
					</div>
				) : servicesError ? (
					<div className="py-12 text-center">
						<div className="text-red-500">
							Error loading services
						</div>
					</div>
				) : (
					servicesResponse && (
						<>
							{filteredServices.length === 0 ? (
								<div className="py-12 text-center">
									<MapPin className="mx-auto h-12 w-12 text-gray-400" />
									<h3 className="mt-2 text-sm font-medium text-gray-900">
										No service found
									</h3>
									<p className="mt-1 text-sm text-gray-500">
										Get started by creating your first
										service.
									</p>
									<Button
										className="mt-4"
										onClick={() =>
											setShowAddServiceForm(true)
										}
									>
										<Plus className="mr-2 h-4 w-4" />
										Add a Service
									</Button>
								</div>
							) : (
								<div className="flex flex-col gap-0.5">
									{filteredServices.map(
										(service: ServiceData) => (
											<ServiceCard
												key={service.id}
												service={service}
												isSelected={selectedServices.includes(
													service.id.toString()
												)}
												onSelectionChange={(selected) =>
													handleServiceSelection(
														service.id.toString(),
														selected
													)
												}
												onEdit={() =>
													console.log(
														"Edit service:",
														service.id
													)
												}
												onView={() =>
													handleViewService(service)
												}
											/>
										)
									)}
								</div>
							)}

							{/* Note: Pagination removed as services API doesn't include pagination yet */}
						</>
					)
				)}
			</div>

			{/* Add Service Sheet */}
			<AddServiceSheet
				open={showAddServiceForm}
				onOpenChange={setShowAddServiceForm}
				onSubmit={handleAddService}
			/>

			{/* Service Details Sheet */}
			<LocationDetailsSheet
				open={showLocationDetails}
				onClose={() => setShowLocationDetails(false)}
			/>

			{/* Service Info Sheet */}
			<ServiceInfoSheet
				open={showServiceInfo}
				onOpenChange={setShowServiceInfo}
				service={selectedService}
				onEdit={handleEditService}
			/>

			{/* Filter Sheet */}
			<ServiceFilterSheet
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
			/>
		</div>
	);
}
