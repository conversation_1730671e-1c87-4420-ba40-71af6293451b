import React from "react";
import { QRCodeSVG } from "qrcode.react";
import { cn } from "@/lib/utils";
import type { QRCodeProps, QRCodeSize } from "./types";

const sizeMap: Record<QRCodeSize, number> = {
	sm: 96,
	md: 128,
	lg: 192,
	xl: 256,
};

const QRCode = React.forwardRef<SVGSVGElement, QRCodeProps>(
	(
		{
			value,
			size = "md",
			title,
			bgColor = "#ffffff",
			fgColor = "#000000",
			level = "L",
			className,
			includeMargin = true,
			...props
		},
		ref
	) => {
		const qrSize = typeof size === "number" ? size : sizeMap[size];

		return (
			<div className={cn("inline-block", className)}>
				<QRCodeSVG
					ref={ref}
					value={value}
					size={qrSize}
					title={title}
					bgColor={bgColor}
					fgColor={fgColor}
					level={level}
					{...props}
				/>
			</div>
		);
	}
);

QRCode.displayName = "QRCode";

export { QRCode };
