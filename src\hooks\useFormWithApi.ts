import { useForm } from 'react-hook-form'
import type { UseFormProps, FieldValues } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useApiForm } from './useApiForm'

interface UseFormWithApiProps<T extends FieldValues> extends UseFormProps<T> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  schema: any
  showSuccessToast?: boolean
  showErrorToast?: boolean
  customErrorMessages?: Record<number, string>
}

export const useFormWithApi = <T extends FieldValues>({
  schema,
  showSuccessToast = true,
  showErrorToast = true,
  customErrorMessages = {},
  ...formProps
}: UseFormWithApiProps<T>) => {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    ...formProps
  })

  const { handleApiError, handleApiSuccess } = useApiForm({
    setError: form.setError,
    showSuccessToast,
    showErrorToast,
    customErrorMessages
  })

  return {
    ...form,
    handleApiError,
    handleApiSuccess
  }
} 