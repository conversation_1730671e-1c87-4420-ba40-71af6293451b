import { useState } from "react";
import { ChevronLeft, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/ui/accordion";

export interface SelectableItem {
	id: string;
	name: string;
	description?: string;
}

export interface SelectableGroup {
	id: string;
	name: string;
	description?: string;
	items: SelectableItem[];
}

export interface MultiSelectAccordionProps {
	groups: SelectableGroup[];
	selectedGroupIds: Set<string>;
	selectedItemIds: Set<string>;
	onGroupSelectionChange: (groupId: string, checked: boolean) => void;
	onItemSelectionChange: (itemId: string, checked: boolean) => void;
	onBack?: () => void;

	// Text customization
	allItemsTitle?: string;
	allItemsSubtitle?: string;
	allItemsDescription?: string;
	selectedItemsTitle?: string;
	selectedItemsSubtitle?: string;
	searchPlaceholder?: string;
	backButtonText?: string;

	// Display options
	showBackButton?: boolean;
	showAllItemsOption?: boolean;
	showSearch?: boolean;
	className?: string;

	// Context-specific customization
	context?: "locations" | "roles" | "permissions" | "services";
}

export function MultiSelectAccordion({
	groups,
	selectedGroupIds,
	selectedItemIds,
	onGroupSelectionChange,
	onItemSelectionChange,
	onBack,
	allItemsTitle = "All Items",
	allItemsSubtitle = "This will apply to all groups and items automatically.",
	allItemsDescription = "This option will apply to all groups and items automatically.",
	selectedItemsTitle = "Selected Items Only",
	selectedItemsSubtitle = "Choose specific groups and items.",
	searchPlaceholder = "Search groups and items...",
	backButtonText,
	showBackButton = true,
	showAllItemsOption = true,
	showSearch = true,
	className = "",
	context = "locations",
}: MultiSelectAccordionProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [expandedItems, setExpandedItems] = useState<string[]>([]);

	// Filter groups based on search term
	const filteredGroups = groups.filter((group: SelectableGroup) =>
		group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		group.items.some(item => item.name.toLowerCase().includes(searchTerm.toLowerCase()))
	);

	const handleSelectAllGroups = () => {
		const allGroupIds = filteredGroups.map((group: SelectableGroup) => group.id);
		const allSelected = allGroupIds.every((id: string) =>
			selectedGroupIds.has(id)
		);

		if (allSelected) {
			// Deselect all
			allGroupIds.forEach((id: string) => {
				onGroupSelectionChange(id, false);
				const group = groups.find((grp: SelectableGroup) => grp.id === id);
				if (group) {
					group.items.forEach((item: SelectableItem) => {
						onItemSelectionChange(item.id, false);
					});
				}
			});
		} else {
			// Select all
			allGroupIds.forEach((id: string) => {
				onGroupSelectionChange(id, true);
				const group = groups.find((grp: SelectableGroup) => grp.id === id);
				if (group) {
					group.items.forEach((item: SelectableItem) => {
						onItemSelectionChange(item.id, true);
					});
				}
			});
			// Auto-expand all selected groups
			setExpandedItems(allGroupIds);
		}
	};

	const handleGroupCheck = (groupId: string, checked: boolean) => {
		onGroupSelectionChange(groupId, checked);

		// Auto-expand when selected
		if (checked && !expandedItems.includes(groupId)) {
			setExpandedItems([...expandedItems, groupId]);
		}

		// Handle items in this group
		const group = groups.find((grp: SelectableGroup) => grp.id === groupId);
		if (group) {
			group.items.forEach((item: SelectableItem) => {
				onItemSelectionChange(item.id, checked);
			});
		}
	};

	const isGroupSelected = (groupId: string) => {
		return selectedGroupIds.has(groupId);
	};

	const isItemSelected = (itemId: string) => {
		return selectedItemIds.has(itemId);
	};

	// Context-specific styling and behavior
	const getContextConfig = () => {
		switch (context) {
			case "roles":
				return {
					groupLabel: "Role Category",
					itemLabel: "Specific Role",
					emptyMessage: "No roles found",
				};
			case "permissions":
				return {
					groupLabel: "Permission Category",
					itemLabel: "Specific Permission",
					emptyMessage: "No permissions found",
				};
			case "services":
				return {
					groupLabel: "Service Category",
					itemLabel: "Specific Service",
					emptyMessage: "No services found",
				};
			default: // locations
				return {
					groupLabel: "Location",
					itemLabel: "Station",
					emptyMessage: "No locations found",
				};
		}
	};

	const contextConfig = getContextConfig();

	return (
		<div className={`w-full ${className}`}>
			<Accordion type="single" className="w-full space-y-2">
				{/* All Items Option */}
				{showAllItemsOption && (
					<AccordionItem
						value="all-items"
						className="rounded-lg border border-gray-200 bg-white"
					>
						<AccordionTrigger className="px-4 py-3 hover:bg-gray-50 hover:no-underline">
							<div className="text-left">
								<h3 className="text-sm font-medium text-gray-900">
									{allItemsTitle}
								</h3>
								<p className="mt-1 text-sm text-gray-500">
									{allItemsSubtitle}
								</p>
							</div>
						</AccordionTrigger>
						<AccordionContent className="px-4 pb-4">
							<div className="text-sm text-gray-600">
								{allItemsDescription}
							</div>
						</AccordionContent>
					</AccordionItem>
				)}

				{/* Selected Items Accordion */}
				<AccordionItem
					value="selected-items"
					className="rounded-lg border border-gray-200 bg-white last:border-b-1"
				>
					<AccordionTrigger className="px-4 py-3 hover:bg-gray-50 hover:no-underline">
						<div className="text-left">
							<h3 className="text-sm font-medium text-gray-900">
								{selectedItemsTitle}
							</h3>
							<p className="mt-1 text-sm text-gray-500">
								{selectedItemsSubtitle}
							</p>
						</div>
					</AccordionTrigger>
					<AccordionContent className="px-4 pb-4">
						<div className="space-y-4 border-t py-3">
							{/* Header with back navigation */}
							{showBackButton && onBack && (
								<div className="space-y-3">
									<button
										onClick={onBack}
										className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
									>
										<ChevronLeft className="h-4 w-4" />
										{backButtonText || selectedItemsTitle}
									</button>
									<p className="text-sm leading-relaxed text-gray-500">
										{selectedItemsSubtitle}
									</p>
								</div>
							)}

							{/* Search field */}
							{showSearch && (
								<div className="relative">
									<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
									<Input
										placeholder={searchPlaceholder}
										value={searchTerm}
										onChange={(e) => setSearchTerm(e.target.value)}
										className="border-gray-200 bg-white pl-9 text-sm"
										onClick={handleSelectAllGroups}
									/>
								</div>
							)}

							{/* Groups List */}
							<Accordion
								type="multiple"
								value={expandedItems}
								onValueChange={setExpandedItems}
								className="space-y-1"
							>
								{filteredGroups.map((group: SelectableGroup) => {
									const isSelected = isGroupSelected(group.id);

									return (
										<AccordionItem
											key={group.id}
											value={group.id}
											className="rounded-lg border border-gray-200 bg-white last:border-b-1"
										>
											<AccordionTrigger className="px-3 py-3 hover:bg-gray-50 hover:no-underline">
												<div className="flex items-center gap-3">
													<Checkbox
														checked={isSelected}
														onCheckedChange={(checked) =>
															handleGroupCheck(group.id, checked as boolean)
														}
														onClick={(e) => e.stopPropagation()}
														className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
													/>
													<div className="text-left">
														<span className="text-sm font-medium text-gray-900">
															{group.name}
														</span>
														{group.description && (
															<p className="text-xs text-gray-500">
																{group.description}
															</p>
														)}
													</div>
												</div>
											</AccordionTrigger>
											<AccordionContent className="px-3 pb-3">
												<div className="-mx-3 border-t border-gray-100 bg-gray-50 px-3 pt-2">
													{group.items.map((item: SelectableItem) => (
														<div
															key={item.id}
															className="flex items-center gap-3 px-3 py-2 transition-colors hover:bg-gray-100"
														>
															<div className="w-4" />
															<Checkbox
																checked={isItemSelected(item.id)}
																onCheckedChange={(checked) =>
																	onItemSelectionChange(item.id, checked as boolean)
																}
																className="data-[state=checked]:border-primary data-[state=checked]:bg-primary"
															/>
															<div className="text-left">
																<span className="text-sm text-gray-700">
																	{item.name}
																</span>
																{item.description && (
																	<p className="text-xs text-gray-500">
																		{item.description}
																	</p>
																)}
															</div>
														</div>
													))}
												</div>
											</AccordionContent>
										</AccordionItem>
									);
								})}
							</Accordion>

							{/* Empty state */}
							{filteredGroups.length === 0 && searchTerm && (
								<div className="py-8 text-center">
									<p className="text-sm text-gray-500">
										{contextConfig.emptyMessage} matching "{searchTerm}"
									</p>
								</div>
							)}
						</div>
					</AccordionContent>
				</AccordionItem>
			</Accordion>
		</div>
	);
}
