import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, SheetContent } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { X, PaintBucket, CircleMinus, CirclePlus, Check } from "lucide-react";
import { SketchPicker, type ColorResult } from "react-color";
import { Label } from "@/components/ui/label";
import {
	DatePicker,
	type DateRange,
} from "@/components/common/Datepicker/DatePicker";
import {
	LocationSelectionStep,
	type LocationSelectionData,
} from "./LocationSelectionStep";
import { useCreateCategory } from "@/hooks/useCategories";
import type {
	CreateCategoryRequest,
	LocationSelection,
} from "@/lib/api/categoriesApi";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useForms, useCustomIntakes } from "@/hooks/useForms";

interface AddCategorySheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: CategoryFormData & LocationSelectionData) => void;
}

export interface CategoryFormData {
	name: string;
	description: string;
	color: string;
	conditions: CategoryCondition[];
}

export interface CategoryCondition {
	id: string;
	categoryCondition: string;
	conditionCheck?: string;
	parameter?: string;
	selectedDate?: Date;
	selectedDateRange?: DateRange;
	priorityLevel?: string;
	informationCheckType?: string;
	selectedForm?: string;
	selectedQuestion?: string;
	selectedFormAnswer?: string;
	selectedCustomIntake?: string;
	selectedCustomIntakeAnswer?: string;
}

const conditionCheckOptions = [
	{ id: "registration", label: "Registration" },
	{ id: "last_visit", label: "Last Visit" },
	{ id: "priority", label: "Priority" },
	{ id: "information_check", label: "Information Check" },
];

const parameterOptions = {
	registration: [
		{ id: "before_date", label: "Before the Date" },
		{ id: "after_date", label: "After the Date" },
		{ id: "on_date", label: "On the Date" },
		{ id: "during_range", label: "During the Range" },
		{ id: "outside_range", label: "Outside the Range" },
	],
	last_visit: [
		{ id: "before_date", label: "Before the Date" },
		{ id: "after_date", label: "After the Date" },
		{ id: "on_date", label: "On the Date" },
		{ id: "during_range", label: "During the Range" },
		{ id: "outside_range", label: "Outside the Range" },
	],
};

const priorityLevels = [
	{ id: "high", label: "High" },
	{ id: "medium", label: "Medium" },
	{ id: "low", label: "Low" },
];

const colorPalette = [
	"#222A31",
	"#5E98C9",
	"#0277D8",
	"#9A76C9",
	"#32BA3F",
	"#E36F6F",
	"#E9ED18",
	"#FF9500",
	"#C77676",
	"#FA38C4",
	"#54758F",
	"#A3762D",
];

export function AddCategorySheet({
	open,
	onOpenChange,
	onSubmit,
}: AddCategorySheetProps) {
	const { organizationId } = useOrganizationContext();
	const [currentStep, setCurrentStep] = useState<
		"category" | "locations" | "success"
	>("category");
	const [formData, setFormData] = useState<CategoryFormData>({
		name: "",
		description: "",
		color: "#000000",
		conditions: [
			{
				id: "1",
				categoryCondition: "",
			},
		],
	});
	const [locationData, setLocationData] = useState<LocationSelectionData>({
		applyToAll: false,
		selectedLocations: [],
		selectedStations: {},
		locationSelections: [],
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [showColorPicker, setShowColorPicker] = useState(false);
	const colorPickerRef = useRef<HTMLDivElement>(null);
	const {
		data: formsResponse,
		isLoading: isLoadingForms,
		error: formsError,
	} = useForms(organizationId);
	const {
		data: customIntakesResponse,
		isLoading: isLoadingCustomIntakes,
		error: customIntakesError,
	} = useCustomIntakes(organizationId);
	const getFormsData = () => {
		if (!formsResponse?.data) return [];
		return formsResponse.data.map((form) => ({
			id: form.form_id,
			label: form.form_title,
		}));
	};

	const getQuestionsForForm = (formId: string) => {
		if (!formsResponse?.data) return [];
		const form = formsResponse.data.find((f) => f.form_id === formId);
		if (!form) return [];
		return form.questions.map((question) => ({
			id: question.id,
			label: question.question,
		}));
	};

	const getAnswersForQuestion = (formId: string, questionId: string) => {
		if (!formsResponse?.data) return [];
		const form = formsResponse.data.find((f) => f.form_id === formId);
		if (!form) return [];
		const question = form.questions.find((q) => q.id === questionId);
		if (!question) return [];
		return question.options || [];
	};

	const getCustomIntakesData = () => {
		if (!customIntakesResponse?.data) return [];
		return customIntakesResponse.data.map((intake) => ({
			id: intake.id.toString(),
			label: intake.title,
		}));
	};

	const getAnswersForCustomIntake = (intakeId: string) => {
		if (!customIntakesResponse?.data) return [];
		const intake = customIntakesResponse.data.find(
			(i) => i.id.toString() === intakeId
		);
		if (!intake) return [];
		return intake.options.map((option) => ({
			id: option.id.toString(),
			label: option.label,
		}));
	};

	const hasAvailableForms = () => {
		return !isLoadingForms && !formsError && getFormsData().length > 0;
	};

	const hasAvailableCustomIntakes = () => {
		return (
			!isLoadingCustomIntakes &&
			!customIntakesError &&
			getCustomIntakesData().length > 0
		);
	};

	const getInformationCheckOptions = () => {
		const options = [];

		if (hasAvailableForms()) {
			options.push({
				id: "selected_answers",
				label: "Selected Answers from Forms",
			});
		}

		if (hasAvailableCustomIntakes()) {
			options.push({ id: "custom_intakes", label: "Custom Intakes" });
		}

		return options;
	};

	const createCategoryMutation = useCreateCategory({
		onSuccess: () => {
			setCurrentStep("success");
		},
		onError: (error) => {
			console.error("Error creating category:", error);
		},
	});

	const resetForm = () => {
		setFormData({
			name: "",
			description: "",
			color: "#000000",
			conditions: [
				{
					id: "1",
					categoryCondition: "",
				},
			],
		});
		setLocationData({
			applyToAll: true,
			selectedLocations: [],
			selectedStations: {},
			locationSelections: [],
		});
		setCurrentStep("category");
	};

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
	) => {
		const { name, value } = e.target;
		setFormData({
			...formData,
			[name]: value,
		});
	};

	const handleColorSelect = (color: string) => {
		setFormData({
			...formData,
			color,
		});
		setShowColorPicker(false);
	};

	const handleColorPickerChange = (color: ColorResult) => {
		setFormData({
			...formData,
			color: color.hex,
		});
	};

	const toggleColorPicker = () => {
		setShowColorPicker(!showColorPicker);
	};

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				colorPickerRef.current &&
				!colorPickerRef.current.contains(event.target as Node)
			) {
				setShowColorPicker(false);
			}
		};

		if (showColorPicker) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showColorPicker]);

	const handleConditionChange = (
		conditionId: string,
		field: string,
		value: string | string[] | Date | DateRange | undefined
	) => {
		const stringValue = Array.isArray(value)
			? value[0]
			: typeof value === "string"
				? value
				: value;

		setFormData({
			...formData,
			conditions: formData.conditions.map((condition) => {
				if (condition.id === conditionId) {
					const updatedCondition = {
						...condition,
						[field]:
							field === "selectedDate" ||
							field === "selectedDateRange"
								? value
								: stringValue,
					};

					if (field === "categoryCondition") {
						updatedCondition.conditionCheck = "";
						updatedCondition.parameter = "";
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
						updatedCondition.priorityLevel = "";
						updatedCondition.informationCheckType = "";
						updatedCondition.selectedForm = "";
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
						updatedCondition.selectedCustomIntake = "";
						updatedCondition.selectedCustomIntakeAnswer = "";
					} else if (field === "conditionCheck") {
						updatedCondition.parameter = "";
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
						updatedCondition.priorityLevel = "";
						updatedCondition.informationCheckType = "";
						updatedCondition.selectedForm = "";
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
						updatedCondition.selectedCustomIntake = "";
						updatedCondition.selectedCustomIntakeAnswer = "";
					} else if (field === "parameter") {
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
					} else if (field === "informationCheckType") {
						updatedCondition.selectedForm = "";
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
						updatedCondition.selectedCustomIntake = "";
						updatedCondition.selectedCustomIntakeAnswer = "";
						if (
							stringValue === "selected_answers" &&
							!hasAvailableForms()
						) {
							updatedCondition.informationCheckType = "";
						} else if (
							stringValue === "custom_intakes" &&
							!hasAvailableCustomIntakes()
						) {
							updatedCondition.informationCheckType = "";
						}
					} else if (field === "selectedForm") {
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
					} else if (field === "selectedQuestion") {
						updatedCondition.selectedFormAnswer = "";
					} else if (field === "selectedCustomIntake") {
						updatedCondition.selectedCustomIntakeAnswer = "";
					}

					return updatedCondition;
				}
				return condition;
			}),
		});
	};

	const handleAddCondition = () => {
		if (formData.conditions.length < 5) {
			const newCondition: CategoryCondition = {
				id: Date.now().toString(),
				categoryCondition: "",
			};
			setFormData({
				...formData,
				conditions: [...formData.conditions, newCondition],
			});
		}
	};

	const handleRemoveCondition = (conditionId: string) => {
		if (formData.conditions.length > 1) {
			setFormData({
				...formData,
				conditions: formData.conditions.filter(
					(condition) => condition.id !== conditionId
				),
			});
		}
	};

	const transformToApiFormat = (
		formData: CategoryFormData,
		locationData: LocationSelectionData
	): CreateCategoryRequest => {
		const isManualCategory = formData.conditions.every(
			(condition) =>
				condition.categoryCondition === "None" ||
				condition.categoryCondition === ""
		);

		const apiData: CreateCategoryRequest = {
			name: formData.name,
			description: formData.description,
			color: formData.color,
			type: isManualCategory ? "manual" : "conditional",
		};

		if (locationData.applyToAll) {
			apiData.apply_to_all_locations = true;
		} else {
			apiData.location_selections = locationData.locationSelections;
			const allLocationIds: number[] = [];
			const allStationIds: number[] = [];

			locationData.locationSelections.forEach((selection) => {
				allLocationIds.push(selection.location_id);
				if (!selection.all_stations) {
					allStationIds.push(...selection.station_ids);
				}
			});

			apiData.location_ids = allLocationIds;
			apiData.station_ids = allStationIds;
		}

		if (isManualCategory) {
			apiData.client_ids = [];
		} else {
			const customCondition = formData.conditions.find(
				(condition) => condition.categoryCondition === "Custom"
			);

			if (customCondition) {
				console.log("Processing custom condition:", customCondition);
				if (customCondition.conditionCheck === "registration") {
					apiData.condition_type = "registration_date";
				} else if (customCondition.conditionCheck === "last_visit") {
					apiData.condition_type = "last_seen";
				} else if (customCondition.conditionCheck === "priority") {
					apiData.condition_type = "priority";
					apiData.operator = "equals";
				} else if (
					customCondition.conditionCheck === "information_check"
				) {
					if (
						customCondition.informationCheckType ===
						"selected_answers"
					) {
						apiData.condition_type = "information_check";
						apiData.information_type = "form_answer";
						apiData.operator = "equals";
						if (customCondition.selectedQuestion) {
							console.log(
								"Selected question ID:",
								customCondition.selectedQuestion
							);
							apiData.form_question_id =
								customCondition.selectedQuestion;
						}
						if (customCondition.selectedFormAnswer) {
							console.log(
								"Selected form answer ID:",
								customCondition.selectedFormAnswer
							);
							apiData.conditional_value =
								customCondition.selectedFormAnswer;
						}
					} else if (
						customCondition.informationCheckType ===
						"custom_intakes"
					) {
						apiData.condition_type = "information_check";
						apiData.information_type = "custom_intake_answer";
						apiData.operator = "equals";
						if (customCondition.selectedCustomIntake) {
							apiData.custom_intake_id = parseInt(
								customCondition.selectedCustomIntake
							);
						}
						if (customCondition.selectedCustomIntakeAnswer) {
							apiData.conditional_value =
								customCondition.selectedCustomIntakeAnswer;
						}
					}
				}

				if (customCondition.parameter) {
					if (customCondition.parameter === "outside_range") {
						apiData.operator = "outside_of_range";
					} else {
						apiData.operator = customCondition.parameter;
					}
				}

				if (customCondition.parameter) {
					if (
						customCondition.parameter === "during_range" ||
						customCondition.parameter === "outside_range"
					) {
						if (customCondition.selectedDateRange) {
							if (customCondition.selectedDateRange.from) {
								apiData.date_range_start =
									customCondition.selectedDateRange.from
										.toISOString()
										.split("T")[0];
							}
							if (customCondition.selectedDateRange.to) {
								apiData.date_range_end =
									customCondition.selectedDateRange.to
										.toISOString()
										.split("T")[0];
							}
						}
					} else if (
						customCondition.parameter === "before_date" ||
						customCondition.parameter === "after_date" ||
						customCondition.parameter === "on_date"
					) {
						if (customCondition.selectedDate) {
							const dateStr = customCondition.selectedDate
								.toISOString()
								.split("T")[0];
							apiData.conditional_value = dateStr;
						}
					}
				}
				if (customCondition.priorityLevel) {
					apiData.priority = customCondition.priorityLevel;
					apiData.conditional_value = customCondition.priorityLevel;
				}
			}
		}

		return apiData;
	};

	const handleCategorySubmit = () => {
		if (!formData.name.trim()) {
			return;
		}
		setCurrentStep("locations");
	};

	const handleLocationSubmit = async () => {
		setIsSubmitting(true);
		try {
			const apiData = transformToApiFormat(formData, locationData);
			console.log("Submitting category data:", apiData);
			console.log("Fields being sent:", Object.keys(apiData));
			await createCategoryMutation.mutateAsync(apiData);
		} catch (error) {
			console.error("Error submitting category:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleSkipLocationSelection = async () => {
		setIsSubmitting(true);
		try {
			const skippedLocationData: LocationSelectionData = {
				applyToAll: true,
				selectedLocations: [],
				selectedStations: {},
				locationSelections: [],
			};
			const apiData = transformToApiFormat(formData, skippedLocationData);
			console.log(
				"Submitting category data (skipped location):",
				apiData
			);
			console.log("Fields being sent:", Object.keys(apiData));
			await createCategoryMutation.mutateAsync(apiData);
		} catch (error) {
			console.error("Error submitting category:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		resetForm();
		onOpenChange(false);
	};

	const handleSheetOpenChange = (open: boolean) => {
		if (!open) {
			resetForm();
		}
		onOpenChange(open);
	};

	return (
		<Sheet open={open} onOpenChange={handleSheetOpenChange}>
			<SheetContent className="w-full !max-w-[525px] p-0 [&>button]:hidden">
				{currentStep === "success" ? (
					<div className="flex h-full max-h-screen flex-col">
						<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
							<div className="flex h-14 items-start justify-start gap-2.5">
								<div className="flex flex-1 flex-col items-start justify-start gap-2">
									<div className="text-base leading-7 font-semibold">
										Add New Category
									</div>
									<div className="text-xs leading-none font-normal text-gray-500">
										Category information
									</div>
								</div>
								<div className="flex items-start justify-start gap-2.5">
									<Button
										variant="ghost"
										size="icon"
										onClick={handleClose}
										className="h-9 w-9 rounded-md"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div className="flex flex-1 items-center justify-center">
							{renderSuccessContent()}
						</div>
					</div>
				) : (
					<div className="flex h-full max-h-screen flex-col">
						<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
							<div className="flex h-14 items-start justify-start gap-2.5">
								<div className="flex flex-1 flex-col items-start justify-start gap-2">
									<div className="text-base leading-7 font-semibold">
										Add New Category
									</div>
									<div className="text-xs leading-none font-normal text-gray-500">
										Category information
									</div>
								</div>
								<div className="flex items-start justify-start gap-2.5">
									<Button
										variant="ghost"
										size="icon"
										onClick={handleClose}
										className="h-9 w-9 rounded-md"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div className="min-h-0 flex-1 overflow-y-auto p-6 pt-6">
							{currentStep === "category" &&
								renderCategoryContent()}
							{currentStep === "locations" &&
								renderLocationContent()}
						</div>

						<div className="flex-shrink-0 bg-white p-6">
							{currentStep === "category"
								? renderCategoryFooter()
								: renderLocationFooter()}
						</div>
					</div>
				)}
			</SheetContent>
		</Sheet>
	);

	function renderCategoryContent() {
		return (
			<div className="flex flex-col gap-6">
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Category Details</h3>

					<div className="space-y-2">
						<Label htmlFor="name" className="text-xs font-medium">
							Category Name *
						</Label>
						<Input
							id="name"
							name="name"
							value={formData.name}
							onChange={handleInputChange}
							className="h-9 text-xs"
							placeholder="Enter category name"
						/>
					</div>

					<div className="space-y-2">
						<Label
							htmlFor="description"
							className="text-xs font-medium"
						>
							Description
						</Label>
						<Textarea
							id="description"
							name="description"
							value={formData.description}
							onChange={handleInputChange}
							className="min-h-20 resize-none text-xs"
							placeholder="Category Description"
						/>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Assign Color</h3>

					<div className="relative flex items-center gap-2">
						<button
							type="button"
							onClick={toggleColorPicker}
							className="flex h-9 items-center gap-4 rounded-md bg-gray-100 p-2 transition-colors hover:bg-gray-200"
						>
							<PaintBucket className="h-5 w-5" />
							<div
								className="h-6 w-6 rounded-md"
								style={{
									backgroundColor: formData.color,
								}}
							/>
						</button>
						<Input
							value={formData.color}
							onChange={(e) => handleColorSelect(e.target.value)}
							className="h-9 w-24 text-xs"
							placeholder="#000000"
						/>

						{showColorPicker && (
							<div
								ref={colorPickerRef}
								className="absolute bottom-full left-0 z-50 mb-2 border border-gray-200 bg-white shadow-lg"
								style={{ borderRadius: "8px" }}
							>
								<style>{`
									.sketch-picker-custom .flexbox-fix:last-child {
										display: none !important;
									}
									.sketch-picker-custom .flexbox-fix:nth-last-child(2) {
										display: none !important;
									}
								`}</style>
								<div className="sketch-picker-custom">
									<SketchPicker
										color={formData.color}
										onChange={handleColorPickerChange}
										disableAlpha={true}
										presetColors={[]}
									/>
								</div>
							</div>
						)}
					</div>

					<div className="flex flex-col gap-1.5">
						<p className="text-[10px] text-gray-500">Recent</p>
						<div className="flex flex-wrap gap-1.5">
							{colorPalette.map((color, index) => (
								<button
									key={index}
									type="button"
									onClick={() => handleColorSelect(color)}
									className={`relative h-7 w-7 rounded-md transition-all ${
										formData.color === color
											? "ring-2 ring-blue-500 ring-offset-1"
											: ""
									}`}
									style={{
										backgroundColor: color,
									}}
								>
									{formData.color === color && (
										<div className="absolute inset-0 flex items-center justify-center">
											<Check className="h-3.5 w-3.5 text-white" />
										</div>
									)}
								</button>
							))}
						</div>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Conditions</h3>

					{formData.conditions.map((condition) => (
						<div key={condition.id} className="flex flex-col gap-4">
							<div className="space-y-2">
								<Label className="text-xs font-medium">
									Category Condition *
								</Label>
								<Select
									value={condition.categoryCondition}
									onValueChange={(value) =>
										handleConditionChange(
											condition.id,
											"categoryCondition",
											value
										)
									}
								>
									<SelectTrigger className="mt-2 h-9 w-full text-xs">
										<SelectValue placeholder="Select condition" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="None">
											None
										</SelectItem>
										<SelectItem value="Custom">
											Custom
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{condition.categoryCondition === "Custom" && (
								<div className="space-y-2">
									<Label className="text-xs font-medium">
										Condition Check *
									</Label>
									<Select
										value={condition.conditionCheck || ""}
										onValueChange={(value) =>
											handleConditionChange(
												condition.id,
												"conditionCheck",
												value
											)
										}
									>
										<SelectTrigger className="mt-2 h-9 w-full text-xs">
											<SelectValue placeholder="Select condition check" />
										</SelectTrigger>
										<SelectContent>
											{conditionCheckOptions.map(
												(option) => (
													<SelectItem
														key={option.id}
														value={option.id}
													>
														{option.label}
													</SelectItem>
												)
											)}
										</SelectContent>
									</Select>
								</div>
							)}
							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Information Check Type *
										</Label>
										<Select
											value={
												condition.informationCheckType ||
												""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"informationCheckType",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select information check type" />
											</SelectTrigger>
											<SelectContent>
												{isLoadingForms ||
												isLoadingCustomIntakes ? (
													<SelectItem
														value="loading"
														disabled
													>
														Loading options...
													</SelectItem>
												) : getInformationCheckOptions()
														.length === 0 ? (
													<SelectItem
														value="no-options"
														disabled
													>
														No forms or custom
														intakes available
													</SelectItem>
												) : (
													getInformationCheckOptions().map(
														(option) => (
															<SelectItem
																key={option.id}
																value={
																	option.id
																}
															>
																{option.label}
															</SelectItem>
														)
													)
												)}
												{/* Show disabled options with explanations when not available */}
												{!hasAvailableForms() &&
													!isLoadingForms && (
														<SelectItem
															value="forms-unavailable"
															disabled
														>
															Selected Answers
															from Forms (No forms
															available)
														</SelectItem>
													)}
												{!hasAvailableCustomIntakes() &&
													!isLoadingCustomIntakes && (
														<SelectItem
															value="intakes-unavailable"
															disabled
														>
															Custom Intakes (No
															custom intakes
															available)
														</SelectItem>
													)}
											</SelectContent>
										</Select>
									</div>
								)}
							{condition.categoryCondition === "Custom" &&
								(condition.conditionCheck === "registration" ||
									condition.conditionCheck ===
										"last_visit") && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Parameter *
										</Label>
										<Select
											value={condition.parameter || ""}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"parameter",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select parameter" />
											</SelectTrigger>
											<SelectContent>
												{parameterOptions[
													condition.conditionCheck as keyof typeof parameterOptions
												]?.map((option) => (
													<SelectItem
														key={option.id}
														value={option.id}
													>
														{option.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								)}
							{condition.categoryCondition === "Custom" &&
								(condition.conditionCheck === "registration" ||
									condition.conditionCheck ===
										"last_visit") &&
								condition.parameter && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											{condition.parameter ===
												"during_range" ||
											condition.parameter ===
												"outside_range"
												? "Select Date Range *"
												: "Select Date *"}
										</Label>
										{condition.parameter ===
											"during_range" ||
										condition.parameter ===
											"outside_range" ? (
											<DatePicker
												variant="range"
												value={
													condition.selectedDateRange as DateRange
												}
												onChange={(dateRange) =>
													handleConditionChange(
														condition.id,
														"selectedDateRange",
														dateRange as DateRange
													)
												}
												placeholder="Select date range"
												className="h-9 text-xs"
											/>
										) : (
											<DatePicker
												variant="default"
												value={
													condition.selectedDate as Date
												}
												onChange={(date) =>
													handleConditionChange(
														condition.id,
														"selectedDate",
														date as Date
													)
												}
												placeholder="Select date"
												className="h-9 text-xs"
											/>
										)}
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck === "priority" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Priority Level *
										</Label>
										<Select
											value={
												condition.priorityLevel || ""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"priorityLevel",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select priority level" />
											</SelectTrigger>
											<SelectContent>
												{priorityLevels.map(
													(option) => (
														<SelectItem
															key={option.id}
															value={option.id}
														>
															{option.label}
														</SelectItem>
													)
												)}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"selected_answers" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Form *
										</Label>
										<Select
											value={condition.selectedForm || ""}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedForm",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select form" />
											</SelectTrigger>
											<SelectContent>
												{isLoadingForms ? (
													<SelectItem
														value="loading"
														disabled
													>
														Loading forms...
													</SelectItem>
												) : formsError ? (
													<SelectItem
														value="error"
														disabled
													>
														Error loading forms
													</SelectItem>
												) : getFormsData().length ===
												  0 ? (
													<SelectItem
														value="no-forms"
														disabled
													>
														No forms available
													</SelectItem>
												) : (
													getFormsData().map(
														(option) => (
															<SelectItem
																key={option.id}
																value={
																	option.id
																}
															>
																{option.label}
															</SelectItem>
														)
													)
												)}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"selected_answers" &&
								condition.selectedForm && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Question *
										</Label>
										<Select
											value={
												condition.selectedQuestion || ""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedQuestion",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select question" />
											</SelectTrigger>
											<SelectContent>
												{getQuestionsForForm(
													condition.selectedForm
												).length === 0 ? (
													<SelectItem
														value="no-questions"
														disabled
													>
														No questions available
														for this form
													</SelectItem>
												) : (
													getQuestionsForForm(
														condition.selectedForm
													).map((option) => (
														<SelectItem
															key={option.id}
															value={option.id}
														>
															{option.label}
														</SelectItem>
													))
												)}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"selected_answers" &&
								condition.selectedForm &&
								condition.selectedQuestion && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Answer *
										</Label>
										<Select
											value={
												condition.selectedFormAnswer ||
												""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedFormAnswer",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select answer" />
											</SelectTrigger>
											<SelectContent>
												{getAnswersForQuestion(
													condition.selectedForm,
													condition.selectedQuestion
												).length === 0 ? (
													<SelectItem
														value="no_answers"
														disabled
													>
														No answers available for
														this question
													</SelectItem>
												) : (
													getAnswersForQuestion(
														condition.selectedForm,
														condition.selectedQuestion
													).map((option) => (
														<SelectItem
															key={option.id}
															value={option.id}
														>
															{option.label}
														</SelectItem>
													))
												)}
											</SelectContent>
										</Select>
									</div>
								)}
							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"custom_intakes" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Custom Intake *
										</Label>
										<Select
											value={
												condition.selectedCustomIntake ||
												""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedCustomIntake",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select custom intake" />
											</SelectTrigger>
											<SelectContent>
												{isLoadingCustomIntakes ? (
													<SelectItem
														value="loading"
														disabled
													>
														Loading custom
														intakes...
													</SelectItem>
												) : customIntakesError ? (
													<SelectItem
														value="error"
														disabled
													>
														Error loading custom
														intakes
													</SelectItem>
												) : getCustomIntakesData()
														.length === 0 ? (
													<SelectItem
														value="no-intakes"
														disabled
													>
														No custom intakes
														available
													</SelectItem>
												) : (
													getCustomIntakesData().map(
														(option) => (
															<SelectItem
																key={option.id}
																value={
																	option.id
																}
															>
																{option.label}
															</SelectItem>
														)
													)
												)}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"custom_intakes" &&
								condition.selectedCustomIntake && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Answer *
										</Label>
										<Select
											value={
												condition.selectedCustomIntakeAnswer ||
												""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedCustomIntakeAnswer",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select answer" />
											</SelectTrigger>
											<SelectContent>
												{getAnswersForCustomIntake(
													condition.selectedCustomIntake
												).length === 0 ? (
													<SelectItem
														value="no_answers"
														disabled
													>
														No answers available for
														this custom intake
													</SelectItem>
												) : (
													getAnswersForCustomIntake(
														condition.selectedCustomIntake
													).map((option) => (
														<SelectItem
															key={option.id}
															value={option.id}
														>
															{option.label}
														</SelectItem>
													))
												)}
											</SelectContent>
										</Select>
									</div>
								)}

							{formData.conditions.length > 1 && (
								<div className="flex justify-end">
									<button
										type="button"
										onClick={() =>
											handleRemoveCondition(condition.id)
										}
										className="flex items-center gap-2 text-xs text-[#27272A]"
									>
										<CircleMinus className="h-3 w-3" />
										Remove Condition
									</button>
								</div>
							)}
						</div>
					))}

					{formData.conditions.length < 5 &&
						formData.conditions.some(
							(condition) =>
								condition.categoryCondition === "Custom"
						) && (
							<div className="flex items-center justify-between">
								<div className="flex flex-col gap-0.5">
									<button
										type="button"
										onClick={handleAddCondition}
										className="flex items-center gap-2 text-xs text-[#27272A]"
									>
										<CirclePlus className="h-3 w-3" />
										Add Another Condition
									</button>
									<div className="pl-5 text-[8px] text-gray-500">
										(Max 5)
									</div>
								</div>
							</div>
						)}
				</div>
			</div>
		);
	}

	function renderCategoryFooter() {
		return (
			<div className="flex items-center justify-end gap-3">
				<Button
					variant="outline"
					onClick={handleClose}
					className="h-9 px-4 text-xs"
				>
					Cancel
				</Button>
				<Button
					onClick={handleCategorySubmit}
					disabled={isSubmitting || !formData.name.trim()}
					className="h-9 bg-[#005893] px-4 text-xs hover:bg-[#004a7a]"
				>
					Next
				</Button>
			</div>
		);
	}

	function renderLocationContent() {
		return (
			<LocationSelectionStep
				locationData={locationData}
				onLocationDataChange={setLocationData}
			/>
		);
	}

	function renderLocationFooter() {
		return (
			<div className="flex items-center justify-end gap-3">
				<Button
					variant="outline"
					onClick={handleClose}
					className="h-9 rounded-md border border-gray-200 bg-white px-4 py-2 text-xs font-medium"
				>
					Cancel
				</Button>
				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						variant="secondary"
						onClick={handleSkipLocationSelection}
						className="h-9 rounded-md bg-gray-100 px-4 py-2 text-xs font-medium"
					>
						Skip for now
					</Button>
					<Button
						onClick={handleLocationSubmit}
						disabled={
							isSubmitting ||
							(!locationData.applyToAll &&
								locationData.selectedLocations.length === 0)
						}
						className="h-9 rounded-md px-4 py-2 text-xs font-medium text-white"
					>
						{isSubmitting ? "Saving..." : "Save"}
					</Button>
				</div>
			</div>
		);
	}

	function renderSuccessContent() {
		const handleDone = () => {
			try {
				const finalData = {
					...formData,
					...locationData,
				};
				onSubmit?.(finalData);
				resetForm();
				onOpenChange(false);
			} catch (error) {
				console.error("Error submitting category:", error);
			}
		};

		return (
			<div className="flex flex-col items-center justify-center gap-8">
				<div className="flex flex-col items-center justify-start gap-11">
					<div className="inline-flex items-center justify-start gap-2.5 overflow-hidden rounded-[500px] bg-zinc-500/5 p-8">
						<div className="relative h-12 w-12 overflow-hidden">
							<Check
								className="absolute top-3 left-2 h-8 w-8 text-[#005893]/16"
								strokeWidth={4}
							/>
						</div>
					</div>

					<div className="flex w-full max-w-72 flex-col items-center justify-start gap-3">
						<div className="text-foreground text-center text-xl leading-loose font-semibold">
							Category Added
						</div>
						<div className="text-muted-foreground text-center text-sm leading-tight font-normal">
							A new category has been added successfully.
						</div>
					</div>
				</div>

				<div className="inline-flex items-start justify-center gap-3">
					<Button
						variant="secondary"
						onClick={() => {
							resetForm();
							setCurrentStep("category");
						}}
						className="h-9 px-4 py-2 text-xs font-medium"
					>
						Add Another Category
					</Button>
					<Button
						onClick={handleDone}
						className="h-9 w-20 bg-sky-800 px-4 py-2 text-xs font-medium text-white hover:bg-sky-900"
					>
						Done
					</Button>
				</div>
			</div>
		);
	}
}
