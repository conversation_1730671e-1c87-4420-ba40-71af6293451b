import React, { useEffect, useRef } from "react";
import { ChevronDown, XIcon, CheckIcon, X } from "lucide-react";
import { FadeLoader } from "react-spinners";

import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@/components/ui/command";

import type { MultiAsyncSelectProps, Option } from "./types";

export const MultiAsyncSelect = React.forwardRef<
	HTMLButtonElement,
	MultiAsyncSelectProps
>(
	(
		{
			options,
			onValueChange,
			onSearch,
			defaultValue = [],
			placeholder = "Select options",
			searchPlaceholder = "Search...",
			clearText = "Clear",
			closeText = "Close",
			maxCount = 3,
			modalPopover = false,
			className,
			loading = false,
			async = false,
			error = null,
			...props
		},
		ref
	) => {
		const [selectedValues, setSelectedValues] =
			React.useState<string[]>(defaultValue);
		const [isPopoverOpen, setIsPopoverOpen] = React.useState(false);
		const optionsRef = useRef<Record<string, Option>>({});

		const handleInputKeyDown = (
			event: React.KeyboardEvent<HTMLInputElement>
		) => {
			if (event.key === "Enter") {
				setIsPopoverOpen(true);
			} else if (
				event.key === "Backspace" &&
				!event.currentTarget.value
			) {
				const newSelectedValues = [...selectedValues];
				newSelectedValues.pop();
				setSelectedValues(newSelectedValues);
				onValueChange(newSelectedValues);
			}
		};

		const toggleOption = (option: string) => {
			const isAddon = selectedValues.includes(option);
			const newSelectedValues = isAddon
				? selectedValues.filter((value) => value !== option)
				: [...selectedValues, option];
			setSelectedValues(newSelectedValues);
			onValueChange(newSelectedValues);
		};

		const handleClear = () => {
			setSelectedValues([]);
			onValueChange([]);
		};

		const handleTogglePopover = () => {
			setIsPopoverOpen((prev) => !prev);
		};

		const clearExtraOptions = () => {
			const newSelectedValues = selectedValues.slice(0, maxCount);
			setSelectedValues(newSelectedValues);
			onValueChange(newSelectedValues);
		};

		const toggleAll = () => {
			if (selectedValues.length === options.length) {
				handleClear();
			} else {
				const allValues = options.map((option) => option.value);
				setSelectedValues(allValues);
				onValueChange(allValues);
			}
		};

		useEffect(() => {
			const temp = options.reduce(
				(acc, option) => {
					acc[option.value] = option;
					return acc;
				},
				{} as Record<string, Option>
			);
			if (async) {
				const temp2 = selectedValues.reduce(
					(acc, value) => {
						const option = optionsRef.current[value];
						if (option) {
							acc[option.value] = option;
						}
						return acc;
					},
					{} as Record<string, Option>
				);
				optionsRef.current = {
					...temp,
					...temp2,
				};
			}
		}, [async, options, selectedValues]);

		return (
			<Popover
				open={isPopoverOpen}
				onOpenChange={setIsPopoverOpen}
				modal={modalPopover}
			>
				<PopoverTrigger asChild>
					<Button
						ref={ref}
						{...props}
						onClick={handleTogglePopover}
						className={cn(
							"flex h-9 w-full items-center justify-between rounded-md border border-zinc-200 bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-sm ring-offset-white hover:bg-transparent focus:ring-1 focus:ring-zinc-950 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:border-zinc-800 dark:bg-black dark:ring-offset-zinc-950 dark:hover:bg-black dark:focus:ring-zinc-300 [&_svg]:pointer-events-auto [&>span]:line-clamp-1",
							className
						)}
					>
						{selectedValues.length > 0 ? (
							<div className="flex w-full items-center justify-between">
								<div className="flex flex-nowrap items-center gap-1 overflow-x-auto">
									{selectedValues
										.slice(0, maxCount)
										.map((value) => {
											let option: Option | undefined;
											if (async) {
												option =
													optionsRef.current[value];
											} else {
												option = options.find(
													(option) =>
														option.value === value
												);
											}
											return (
												<Badge
													key={value}
													className="border-primary bg-primary/10 text-black"
												>
													<span>{option?.label}</span>
													<div
														className="ml-2 size-4 cursor-pointer"
														onClick={(event) => {
															event.stopPropagation();
															toggleOption(value);
														}}
													>
														<X />
													</div>
												</Badge>
											);
										})}
									{selectedValues.length > maxCount && (
										<Badge>
											<span>{`+ ${selectedValues.length - maxCount}`}</span>

											<div
												className="ml-2 size-4 cursor-pointer"
												onClick={(event) => {
													event.stopPropagation();
													clearExtraOptions();
												}}
											>
												<X />
											</div>
										</Badge>
									)}
								</div>
								<div className="flex items-center justify-between">
									<XIcon
										className="h-4 cursor-pointer text-zinc-500"
										onClick={(event) => {
											event.stopPropagation();
											handleClear();
										}}
									/>
									<Separator
										orientation="vertical"
										className="mx-2 flex h-full min-h-6"
									/>
									<ChevronDown className="h-4 cursor-pointer text-zinc-300 dark:text-zinc-500" />
								</div>
							</div>
						) : (
							<div className="mx-auto flex w-full items-center justify-between">
								<span className="text-sm font-normal text-zinc-500">
									{placeholder}
								</span>
								<ChevronDown className="h-4 cursor-pointer text-zinc-300 dark:text-zinc-500" />
							</div>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent
					className="z-[1004] w-auto p-0"
					align="start"
					onEscapeKeyDown={() => setIsPopoverOpen(false)}
				>
					<Command shouldFilter={!async}>
						<CommandInput
							placeholder={searchPlaceholder}
							onValueChange={(value) => {
								if (onSearch) {
									onSearch(value);
								}
							}}
							onKeyDown={handleInputKeyDown}
						/>
						<CommandList>
							{async && error && (
								<div className="text-destructive p-4 text-center">
									{error.message}
								</div>
							)}
							{async && loading && options.length === 0 && (
								<div className="flex h-full items-center justify-center py-6">
									<FadeLoader
										color="#ffa500"
										style={{
											transform: "scale(0.38)",
											position: "relative",
											top: "-1px",
										}}
									/>
								</div>
							)}
							{async ? (
								!loading &&
								!error &&
								options.length === 0 && (
									<div className="pt-6 pb-4 text-center text-sm">
										{`No ${placeholder.toLowerCase()} found.`}
									</div>
								)
							) : (
								<CommandEmpty>
									{`No ${placeholder.toLowerCase()} found.`}
								</CommandEmpty>
							)}
							<CommandGroup>
								{!async && (
									<CommandItem
										key="all"
										onSelect={toggleAll}
										className="cursor-pointer"
									>
										<div
											className={cn(
												"border-primary mr-1 size-4 rounded-[4px] border text-center shadow-xs transition-shadow outline-none",
												selectedValues.length ===
													options.length
													? "bg-primary text-primary-foreground border-primary"
													: "opacity-50 [&_svg]:invisible"
											)}
										>
											<CheckIcon className="size-3.5 text-white dark:text-black" />
										</div>
										<span>Select all</span>
									</CommandItem>
								)}
								{options.map((option) => {
									const isSelected = selectedValues.includes(
										option.value
									);
									return (
										<CommandItem
											key={option.value}
											onSelect={() =>
												toggleOption(option.value)
											}
											className="cursor-pointer"
										>
											<div
												className={cn(
													"border-primary mr-1 size-4 rounded-[4px] border text-center shadow-xs transition-shadow outline-none",
													isSelected
														? "bg-primary text-primary-foreground border-primary"
														: "opacity-50 [&_svg]:invisible"
												)}
											>
												<CheckIcon className="size-3.5 text-white dark:text-black" />
											</div>
											<span>{option.label}</span>
										</CommandItem>
									);
								})}
							</CommandGroup>
							<CommandSeparator />
							<CommandGroup>
								<div className="flex items-center justify-between">
									{selectedValues.length > 0 && (
										<>
											<CommandItem
												onSelect={handleClear}
												className="flex-1 cursor-pointer justify-center"
											>
												{clearText}
											</CommandItem>
											<Separator
												orientation="vertical"
												className="flex h-full min-h-6"
											/>
										</>
									)}
									<CommandItem
										onSelect={() => setIsPopoverOpen(false)}
										className="max-w-full flex-1 cursor-pointer justify-center"
									>
										{closeText}
									</CommandItem>
								</div>
							</CommandGroup>
						</CommandList>
					</Command>
				</PopoverContent>
			</Popover>
		);
	}
);

MultiAsyncSelect.displayName = "MultiAsyncSelect";
