import * as React from "react";
import { cn } from "@/lib/utils";
import { ChevronRight, UserCheck } from "lucide-react";

export interface OptionCardProps extends React.HTMLAttributes<HTMLDivElement> {
	title: string;
	description: string;
	icon?: React.ReactNode;
	variant?: "default" | "selected";
	showChevron?: boolean;
	iconBackgroundColor?: string;
	onSelect?: () => void;
}

const OptionCard = React.forwardRef<HTMLDivElement, OptionCardProps>(
	(
		{
			className,
			title,
			description,
			icon,
			variant = "default",
			showChevron = true,
			iconBackgroundColor,
			onSelect,
			...props
		},
		ref
	) => {
		const defaultIcon = <UserCheck className="h-3.5 w-3.5 text-gray-700" />;

		return (
			<div
				ref={ref}
				className={cn(
					"inline-flex w-full max-w-[453px] cursor-pointer items-start justify-start gap-2.5 overflow-hidden rounded-lg border border-[#E4E4E7] bg-white p-4 transition-all hover:shadow-sm",
					variant === "selected" && "border-[#005893]",
					className
				)}
				{...props}
			>
				<div
					className={cn(
						"flex h-6 w-6 items-center justify-center rounded-md p-1",
						iconBackgroundColor || "bg-[#F4F4F5]"
					)}
				>
					{icon || defaultIcon}
				</div>
				<div className="inline-flex flex-1 flex-col items-start justify-start gap-1">
					<div className="w-full justify-start text-sm leading-tight font-semibold text-gray-900">
						{title}
					</div>
					<div className="w-full justify-start text-[10px] leading-3 font-normal text-gray-500">
						{description}
					</div>
				</div>
				{showChevron && (
					<div className="flex h-5 w-5 items-center justify-center">
						<ChevronRight className="h-3.5 w-3.5 text-gray-400" />
					</div>
				)}
			</div>
		);
	}
);

OptionCard.displayName = "OptionCard";

export { OptionCard };
