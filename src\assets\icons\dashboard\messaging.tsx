import React from "react";

const MessagingIcon: React.FC = () => {
	return (
		<svg
			width="32"
			height="33"
			viewBox="0 0 32 33"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M8.13158 25.832C6.3981 25.6615 5.0995 25.1408 4.2286 24.2699C2.6665 22.7079 2.6665 20.1936 2.6665 15.1654V14.4987C2.6665 9.47038 2.6665 6.95623 4.2286 5.39412C5.7907 3.83203 8.30485 3.83203 13.3332 3.83203H18.6665C23.6948 3.83203 26.209 3.83203 27.771 5.39412C29.3332 6.95623 29.3332 9.47038 29.3332 14.4987V15.1654C29.3332 20.1936 29.3332 22.7079 27.771 24.2699C26.209 25.832 23.6948 25.832 18.6665 25.832C17.9192 25.8487 17.324 25.9055 16.7393 26.0387C15.1414 26.4066 13.6618 27.2242 12.1997 27.9372C10.1162 28.9531 9.0745 29.4611 8.42076 28.9855C7.17009 28.054 8.39256 25.1679 8.6665 23.832"
				fill="#CFC4F6"
			/>
			<path
				d="M8.13158 25.832C6.3981 25.6615 5.0995 25.1408 4.2286 24.2699C2.6665 22.7079 2.6665 20.1936 2.6665 15.1654V14.4987C2.6665 9.47038 2.6665 6.95623 4.2286 5.39412C5.7907 3.83203 8.30485 3.83203 13.3332 3.83203H18.6665C23.6948 3.83203 26.209 3.83203 27.771 5.39412C29.3332 6.95623 29.3332 9.47038 29.3332 14.4987V15.1654C29.3332 20.1936 29.3332 22.7079 27.771 24.2699C26.209 25.832 23.6948 25.832 18.6665 25.832C17.9192 25.8487 17.324 25.9055 16.7393 26.0387C15.1414 26.4066 13.6618 27.2242 12.1997 27.9372C10.1162 28.9531 9.0745 29.4611 8.42076 28.9855C7.17009 28.054 8.39256 25.1679 8.6665 23.832"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path
				d="M10.6665 18.4987H21.3332M10.6665 11.832H15.9998"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
		</svg>
	);
};

export default MessagingIcon;
