import { apiClient } from "./clients";

const CLIENTS_ENDPOINTS = {
	base: "/api/v1/clients",
	byId: (id: string | number) => `/api/v1/clients/${id}`,
} as const;

export interface CreateClientRequest {
	first_name: string;
	last_name: string;
	email: string;
	phone_number: string;
	attributes?: Record<string, string | number | boolean>;
}

export interface CreateClientResponse {
	success: boolean;
	message: string;
	data: {
		id: number;
		first_name: string;
		last_name: string;
		email: string;
		phone_number: string;
		attributes: Record<string, any>;
		created_at: string;
		updated_at: string;
	};
}

export interface UpdateClientRequest {
	first_name: string;
	last_name: string;
	email: string;
	phone_number: string;
	is_active?: boolean;
	attributes?: Record<string, string | number | boolean>;
}

export interface UpdateClientResponse {
	success: boolean;
	message: string;
	data: {
		id: number;
		first_name: string;
		last_name: string;
		email: string;
		phone_number: string;
		is_active: boolean;
		attributes: Record<string, any>;
		created_at: string;
		updated_at: string;
	};
}

export interface Client {
	id: number;
	first_name: string;
	last_name: string;
	email: string;
	phone_number: string;
	attributes: Record<string, any>;
	created_at: string;
	updated_at: string;
}

export interface ClientsFilters {
	page?: number;
	per_page?: number;
	search?: string;
	status?: string;
	organization_id?: number;
}

export interface ClientsMeta {
	pagination: {
		total: number;
		count: number;
		per_page: number;
		current_page: number;
		total_pages: number;
	};
}

export interface ClientsResponse {
	success: boolean;
	message: string;
	data: Client[];
	meta: ClientsMeta;
}

export interface ClientAttribute {
	attribute_id: number;
	key: string;
	label: string;
	type: string;
	value: any;
}

export interface ClientCategory {
	id: number;
	name: string;
	color: string;
	is_conditional: boolean;
	description: string;
	type: string;
	client_count: number;
	station_count: number;
	is_active: boolean;
	business_id: number;
	conditions?: Array<{
		id: number;
		attribute: {
			id: number;
			name: string;
			type: string;
		};
		operator: {
			key: string;
			label: string;
		};
		value: any;
	}>;
}

export interface ClientDetail {
	id: number;
	external_id?: string;
	first_name: string;
	last_name: string;
	email: string;
	phone_number: string;
	phone_number_verified: boolean;
	profile_picture_url?: string;
	last_visit?: string;
	is_active: boolean;
	business_id: number;
	attributes: ClientAttribute[];
	categories: ClientCategory[];
}

export interface ClientDetailResponse {
	success: boolean;
	message: string;
	data: ClientDetail;
}

export const clientsApi = {
	getClients: async (
		filters: ClientsFilters = {}
	): Promise<ClientsResponse> => {
		const params = new URLSearchParams();
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.per_page !== undefined) {
			params.append("per_page", filters.per_page.toString());
		}
		if (filters.search) {
			params.append("search", filters.search);
		}
		if (filters.status) {
			params.append("status", filters.status);
		}

		const queryString = params.toString();
		const url = queryString
			? `${CLIENTS_ENDPOINTS.base}?${queryString}`
			: CLIENTS_ENDPOINTS.base;

		const headers: Record<string, any> = {};
		if (filters.organization_id) {
			headers["X-organizationId"] = filters.organization_id;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},
	getClientById: async (
		id: string | number,
		organizationId?: number
	): Promise<ClientDetailResponse> => {
		const headers: Record<string, any> = {};
		if (organizationId) {
			headers["X-organizationId"] = organizationId;
		}

		const response = await apiClient.get(CLIENTS_ENDPOINTS.byId(id), {
			headers,
		});
		return response.data;
	},

	createClient: async (
		data: CreateClientRequest,
		organizationId?: number
	): Promise<CreateClientResponse> => {
		const headers: Record<string, any> = {};
		if (organizationId) {
			headers["X-organizationId"] = organizationId;
		}

		const response = await apiClient.post(CLIENTS_ENDPOINTS.base, data, {
			headers,
		});
		return response.data;
	},

	updateClient: async (
		id: string | number,
		data: UpdateClientRequest,
		organizationId?: number
	): Promise<UpdateClientResponse> => {
		const headers: Record<string, any> = {};
		if (organizationId) {
			headers["X-organizationId"] = organizationId;
		}

		const response = await apiClient.put(CLIENTS_ENDPOINTS.byId(id), data, {
			headers,
		});
		return response.data;
	},

	deleteClient: async (
		id: string | number,
		organizationId?: number
	): Promise<void> => {
		const headers: Record<string, any> = {};
		if (organizationId) {
			headers["X-organizationId"] = organizationId;
		}

		await apiClient.delete(CLIENTS_ENDPOINTS.byId(id), {
			headers,
		});
	},
};
