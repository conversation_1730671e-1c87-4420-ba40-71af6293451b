import React, { useState } from "react";
import {
	Search,
	Filter,
	Plus,
	Setting<PERSON>,
	<PERSON>,
	Edit,
	Trash2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";

interface CustomTabProps {
	className?: string;
}

interface CustomConfiguration {
	id: string;
	name: string;
	type: "workflow" | "template" | "rule" | "automation";
	description: string;
	status: "active" | "draft" | "archived";
	lastModified: string;
	createdBy: string;
	usageCount: number;
}

// Mock data for demonstration
const mockConfigurations: CustomConfiguration[] = [
	{
		id: "1",
		name: "Patient Check-in Workflow",
		type: "workflow",
		description:
			"Automated patient check-in process with document verification",
		status: "active",
		lastModified: "2024-01-20",
		createdBy: "<PERSON>. <PERSON>",
		usageCount: 245,
	},
	{
		id: "2",
		name: "Appointment Reminder Template",
		type: "template",
		description: "Custom email template for appointment reminders",
		status: "active",
		lastModified: "2024-01-18",
		createdBy: "Admin User",
		usageCount: 180,
	},
	{
		id: "3",
		name: "Emergency Contact Rule",
		type: "rule",
		description: "Automatic emergency contact notification rules",
		status: "draft",
		lastModified: "2024-01-15",
		createdBy: "Dr. Michael Chen",
		usageCount: 0,
	},
	{
		id: "4",
		name: "Daily Report Automation",
		type: "automation",
		description: "Automated daily reports generation and distribution",
		status: "active",
		lastModified: "2024-01-12",
		createdBy: "System Admin",
		usageCount: 90,
	},
	{
		id: "5",
		name: "Insurance Verification Workflow",
		type: "workflow",
		description: "Streamlined insurance verification process",
		status: "archived",
		lastModified: "2024-01-10",
		createdBy: "Dr. Emily Rodriguez",
		usageCount: 67,
	},
];

export function CustomTab({ className }: CustomTabProps) {
	const [selectedConfigurations, setSelectedConfigurations] = useState<
		string[]
	>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [isLoading] = useState(false);
	const [showCreateForm, setShowCreateForm] = useState(false);

	// Filter configurations based on search term
	const filteredConfigurations = mockConfigurations.filter(
		(config) =>
			config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			config.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
			config.description
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			config.createdBy.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const handleSelectAll = (checked: boolean) => {
		if (checked) {
			setSelectedConfigurations(
				filteredConfigurations.map((config) => config.id)
			);
		} else {
			setSelectedConfigurations([]);
		}
	};

	const handleConfigurationSelection = (
		configId: string,
		selected: boolean
	) => {
		if (selected) {
			setSelectedConfigurations((prev) => [...prev, configId]);
		} else {
			setSelectedConfigurations((prev) =>
				prev.filter((id) => id !== configId)
			);
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "active":
				return "text-green-600 bg-green-100";
			case "draft":
				return "text-yellow-600 bg-yellow-100";
			case "archived":
				return "text-gray-600 bg-gray-100";
			default:
				return "text-gray-600 bg-gray-100";
		}
	};

	const getTypeColor = (type: string) => {
		switch (type) {
			case "workflow":
				return "text-blue-600 bg-blue-100";
			case "template":
				return "text-purple-600 bg-purple-100";
			case "rule":
				return "text-orange-600 bg-orange-100";
			case "automation":
				return "text-teal-600 bg-teal-100";
			default:
				return "text-gray-600 bg-gray-100";
		}
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-1 pl-4">
				<Checkbox
					label="Select All"
					checked={
						selectedConfigurations.length ===
							filteredConfigurations.length &&
						filteredConfigurations.length > 0
					}
					className="border-primary"
					onCheckedChange={handleSelectAll}
				/>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search configurations..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => console.log("Filter configurations")}
					>
						<Filter className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						className="cursor-pointer"
						onClick={() => setShowCreateForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Create Custom
					</Button>
				</div>
			</div>

			{/* Loading State */}
			{isLoading && (
				<div className="grid grid-cols-1 gap-4">
					{[...Array(6)].map((_, i) => (
						<div key={i} className="animate-pulse">
							<div className="h-32 rounded-lg bg-gray-200"></div>
						</div>
					))}
				</div>
			)}

			{/* Configurations List */}
			{!isLoading && (
				<>
					{filteredConfigurations.length === 0 ? (
						<div className="py-12 text-center">
							<Settings className="mx-auto h-12 w-12 text-gray-400" />
							<h3 className="mt-2 text-sm font-medium text-gray-900">
								No custom configurations found
							</h3>
							<p className="mt-1 text-sm text-gray-500">
								Get started by creating your first custom
								configuration.
							</p>
							<Button
								className="mt-4"
								onClick={() => setShowCreateForm(true)}
							>
								<Plus className="mr-2 h-4 w-4" />
								Create Custom
							</Button>
						</div>
					) : (
						<div className="flex flex-col gap-0.5">
							{filteredConfigurations.map((configuration) => (
								<div
									key={configuration.id}
									className="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-50"
								>
									<div className="flex items-center gap-3">
										<Checkbox
											checked={selectedConfigurations.includes(
												configuration.id
											)}
											onCheckedChange={(checked) =>
												handleConfigurationSelection(
													configuration.id,
													checked
												)
											}
										/>
										<Settings className="h-8 w-8 text-gray-400" />
										<div className="flex-1">
											<div className="mb-1 flex items-center gap-2">
												<h4 className="font-medium text-gray-900">
													{configuration.name}
												</h4>
												<span
													className={`rounded-full px-2 py-1 text-xs ${getTypeColor(configuration.type)}`}
												>
													{configuration.type}
												</span>
											</div>
											<p className="mb-1 text-sm text-gray-500">
												{configuration.description}
											</p>
											<p className="text-xs text-gray-400">
												Created by{" "}
												{configuration.createdBy} • Last
												modified{" "}
												{configuration.lastModified}
											</p>
										</div>
									</div>
									<div className="flex items-center gap-4">
										<div className="text-right">
											<p className="text-sm font-medium text-gray-900">
												{configuration.usageCount} uses
											</p>
											<span
												className={`rounded-full px-2 py-1 text-xs ${getStatusColor(configuration.status)}`}
											>
												{configuration.status}
											</span>
										</div>
										<div className="flex items-center gap-1">
											<Button
												variant="ghost"
												size="sm"
												onClick={() =>
													console.log(
														"View configuration:",
														configuration.id
													)
												}
											>
												<Eye className="h-4 w-4" />
											</Button>
											<Button
												variant="ghost"
												size="sm"
												onClick={() =>
													console.log(
														"Edit configuration:",
														configuration.id
													)
												}
											>
												<Edit className="h-4 w-4" />
											</Button>
											<Button
												variant="ghost"
												size="sm"
												onClick={() =>
													console.log(
														"Delete configuration:",
														configuration.id
													)
												}
											>
												<Trash2 className="h-4 w-4 text-red-500" />
											</Button>
										</div>
									</div>
								</div>
							))}
						</div>
					)}
				</>
			)}

			{/* Create Configuration Modal Placeholder */}
			{showCreateForm && (
				<div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
					<div className="mx-4 w-full max-w-md rounded-lg bg-white p-6">
						<h3 className="mb-4 text-lg font-semibold">
							Create Custom Configuration
						</h3>
						<p className="mb-4 text-gray-600">
							Custom configuration builder would go here.
						</p>
						<div className="mb-4 space-y-3">
							<div className="grid grid-cols-2 gap-2">
								<Button variant="outline" size="sm">
									Workflow
								</Button>
								<Button variant="outline" size="sm">
									Template
								</Button>
								<Button variant="outline" size="sm">
									Rule
								</Button>
								<Button variant="outline" size="sm">
									Automation
								</Button>
							</div>
						</div>
						<div className="flex justify-end gap-2">
							<Button
								variant="outline"
								onClick={() => setShowCreateForm(false)}
							>
								Cancel
							</Button>
							<Button onClick={() => setShowCreateForm(false)}>
								Create
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
