import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const notificationVariants = cva(
  "pb-4 inline-flex justify-start items-start gap-4",
  {
    variants: {
      variant: {
        success: "",
        error: "",
        warning: "",
        info: "",
        default: "",
      },
      size: {
        default: "w-full",
        sm: "w-64",
        lg: "w-96",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const statusDotVariants = cva(
  "w-2 h-2 relative rounded-full",
  {
    variants: {
      variant: {
        success: "bg-[#289144]",
        error: "bg-destructive",
        warning: "bg-yellow-500",
        info: "bg-blue-500",
        default: "bg-primary",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface NotificationProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof notificationVariants> {
  title?: string
  description?: string
  asChild?: boolean
}

function Notification({
  className,
  variant,
  size,
  title = "Notification",
  description,
  ...props
}: NotificationProps) {
  return (
    <div
      data-slot="notification"
      className={cn(notificationVariants({ variant, size, className }))}
      {...props}
    >
      <div
        data-slot="notification-status-dot"
        className={cn(statusDotVariants({ variant }))}
      />
      <div className="flex-1 inline-flex flex-col justify-start items-start gap-1">
        <div
          data-slot="notification-title"
          className="self-stretch justify-start text-foreground text-xs font-medium leading-3"
        >
          {title}
        </div>
        {description && (
          <div
            data-slot="notification-description"
            className="self-stretch justify-start text-muted-foreground text-xs font-normal  leading-none"
          >
            {description}
          </div>
        )}
      </div>
    </div>
  )
}

export { Notification, notificationVariants, statusDotVariants }