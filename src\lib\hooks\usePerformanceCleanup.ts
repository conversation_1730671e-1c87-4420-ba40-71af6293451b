import { useEffect, useRef } from "react";
import {
	useUIStore,
	startPerformanceCleanup,
	stopPerformanceCleanup,
} from "@/stores/uiStore";

/**
 * Hook for managing automatic performance cleanup of modals, drawers, and sheets
 * Should be used once at the app level
 */
export function usePerformanceCleanup() {
	const { performanceCleanup } = useUIStore();
	const cleanupInitialized = useRef(false);

	useEffect(() => {
		// Start automatic cleanup only once
		if (!cleanupInitialized.current) {
			startPerformanceCleanup();
			cleanupInitialized.current = true;
		}

		// Cleanup on unmount
		return () => {
			stopPerformanceCleanup();
		};
	}, []);

	// Manual cleanup function
	const manualCleanup = () => {
		performanceCleanup();
	};

	return { manualCleanup };
}

/**
 * Hook for monitoring performance metrics of modals, drawers, and sheets
 */
export function useModalDrawerMetrics() {
	const { modals, drawers, activeModal, activeDrawer } = useUIStore();

	const metrics = {
		totalModalsInMemory: modals.length,
		totalDrawersInMemory: drawers.length,
		hasActiveModal: !!activeModal,
		hasActiveDrawer: !!activeDrawer,
		memoryUsage: {
			modals: modals.length,
			drawers: drawers.length,
			total: modals.length + drawers.length,
		},
	};

	return metrics;
}

/**
 * Hook for modal/drawer/sheet component optimization
 * Use this in individual modal/drawer/sheet components for better performance
 */
export function useComponentCleanup(
	componentId: string,
	onMount?: () => void,
	onUnmount?: () => void
) {
	useEffect(() => {
		onMount?.();

		return () => {
			onUnmount?.();
		};
	}, [componentId, onMount, onUnmount]);
}
