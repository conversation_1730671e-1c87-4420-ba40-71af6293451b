"use client";

import * as React from "react";
import { Plus, Trash2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { TimePicker } from "@/components/common/TimePicker";
import {
	validateTimeSlots,
	type WeeklyScheduleData,
	type TimeSlot,
} from "@/lib/utils/validation";
import { addMinutesToTime } from "@/lib/utils/time-utils";

interface AppliesTo {
	schedule: boolean;
	waitlist: boolean;
}

interface OperatingHoursProps {
	value?: WeeklyScheduleData;
	onChange?: (data: WeeklyScheduleData) => void;
	onValidationChange?: (errors: Record<string, string[]>) => void;
	defaultSlotDuration?: number;
	slotDuration?: number;
	onSlotDurationChange?: (duration: number) => void;
	appliesTo?: AppliesTo;
	onAppliesToChange?: (appliesTo: AppliesTo) => void;
	className?: string;
	disabled?: boolean;
	showAppliesTo?: boolean;
	showSlotDuration?: boolean;
}

const DAYS = [
	{ key: "monday", label: "Monday" },
	{ key: "tuesday", label: "Tuesday" },
	{ key: "wednesday", label: "Wednesday" },
	{ key: "thursday", label: "Thursday" },
	{ key: "friday", label: "Friday" },
	{ key: "saturday", label: "Saturday" },
	{ key: "sunday", label: "Sunday" },
] as const;

const DURATION_OPTIONS = [
	{ value: 10, label: "10 Minutes" },
	{ value: 15, label: "15 Minutes" },
	{ value: 30, label: "30 Minutes" },
	{ value: 45, label: "45 Minutes" },
	{ value: 60, label: "60 Minutes" },
];

const getDefaultSchedule = (): WeeklyScheduleData => ({
	monday: {
		enabled: true,
		slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
	},
	tuesday: {
		enabled: true,
		slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
	},
	wednesday: {
		enabled: true,
		slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
	},
	thursday: {
		enabled: true,
		slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
	},
	friday: {
		enabled: false,
		slots: [{ id: "1", startTime: "09:00 AM", endTime: "06:00 PM" }],
	},
	saturday: {
		enabled: true,
		slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
	},
	sunday: {
		enabled: false,
		slots: [{ id: "1", startTime: "08:00 AM", endTime: "05:00 PM" }],
	},
});

export const OperatingHours = ({
	value,
	onChange,
	onValidationChange,
	defaultSlotDuration = 10,
	slotDuration,
	onSlotDurationChange,
	appliesTo,
	onAppliesToChange,
	className,
	disabled = false,
	showAppliesTo = true,
	showSlotDuration = true,
}: OperatingHoursProps) => {
	const [schedule, setSchedule] = React.useState<WeeklyScheduleData>(
		value || getDefaultSchedule()
	);
	const [internalSlotDuration, setInternalSlotDuration] =
		React.useState(defaultSlotDuration);
	const [internalAppliesTo, setInternalAppliesTo] = React.useState<AppliesTo>(
		{
			schedule: true,
			waitlist: false,
		}
	);
	const [validationErrors, setValidationErrors] = React.useState<
		Record<string, string[]>
	>({});

	// Sync with external value changes
	React.useEffect(() => {
		if (value) {
			setSchedule(value);
		}
	}, [value]);

	// Sync with external slot duration changes
	React.useEffect(() => {
		if (slotDuration !== undefined) {
			setInternalSlotDuration(slotDuration);
		}
	}, [slotDuration]);

	// Sync with external applies to changes
	React.useEffect(() => {
		if (appliesTo) {
			setInternalAppliesTo(appliesTo);
		}
	}, [appliesTo]);

	React.useEffect(() => {
		onValidationChange?.(validationErrors);
	}, [validationErrors, onValidationChange]);

	const updateSchedule = (newSchedule: WeeklyScheduleData) => {
		setSchedule(newSchedule);
		onChange?.(newSchedule);
	};

	const validateDaySlots = (dayKey: string, slots: TimeSlot[]) => {
		const errors = validateTimeSlots(slots);
		setValidationErrors((prev) => ({
			...prev,
			[dayKey]: errors,
		}));
		return errors.length === 0;
	};

	const toggleDay = (dayKey: keyof WeeklyScheduleData) => {
		const newSchedule = {
			...schedule,
			[dayKey]: {
				...schedule[dayKey],
				enabled: !schedule[dayKey].enabled,
			},
		};
		updateSchedule(newSchedule);
	};

	const addTimeSlot = (dayKey: keyof WeeklyScheduleData) => {
		const currentSlots = schedule[dayKey].slots;
		const currentDuration =
			slotDuration !== undefined ? slotDuration : internalSlotDuration;

		// Calculate the new start time based on the last slot's end time + slot duration
		let newStartTime = "08:00 AM";
		let newEndTime = addMinutesToTime(newStartTime, currentDuration);

		if (currentSlots.length > 0) {
			const lastSlot = currentSlots[currentSlots.length - 1];
			newStartTime = addMinutesToTime(lastSlot.endTime, currentDuration);
			newEndTime = addMinutesToTime(newStartTime, currentDuration);
		}

		const newSlot: TimeSlot = {
			id: Date.now().toString(),
			startTime: newStartTime,
			endTime: newEndTime,
		};

		const newSchedule = {
			...schedule,
			[dayKey]: {
				...schedule[dayKey],
				slots: [...currentSlots, newSlot],
			},
		};
		updateSchedule(newSchedule);

		// Validate after adding
		const updatedSlots = [...currentSlots, newSlot];
		validateDaySlots(dayKey.toString(), updatedSlots);
	};

	const removeTimeSlot = (
		dayKey: keyof WeeklyScheduleData,
		slotId: string
	) => {
		const newSchedule = {
			...schedule,
			[dayKey]: {
				...schedule[dayKey],
				slots: schedule[dayKey].slots.filter(
					(slot: TimeSlot) => slot.id !== slotId
				),
			},
		};
		updateSchedule(newSchedule);

		// Validate after removing
		validateDaySlots(dayKey.toString(), newSchedule[dayKey].slots);
	};

	const updateTimeSlot = (
		dayKey: keyof WeeklyScheduleData,
		slotId: string,
		field: "startTime" | "endTime",
		value: string
	) => {
		const newSchedule = {
			...schedule,
			[dayKey]: {
				...schedule[dayKey],
				slots: schedule[dayKey].slots.map((slot: TimeSlot) =>
					slot.id === slotId ? { ...slot, [field]: value } : slot
				),
			},
		};
		updateSchedule(newSchedule);

		// Validate after updating
		setTimeout(() => {
			validateDaySlots(dayKey.toString(), newSchedule[dayKey].slots);
		}, 0);
	};

	const handleSlotDurationChange = (duration: number) => {
		if (onSlotDurationChange) {
			onSlotDurationChange(duration);
		} else {
			setInternalSlotDuration(duration);
		}
	};

	const handleAppliesToChange = (key: keyof AppliesTo, checked: boolean) => {
		const newAppliesTo = {
			...(appliesTo || internalAppliesTo),
			[key]: checked,
		};

		if (onAppliesToChange) {
			onAppliesToChange(newAppliesTo);
		} else {
			setInternalAppliesTo(newAppliesTo);
		}
	};

	const currentSlotDuration =
		slotDuration !== undefined ? slotDuration : internalSlotDuration;
	const currentAppliesTo = appliesTo || internalAppliesTo;

	return (
		<div className={className}>
			{/* Header */}
			<div className="mb-6 flex items-center justify-end gap-3">
				<h3 className="text-sm font-medium">Default Time Slots</h3>
				{showSlotDuration && (
					<div className="flex items-center gap-2">
						<Select
							value={currentSlotDuration.toString()}
							onValueChange={(value) =>
								handleSlotDurationChange(Number(value))
							}
							disabled={disabled}
						>
							<SelectTrigger className="w-32">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								{DURATION_OPTIONS.map((option) => (
									<SelectItem
										key={option.value}
										value={option.value.toString()}
									>
										{option.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				)}
			</div>

			{/* Days Schedule */}
			<div className="">
				{DAYS.map(({ key, label }) => {
					const daySchedule = schedule[key];
					return (
						<div key={key} className="space-y-2 border-b py-2">
							<div className="flex items-start gap-20">
								{/* Day Toggle */}
								<div className="mt-[15px] flex w-[141px] items-center gap-1.5">
									<Switch
										checked={daySchedule.enabled}
										onCheckedChange={() => toggleDay(key)}
										disabled={disabled}
									/>
									<span className="text-muted-foreground pr-2 text-sm">
										{daySchedule.enabled ? "On" : "Off"}
									</span>
									<Label className="text-sm font-medium text-[#3B5566]">
										{label}
									</Label>
								</div>

								{/* Time Slots */}
								<div className="flex-1 space-y-2">
									{daySchedule.slots.map(
										(slot: TimeSlot, index: number) => (
											<div
												key={slot.id}
												className="flex items-center justify-between gap-2"
											>
												<div className="flex items-center gap-4">
													<TimePicker
														value={slot.startTime}
														onChange={(time) =>
															updateTimeSlot(
																key,
																slot.id,
																"startTime",
																time
															)
														}
														disabled={
															!daySchedule.enabled ||
															disabled
														}
														className="w-[199px]"
													/>

													<TimePicker
														value={slot.endTime}
														onChange={(time) =>
															updateTimeSlot(
																key,
																slot.id,
																"endTime",
																time
															)
														}
														disabled={
															!daySchedule.enabled ||
															disabled
														}
														className="w-[199px]"
													/>
												</div>

												{/* Action Buttons */}
												<div className="flex items-center gap-1">
													{daySchedule.slots.length >
														1 && (
														// index !==
														// 	daySchedule.slots
														// 		.length -
														// 		1 &&
														<Button
															type="button"
															variant="ghost"
															size="sm"
															onClick={() =>
																removeTimeSlot(
																	key,
																	slot.id
																)
															}
															disabled={
																!daySchedule.enabled ||
																disabled
															}
															className="text-muted-foreground hover:text-destructive h-8 w-8 p-0"
														>
															<Trash2 className="h-4 w-4" />
														</Button>
													)}
													{index ===
														daySchedule.slots
															.length -
															1 && (
														<Button
															type="button"
															variant="ghost"
															size="sm"
															onClick={() =>
																addTimeSlot(key)
															}
															disabled={
																!daySchedule.enabled ||
																disabled
															}
															className="h-8 w-8 p-0"
														>
															<Plus className="h-4 w-4" />
														</Button>
													)}
												</div>
											</div>
										)
									)}
								</div>
							</div>

							{/* Validation Errors */}
							{validationErrors[key] &&
								validationErrors[key].length > 0 && (
									<div className="ml-[222px] space-y-1">
										{validationErrors[key].map(
											(error, index) => (
												<p
													key={index}
													className="text-destructive text-sm"
												>
													{error}
												</p>
											)
										)}
									</div>
								)}
						</div>
					);
				})}
			</div>

			{/* Applies To Section */}
			{showAppliesTo && (
				<div className="border-b py-6">
					<div className="flex items-center gap-4">
						<Label className="text-sm font-medium">
							Applies to
						</Label>
						<div className="flex items-center gap-4">
							<div className="flex items-center space-x-2">
								<Checkbox
									id="schedule"
									checked={currentAppliesTo.schedule}
									onCheckedChange={(checked) =>
										handleAppliesToChange(
											"schedule",
											checked as boolean
										)
									}
									disabled={disabled}
								/>
								<Label htmlFor="schedule" className="text-sm">
									Schedule
								</Label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="waitlist"
									checked={currentAppliesTo.waitlist}
									onCheckedChange={(checked) =>
										handleAppliesToChange(
											"waitlist",
											checked as boolean
										)
									}
									disabled={disabled}
								/>
								<Label htmlFor="waitlist" className="text-sm">
									Waitlist
								</Label>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};
