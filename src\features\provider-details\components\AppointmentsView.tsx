import { useState, type FC } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	TabsTrigger,
} from "@/components/common/Tabs";
import { AppointmentCard } from "./overview/tabs-content/AppointmentCard";

interface AppointmentsViewProps {
	className?: string;
}

export const AppointmentsView: FC<AppointmentsViewProps> = ({ className }) => {
	const [activeTab, setActiveTab] = useState("upcoming");

	const items = [
		{ value: "upcoming", label: "Upcoming" },
		{ value: "history", label: "History" },
	];

	return (
		<div className="w-full">
			<Tabs
				items={items}
				defaultValue="upcoming"
				value={activeTab}
				onValueChange={setActiveTab}
			>
				<div className="scrollbar-hide max-h-screen overflow-y-scroll">
					<TabsContent value="upcoming">
						<AppointmentCard />
					</TabsContent>
					<TabsContent value="history">
						<AppointmentCard />
					</TabsContent>
				</div>
			</Tabs>
		</div>
	);
};
