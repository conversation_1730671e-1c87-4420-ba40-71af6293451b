import { type NavigateFunction } from 'react-router';

export type Level = 'organization' | 'location' | 'provider';

export interface NavigationHelpers {
  getBasePath: (level: Level, entityId?: string) => string;
  navigateToPreferences: (navigate: NavigateFunction, level: Level, entityId?: string) => void;
  navigateToAddService: (navigate: NavigateFunction, level: Level, entityId?: string) => void;
  navigateToServiceRules: (navigate: NavigateFunction, level: Level, entityId: string, serviceId: string) => void;
  navigateToConflicts: (navigate: NavigateFunction, level: Level, entityId?: string) => void;
  navigateToSuccess: (navigate: NavigateFunction, level: Level, entityId?: string) => void;
}

export const navigationHelpers: NavigationHelpers = {
  getBasePath: (level: Level, entityId?: string) => {
    switch (level) {
      case 'organization':
        return '/dashboard/schedule/planner/organization';
      case 'location':
        return `/dashboard/schedule/planner/location/${entityId}`;
      case 'provider':
        return `/dashboard/schedule/planner/provider/${entityId}`;
      default:
        return '/dashboard/schedule/planner/organization';
    }
  },

  navigateToPreferences: (navigate: NavigateFunction, level: Level, entityId?: string) => {
    const basePath = navigationHelpers.getBasePath(level, entityId);
    navigate(basePath);
  },

  navigateToAddService: (navigate: NavigateFunction, level: Level, entityId?: string) => {
    const basePath = navigationHelpers.getBasePath(level, entityId);
    navigate(`${basePath}/services/add`);
  },

  navigateToServiceRules: (navigate: NavigateFunction, level: Level, entityId: string, serviceId: string) => {
    const basePath = navigationHelpers.getBasePath(level, entityId);
    navigate(`${basePath}/services/${serviceId}/rules`);
  },

  navigateToConflicts: (navigate: NavigateFunction, level: Level, entityId?: string) => {
    const basePath = navigationHelpers.getBasePath(level, entityId);
    navigate(`${basePath}/services/conflicts`);
  },

  navigateToSuccess: (navigate: NavigateFunction, level: Level, entityId?: string) => {
    const basePath = navigationHelpers.getBasePath(level, entityId);
    navigate(basePath, { 
      state: { 
        showSuccess: true, 
        preferenceData: {} 
      }
    });
  }
};

// hooks/useCurrentContext.ts




// Simplified index.tsx (remove complex wrapper components)


// Key Benefits of This Refactor:

/*
1. **Route-Based Navigation**: All major navigation is handled through routes, not complex state
2. **Simplified Components**: Removed complex container components that managed view state
3. **Consistent Patterns**: All levels (org/location/provider) follow the same URL pattern
4. **Modal State Only**: States are only used for modals/temporary UI, not navigation
5. **Reusable Helpers**: Navigation logic is centralized and reusable

Key Changes to Make:

1. **Remove OrganizationPreferencesContainer** - It's adding unnecessary complexity
2. **Remove ServicePreferencesContainer** - Routes handle the flow instead
3. **Simplify AddServicePreference** - Remove onBack/onSave props, use navigation directly
4. **Update all components** to use useCurrentContext() and navigationHelpers

URL Structure (Clean & Consistent):
- /dashboard/schedule/planner
- /dashboard/schedule/planner/organization
- /dashboard/schedule/planner/organization/services/add
- /dashboard/schedule/planner/organization/services/123/rules
- /dashboard/schedule/planner/location/456
- /dashboard/schedule/planner/location/456/services/add
- /dashboard/schedule/planner/provider/789/services/conflicts

Benefits:
✅ Bookmarkable URLs
✅ Browser back/forward works
✅ No complex state management
✅ Easy to understand flow
✅ Consistent patterns
✅ Easy to debug
*/

// Example of simplified component pattern:
// import React from 'react';
// import { useNavigate } from 'react-router-dom';
// import { useCurrentContext } from '../hooks/useCurrentContext';
// import { navigationHelpers } from '../utils/navigation';

// export const SimpleComponent: React.FC = () => {
//   const navigate = useNavigate();
//   const context = useCurrentContext();
  
//   const handleAction = () => {
//     // Simple, direct navigation
//     navigationHelpers.navigateToAddService(navigate, context.level, context.entityId);
//   };

//   return (
//     <div>
//       <h1>Current Level: {context.level}</h1>
//       <h2>Entity: {context.entityName}</h2>
//       <button onClick={handleAction}>Add Service</button>
//     </div>
//   );
// };