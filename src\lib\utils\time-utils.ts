// Helper function to convert 12-hour time to minutes for comparison
export function timeToMinutes(timeString: string): number {
	const [time, period] = timeString.split(" ");
	const [hours, minutes] = time.split(":").map(Number);

	let totalMinutes = minutes;
	if (period === "AM") {
		totalMinutes += hours === 12 ? 0 : hours * 60;
	} else {
		totalMinutes += hours === 12 ? 12 * 60 : (hours + 12) * 60;
	}

	return totalMinutes;
}

// Helper function to convert minutes back to 12-hour time format
export function minutesToTime(totalMinutes: number): string {
	// Handle day overflow
	totalMinutes = totalMinutes % (24 * 60);
	if (totalMinutes < 0) totalMinutes += 24 * 60;

	const hours24 = Math.floor(totalMinutes / 60);
	const minutes = totalMinutes % 60;

	let hours12 = hours24 % 12;
	if (hours12 === 0) hours12 = 12;

	const period = hours24 < 12 ? "AM" : "PM";

	return `${hours12.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")} ${period}`;
}

// Helper function to add minutes to a time string
export function addMinutesToTime(
	timeString: string,
	minutesToAdd: number
): string {
	const currentMinutes = timeToMinutes(timeString);
	const newMinutes = currentMinutes + minutesToAdd;
	return minutesToTime(newMinutes);
}
