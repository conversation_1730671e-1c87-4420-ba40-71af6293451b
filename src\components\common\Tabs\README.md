# Tabs Component

A reusable Tab component built with Radix UI primitives that matches the Figma design specifications. The component supports optional count badges in tab headers.

## Features

- ✅ Built with Radix UI for accessibility
- ✅ TypeScript support with proper type definitions
- ✅ Optional count badges in tab headers
- ✅ Disabled state support
- ✅ Responsive design
- ✅ Matches Figma design specifications
- ✅ Customizable styling with Tailwind CSS

## Usage

### Basic Usage

```tsx
import { Tabs, TabsContent } from "@/components/common/Tabs";

const items = [
	{ value: "organization", label: "Organization", count: 12 },
	{ value: "location", label: "Location" },
	{ value: "provider", label: "Provider" },
	{ value: "custom", label: "Custom", count: 12 },
];

function MyComponent() {
	return (
		<Tabs items={items} defaultValue="organization">
			<TabsContent value="organization">
				<div>Organization content here</div>
			</TabsContent>
			<TabsContent value="location">
				<div>Location content here</div>
			</TabsContent>
			<TabsContent value="provider">
				<div>Provider content here</div>
			</TabsContent>
			<TabsContent value="custom">
				<div>Custom content here</div>
			</TabsContent>
		</Tabs>
	);
}
```

### With State Management

```tsx
import { useState } from "react";
import { Tabs, TabsContent } from "@/components/common/Tabs";

function MyComponent() {
	const [activeTab, setActiveTab] = useState("organization");

	const items = [
		{ value: "organization", label: "Organization", count: 12 },
		{ value: "location", label: "Location" },
		{ value: "provider", label: "Provider" },
		{ value: "custom", label: "Custom", count: 12 },
	];

	return (
		<Tabs items={items} value={activeTab} onValueChange={setActiveTab}>
			<TabsContent value="organization">
				<div>Organization content here</div>
			</TabsContent>
			{/* ... other tab contents */}
		</Tabs>
	);
}
```

### Disabled Tabs

```tsx
const items = [
  { value: 'active', label: 'Active', count: 5 },
  { value: 'disabled', label: 'Disabled', count: 0, disabled: true },
  { value: 'another', label: 'Another' },
]

<Tabs items={items} defaultValue="active">
  {/* ... tab contents */}
</Tabs>
```

## Props

### Tabs Props

| Prop            | Type                      | Default  | Description                                                        |
| --------------- | ------------------------- | -------- | ------------------------------------------------------------------ |
| `items`         | `TabItem[]`               | required | Array of tab items to render                                       |
| `defaultValue`  | `string`                  | -        | The value of the tab that should be active when initially rendered |
| `value`         | `string`                  | -        | The controlled value of the tab to activate                        |
| `onValueChange` | `(value: string) => void` | -        | Event handler called when the value changes                        |
| `className`     | `string`                  | -        | Additional CSS classes                                             |

### TabItem Interface

```tsx
interface TabItem {
	value: string; // Unique identifier for the tab
	label: string; // Display text for the tab
	count?: number; // Optional count badge (only shows if provided)
	disabled?: boolean; // Whether the tab is disabled
}
```

### TabsContent Props

| Prop        | Type              | Default  | Description                           |
| ----------- | ----------------- | -------- | ------------------------------------- |
| `value`     | `string`          | required | The value that activates this content |
| `className` | `string`          | -        | Additional CSS classes                |
| `children`  | `React.ReactNode` | -        | Content to display when tab is active |

## Design Specifications

The component follows the Figma design with these specifications:

- **Container**: Gray background (`bg-zinc-100`) with rounded corners (`rounded-lg`)
- **Active tab**: White background (`bg-white`) with shadow
- **Inactive tabs**: Transparent background with gray text (`text-zinc-500`)
- **Count badges**: Secondary badge style with zinc colors
- **Typography**: Inter font, medium weight, 14px size
- **Spacing**: Consistent padding and gaps as per design

## Accessibility

The component is built with Radix UI primitives and includes:

- ✅ Keyboard navigation (Arrow keys, Tab, Enter, Space)
- ✅ ARIA attributes for screen readers
- ✅ Focus management
- ✅ Disabled state handling

## Examples

See `Tabs.stories.tsx` for comprehensive usage examples including:

- Default tabs with counts
- Tabs without counts
- Tabs with disabled states
