export interface BusinessAttributeOption {
	id?: string | number;
	value: string;
	label: string;
}

export interface BusinessAttribute {
	id: number;
	key: string;
	label: string;
	type:
		| "dropdown"
		| "text"
		| "number"
		| "date"
		| "checkbox"
		| "textarea"
		| "email"
		| "phone"
		| "boolean"
		| "attachment";
	options: BusinessAttributeOption[];
	is_required: boolean;
	is_basic_attribute?: boolean;
	is_system_field: boolean;
	show_in_list?: boolean;
	import_column_name: string;
	field_config_id?: number;
	emr_sync_id?: number | null;
	is_validator: boolean;
	business_id?: number;
}

export interface BusinessAttributesMeta {
	total: number;
	page: number;
	per_page: number;
	total_pages: number;
}

export interface BusinessAttributesResponse {
	success: boolean;
	message: string;
	data: BusinessAttribute[];
	meta: BusinessAttributesMeta;
}

export interface BusinessAttributesFilters {
	page?: number;
	per_page?: number;
	search?: string;
	type?: BusinessAttribute["type"];
	is_required?: boolean;
	show_in_list?: boolean;
}

export interface BusinessAttributesError {
	success: false;
	message: string;
	errors?: string[];
}

export interface ConditionalAttributeOperator {
	key: string;
	label: string;
}

export interface ConditionalAttribute {
	id: number;
	label: string;
	type:
		| "number"
		| "text"
		| "date"
		| "dropdown"
		| "checkbox"
		| "email"
		| "phone"
		| "boolean"
		| "attachment";
	is_system_field: boolean;
	operators: ConditionalAttributeOperator[];
}

export interface ConditionalAttributesResponse {
	success: boolean;
	data: ConditionalAttribute[];
	message: string;
}
