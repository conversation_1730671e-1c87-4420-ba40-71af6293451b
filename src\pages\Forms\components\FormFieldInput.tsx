import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { useFieldArray } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import ConditionalOptions from "./ConditionalOptions";
import { createField } from "@/pages/Forms/types";
import { DateValidationOptions } from "./DateValidationOptions";

import { Copy, Minus, Plus, Trash2 } from "lucide-react";

const customFieldTypeOptions = [
	{ value: "text", label: "Text" },
	{ value: "longtext", label: "Long Text" },
	{ value: "numeric", label: "Number" },
	{ value: "date", label: "Date" },
	{ value: "date_range", label: "Date Range" },
	{ value: "dropdown", label: "Dropdown" },
	{ value: "radio", label: "Radio" },
	{ value: "checkbox", label: "Checkbox" },
	{ value: "attachment", label: "Attachment" },
	{ value: "infoImage", label: "Informational Image" },
	{ value: "infoText", label: "Informational Text" },
];

export const FormFieldInput = ({
	nestIndex,
	field,
	control,
	watch,
	setValue,
	getValues,
	clearErrors,
	form,
}: {
	nestIndex: number;
	field: any;
	control: any;
	watch: any;
	setValue: any;
	getValues: any;
	clearErrors: any;
	form: any;
}) => {
	const [showDescription, setShowDescription] = useState(false);
	const fieldValue = field?.value || {};
	const fieldType = fieldValue.type || "text";

	const handleTypeChange = (value: string) => {
		const currentValues = getValues(
			`sections.${nestIndex}.fields.${field.name}`
		);
		const preserveValues = {
			title: currentValues?.title || "",
			description: currentValues?.description || "",
		};

		const newField = createField(value, preserveValues);

		setValue(`sections.${nestIndex}.fields.${field.name}`, newField, {
			shouldValidate: false,
			shouldDirty: true,
			shouldTouch: false,
		});

		const fieldPath = `sections.${nestIndex}.fields.${field.name}`;
		clearErrors(fieldPath);
	};

	const { fields: sectionFields, update: updateSection } = useFieldArray({
		control,
		name: "sections",
	});

	const { remove } = useFieldArray({
		control,
		name: `sections.${nestIndex}.fields`,
	});

	const handleDuplicateField = (sectionIndex: number, fieldIndex: number) => {
		const section: any = sectionFields[sectionIndex];

		if (section && section.fields) {
			const fieldToDuplicate = section.fields[fieldIndex];
			if (fieldToDuplicate) {
				const currentValues = getValues(
					`sections.${sectionIndex}.fields.${fieldIndex}`
				);

				const duplicatedField = {
					...fieldToDuplicate,
					...currentValues,
					id: crypto.randomUUID(),
				};

				const newFields = [...section.fields];
				newFields.splice(fieldIndex + 1, 0, duplicatedField);

				const updatedSection = {
					...section,
					fields: newFields,
				};

				updateSection(sectionIndex, updatedSection);
			}
		}
	};

	const handleDelete = () => {
		remove(field.name);
	};

	return (
		<div className="space-y-4">
			<div className="flex flex-col gap-6">
				<div className="flex-1">
					<FormField
						control={control}
						name={`sections.${nestIndex}.fields.${field.name}.title`}
						render={({ field: titleField }) => (
							<FormItem>
								<FormLabel className="text-base font-semibold">
									Field Title{" "}
									<span className="text-red-500">*</span>
								</FormLabel>
								<FormControl>
									<Input
										{...titleField}
										placeholder="Field Title"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
				{!showDescription && (
					<button
						type="button"
						className="ml-5 flex items-center gap-2 text-sm font-medium text-[#323539]"
						onClick={() => setShowDescription(!showDescription)}
					>
						<Plus
							color="#fff"
							className="bg-primary rounded-full"
							size={12}
						/>
						Add Description
					</button>
				)}
				{showDescription && (
					<div>
						<FormField
							control={control}
							name={`sections.${nestIndex}.fields.${field.name}.description`}
							render={({ field: descriptionField }) => (
								<FormItem>
									<FormLabel>Description</FormLabel>
									<FormControl>
										<Textarea
											{...descriptionField}
											placeholder="Add a description for this field"
											className="mt-2"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<button
							type="button"
							className="!mt-1 ml-5 flex items-center gap-2 text-sm font-medium text-[#323539]"
							onClick={() => setShowDescription(false)}
						>
							<Minus
								color="#fff"
								className="rounded-full bg-red-600"
								size={12}
							/>
							Hide Description
						</button>
					</div>
				)}
				<div className="w-full">
					<FormField
						control={control}
						name={`sections.${nestIndex}.fields.${field.name}.type`}
						render={({ field: typeField }) => (
							<FormItem>
								<FormLabel className="text-base font-semibold">
									Field Type{" "}
									<span className="text-red-500">*</span>
								</FormLabel>
								<Select
									onValueChange={(value) => {
										handleTypeChange(value as string);
										typeField.onChange(value);
									}}
									value={typeField.value}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Select type" />
									</SelectTrigger>
									<SelectContent>
										{customFieldTypeOptions.map(
											(option) => (
												<SelectItem
													key={option.value}
													value={option.value}
												>
													{option.label}
												</SelectItem>
											)
										)}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{(fieldType === "checkbox" ||
				fieldType === "radio" ||
				fieldType === "dropdown" ||
				fieldType === "attachment" ||
				fieldType === "infoImage" ||
				fieldType === "infoText") && (
				<ConditionalOptions
					nestIndex={nestIndex}
					field={field}
					control={control}
					watch={watch}
					setValue={setValue}
				/>
			)}
			<DateValidationOptions
				sectionIndex={nestIndex}
				field={field}
				control={control}
				watch={watch}
				form={form}
			/>

			<div className="flex items-center justify-end space-x-3 divide-x-2">
				<div className="flex items-center space-x-3">
					<Button
						type="button"
						variant="outline"
						className="cursor-pointer"
						onClick={() =>
							handleDuplicateField(nestIndex, field.name)
						}
					>
						<Copy className="h-2.5 w-2.5 text-black" />
					</Button>
					<Button
						type="button"
						variant="outline"
						onClick={handleDelete}
						className="mr-3 cursor-pointer"
					>
						<Trash2 className="h-2.5 w-2.5 text-black" />
					</Button>
				</div>
				<div className="flex items-center space-x-2 pl-3">
					<Switch
						checked={field.value?.required}
						onCheckedChange={(checked) =>
							field.onChange({
								...field.value,
								required: checked,
							})
						}
						disabled={field.value?.type === "infoImage"}
					/>
					<span className="text-sm font-medium">Required</span>
				</div>
			</div>
		</div>
	);
};
