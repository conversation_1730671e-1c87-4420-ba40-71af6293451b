import { useEffect, useState, type FC } from "react";
import { StationDetailsCard } from "@/features/locations";
import { LocationProviderDetailsList } from "@/features/provider-details";
import { useUIStore } from "@/stores/uiStore";
import type { StationDetails } from "@/features/locations/components/StationDetailsCard";
import { StationInformationSheet } from "@/features/locations/components/sheets/station-information";
import { SendBookingLinkSheet } from "@/features/schedule/components/SendBookingLinkSheet";
import { useParams } from "react-router";

const LocationProviderDetails: FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);
	const [openOrganizationDetailsSheet, setOpenOrganizationDetailsSheet] =
		useState(false);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);
	const [showStationDetails, setShowStationDetails] = useState(false);
	const { providerId } = useParams();

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Workplace",
				href: "/dashboard/workplace",
			},
			{
				label: "Organizations",
				href: "/dashboard/workplace/locations",
			},
			{
				label: "Locations",
				href: `/dashboard/workplace/providers/${providerId}`,
			},
			{
				label: "[Station Name]",
				isCurrentPage: true,
			},
		]);
		setCurrentPageTitle("Provider Details");

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, setCurrentPageTitle, providerId]);

	const handleView = (station: StationDetails) => {
		console.log("View:", station.name);
		setOpenOrganizationDetailsSheet(true);
	};

	return (
		<div className="flex flex-col gap-4 py-6">
			<StationDetailsCard
				station={{
					id: "org-1",
					name: "HealthCare Plus Medical Center",
					address:
						"123 Main Street, Downtown Medical District, NY 10001",
					rating: 4,
					locationsCount: 4,
					providersCount: 20,
					averageWaitTime: "2 hr 30 mins",
				}}
				onView={handleView}
				onEdit={(station) => console.log("Edit:", station.name)}
				onDelete={(station) => console.log("Delete:", station.name)}
			/>
			<LocationProviderDetailsList />
			{/* Station Information Sheet */}
			<StationInformationSheet
				open={showStationDetails}
				onClose={() => setShowStationDetails(false)}
				onSendBookingLink={() => setShowSendBookingLinkSheet(true)}
			/>
			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>
		</div>
	);
};

export default LocationProviderDetails;
