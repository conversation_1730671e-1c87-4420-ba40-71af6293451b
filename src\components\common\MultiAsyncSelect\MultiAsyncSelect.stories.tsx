import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { useState, useEffect } from "react";
import { MultiAsyncSelect } from "./MultiAsyncSelect";
import type { Option } from "./types";
import { createOption } from "./types";

const meta: Meta<typeof MultiAsyncSelect> = {
	title: "Components/MultiAsyncSelect",
	component: MultiAsyncSelect,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"A multi-select dropdown component with async support, search functionality, and customizable options display.",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		async: {
			control: "boolean",
			description: "Whether the select is async",
		},
		loading: {
			control: "boolean",
			description: "Whether is fetching options",
		},
		maxCount: {
			control: "number",
			description: "Maximum number of items to display",
		},
		modalPopover: {
			control: "boolean",
			description: "The modality of the popover",
		},
		placeholder: {
			control: "text",
			description: "Placeholder text when no values are selected",
		},
		searchPlaceholder: {
			control: "text",
			description: "Placeholder text for search input",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

const frameworks: Option[] = [
	createOption("react", "React"),
	createOption("vue", "Vue.js"),
	createOption("angular", "Angular"),
	createOption("svelte", "Svelte"),
	createOption("next.js", "Next.js"),
	createOption("nuxt", "Nuxt.js"),
	createOption("sveltekit", "SvelteKit"),
	createOption("remix", "Remix"),
	createOption("express", "Express.js"),
	createOption("fastify", "Fastify"),
];

const countries: Option[] = [
	createOption("us", "United States"),
	createOption("ca", "Canada"),
	createOption("uk", "United Kingdom"),
	createOption("de", "Germany"),
	createOption("fr", "France"),
	createOption("jp", "Japan"),
	createOption("au", "Australia"),
	createOption("in", "India"),
	createOption("br", "Brazil"),
	createOption("mx", "Mexico"),
];

const skills: Option[] = [
	createOption("typescript", "TypeScript"),
	createOption("javascript", "JavaScript"),
	createOption("python", "Python"),
	createOption("java", "Java"),
	createOption("csharp", "C#"),
	createOption("golang", "Go"),
	createOption("rust", "Rust"),
	createOption("php", "PHP"),
	createOption("swift", "Swift"),
	createOption("kotlin", "Kotlin"),
];

export const Default: Story = {
	args: {
		placeholder: "Select frameworks...",
		options: frameworks,
		onValueChange: (value) => console.log("Selected:", value),
	},
};

export const WithDefaultValues: Story = {
	args: {
		placeholder: "Select frameworks...",
		options: frameworks,
		defaultValue: ["react", "next.js"],
		onValueChange: (value) => console.log("Selected:", value),
	},
};

export const CustomMaxCount: Story = {
	args: {
		placeholder: "Select skills...",
		options: skills,
		maxCount: 5,
		defaultValue: [
			"typescript",
			"javascript",
			"python",
			"java",
			"csharp",
			"golang",
		],
		onValueChange: (value) => console.log("Selected:", value),
	},
};

export const AsyncExample: Story = {
	render: () => {
		const [options, setOptions] = useState<Option[]>([]);
		const [loading, setLoading] = useState(false);
		const [error, setError] = useState<Error | null>(null);
		const [selectedValues, setSelectedValues] = useState<string[]>([]);

		const simulateApiCall = (searchTerm: string) => {
			setLoading(true);
			setError(null);

			// Simulate API delay
			setTimeout(() => {
				try {
					const filteredCountries = countries.filter((country) =>
						country.label
							.toLowerCase()
							.includes(searchTerm.toLowerCase())
					);

					// Simulate random error occasionally
					if (Math.random() < 0.1) {
						throw new Error("Failed to fetch data");
					}

					setOptions(filteredCountries);
				} catch (err) {
					setError(err as Error);
					setOptions([]);
				} finally {
					setLoading(false);
				}
			}, 800);
		};

		const handleSearch = (searchTerm: string) => {
			simulateApiCall(searchTerm);
		};

		useEffect(() => {
			// Initial load
			simulateApiCall("");
		}, []);

		return (
			<div className="w-full max-w-md">
				<MultiAsyncSelect
					async
					loading={loading}
					error={error}
					options={options}
					placeholder="Search countries..."
					searchPlaceholder="Type to search..."
					onValueChange={setSelectedValues}
					onSearch={handleSearch}
				/>
				{selectedValues.length > 0 && (
					<div className="mt-4 rounded bg-gray-50 p-3">
						<p className="text-sm font-medium">Selected values:</p>
						<p className="text-sm text-gray-600">
							{JSON.stringify(selectedValues)}
						</p>
					</div>
				)}
			</div>
		);
	},
};

export const CustomTexts: Story = {
	args: {
		placeholder: "Seleccionar opciones...",
		searchPlaceholder: "Buscar...",
		clearText: "Limpiar",
		closeText: "Cerrar",
		options: frameworks,
		onValueChange: (value) => console.log("Selected:", value),
	},
};

export const ModalPopover: Story = {
	args: {
		placeholder: "Select frameworks...",
		options: frameworks,
		modalPopover: true,
		onValueChange: (value) => console.log("Selected:", value),
	},
};

export const AllFeatures: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"react",
		]);

		return (
			<div className="w-full max-w-md space-y-6">
				<div>
					<h3 className="mb-2 text-lg font-semibold">
						Multi-Select with All Features
					</h3>
					<MultiAsyncSelect
						placeholder="Select your tech stack..."
						searchPlaceholder="Search technologies..."
						options={[...frameworks, ...skills]}
						defaultValue={selectedValues}
						maxCount={2}
						clearText="Clear All"
						closeText="Done"
						onValueChange={setSelectedValues}
						className="w-full"
					/>
				</div>

				{selectedValues.length > 0 && (
					<div className="rounded-lg bg-blue-50 p-4">
						<p className="text-sm font-medium text-blue-900">
							Selected Technologies:
						</p>
						<ul className="mt-2 space-y-1">
							{selectedValues.map((value) => {
								const option = [...frameworks, ...skills].find(
									(opt) => opt.value === value
								);
								return (
									<li
										key={value}
										className="text-sm text-blue-700"
									>
										• {option?.label || value}
									</li>
								);
							})}
						</ul>
					</div>
				)}
			</div>
		);
	},
};
