import { apiClient } from "@/lib/api/clients";
import type {
	Location,
	LocationsResponse,
	LocationsFilters,
	CreateLocationRequest,
	UpdateLocationRequest,
} from "../types";

const LOCATIONS_ENDPOINTS = {
	base: "/api/v1/locations",
	byId: (id: string) => `/api/v1/locations/${id}`,
} as const;

export const locationsApi = {
	// Get all locations with filters
	getLocations: async (
		filters: LocationsFilters = {},
		orgId: number
	): Promise<LocationsResponse["data"]> => {
		const params = new URLSearchParams();

		Object.entries(filters).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				if (Array.isArray(value)) {
					value.forEach((item) =>
						params.append(key, item.toString())
					);
				} else {
					params.append(key, value.toString());
				}
			}
		});
		params.append(
			"include",
			"provider_count,service_count,avg_waittime,stations"
		);
		const response = await apiClient.get(
			`${LOCATIONS_ENDPOINTS.base}?${params}`,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data.data;
	},

	// Get single location by ID
	getLocation: async (id: string, orgId: number): Promise<Location> => {
		const response = await apiClient.get(LOCATIONS_ENDPOINTS.byId(id), {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data.data;
	},

	// Create new location
	createLocation: async (
		data: CreateLocationRequest,
		orgId: number
	): Promise<Location> => {
		const response = await apiClient.post(LOCATIONS_ENDPOINTS.base, data, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Update existing location
	updateLocation: async (
		data: UpdateLocationRequest,
		orgId: number
	): Promise<Location> => {
		const { id, ...updateData } = data;
		const response = await apiClient.put(
			LOCATIONS_ENDPOINTS.byId(id),
			updateData,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data;
	},

	// Delete location
	deleteLocation: async (id: string, orgId: number): Promise<void> => {
		await apiClient.delete(LOCATIONS_ENDPOINTS.byId(id), {
			headers: {
				"X-organizationId": orgId,
			},
		});
	},

	// Toggle location active status
	toggleLocationStatus: async (
		id: string,
		isActive: boolean,
		orgId: number
	): Promise<Location> => {
		const response = await apiClient.patch(
			LOCATIONS_ENDPOINTS.byId(id),
			{ isActive },
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data;
	},
};
