import { useState } from "react";
import {
	Clock,
	UserCheck,
	FileText,
	Search,
	MailCheck,
	CalendarCheck,
	Store,
	Presentation,
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { InputText } from "@/components/common/InputText";
import { GeneralSettingsContent } from "./GeneralSettingsContent";
import { OperatingHoursContent } from "./OperatingHoursContent";
import { ScheduleOptimizerContent } from "./ScheduleOptimizerContent";
import { ReminderRulesContent } from "./ReminderRulesContent";
import { CancellationRules } from "./CancellationRules";

interface ScheduleSettingsTabProps {
	className?: string;
}

interface SettingsTabItem {
	id: string;
	label: string;
	icon: React.ComponentType<{ className?: string }>;
}

const tabItems: SettingsTabItem[] = [
	{ id: "general", label: "General", icon: Store },
	{ id: "operating-hours", label: "Operating Hours", icon: Clock },
	{ id: "auto-sign", label: "Auto Sign", icon: UserCheck },
	{ id: "schedule-optimizer", label: "Schedule Optimizer", icon: MailCheck },
	{ id: "intake-fields", label: "Intake Fields", icon: FileText },
	{
		id: "reminder-rules",
		label: "Reminder Rules",
		icon: CalendarCheck,
	},
	{
		id: "cancellation-rules",
		label: "Cancellation Rules",
		icon: CalendarCheck,
	},
	{ id: "banner", label: "Banner", icon: Presentation },
];

export const ScheduleSettingsTab = ({
	className,
}: ScheduleSettingsTabProps) => {
	const [searchTerm, setSearchTerm] = useState("");

	const getTabTitle = (tabId: string) => {
		switch (tabId) {
			case "general":
				return "General Settings";
			case "operating-hours":
				return "Operating Hours";
			case "auto-sign":
				return "Auto Sign";
			case "schedule-optimizer":
				return "Schedule Optimizer";
			case "intake-fields":
				return "Intake Fields";
			case "reminder-rules":
				return "Reminder Rules";
			case "cancellation-rules":
				return "Cancelation Custom Messages & Rules";
			case "banner":
				return "Banner";
			default:
				return "General Settings";
		}
	};

	return (
		<>
			<div className="flex items-center justify-between py-5">
				<h1 className="text-2xl font-bold">
					Organization Schedule Settings
				</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="min-w-[293px] pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
				</div>
			</div>

			<Tabs
				defaultValue="general"
				className={cn(
					"mt-4 flex h-full flex-row overflow-auto",
					className
				)}
			>
				{/* Vertical Sidebar Navigation */}
				<TabsList className="h-fit w-64 flex-col items-stretch justify-start space-y-1 border-r border-gray-200 bg-transparent p-4">
					<div>
						{tabItems.map((item) => {
							const IconComponent = item.icon;
							return (
								<TabsTrigger
									key={item.id}
									value={item.id}
									className={cn(
										"group flex h-auto w-full cursor-pointer items-center justify-start gap-3 rounded-lg px-3 py-2 text-left text-sm font-medium transition-colors",
										"data-[state=active]:bg-sidebar-accent data-[state=active]:text-gray-900 data-[state=active]:shadow-sm",
										"data-[state=inactive]:hover:bg-sidebar-accent data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-900",
										"border-none bg-transparent"
									)}
								>
									<IconComponent
										className={cn(
											"h-4 w-4 transition-colors",
											"group-data-[state=active]:text-[#3B5566]",
											"group-data-[state=inactive]:text-[#8CA3B2]",
											"group-data-[state=inactive]:group-hover:text-[#3B5566]"
										)}
									/>
									{item.label}
								</TabsTrigger>
							);
						})}
					</div>
				</TabsList>

				{/* Main Content Area */}
				<div className="flex flex-1 flex-col">
					<TabsContent value="general" className="m-0 flex-1">
						<div className="px-10.5">
							<div className="border-b border-gray-200 py-4">
								<h1 className="text-2xl font-bold">
									{getTabTitle("general")}
								</h1>
							</div>
							<GeneralSettingsContent />
						</div>
					</TabsContent>

					<TabsContent value="operating-hours" className="m-0 flex-1">
						<div className="px-10.5">
							<div className="border-b border-gray-200 py-4">
								<h1 className="text-2xl font-bold">
									{getTabTitle("operating-hours")}
								</h1>
							</div>
							<OperatingHoursContent />
						</div>
					</TabsContent>

					<TabsContent value="auto-sign" className="m-0 flex-1">
						<div className="px-10.5">
							<div className="border-b border-gray-200 py-4">
								<h1 className="text-2xl font-bold">
									{getTabTitle("auto-sign")}
								</h1>
							</div>
							<div className="p-6">
								Auto Sign content coming soon...
							</div>
						</div>
					</TabsContent>

					<TabsContent
						value="schedule-optimizer"
						className="m-0 flex-1"
					>
						<div className="px-10.5">
							<div className="pt-4 pb-1">
								<h1 className="text-lg font-bold">
									{getTabTitle("schedule-optimizer")}
								</h1>
							</div>
							<ScheduleOptimizerContent />
						</div>
					</TabsContent>

					<TabsContent value="intake-fields" className="m-0 flex-1">
						<div className="px-10.5">
							<div className="border-b border-gray-200 py-4">
								<h1 className="text-2xl font-bold">
									{getTabTitle("intake-fields")}
								</h1>
							</div>
							<div className="p-6">
								Intake Fields content coming soon...
							</div>
						</div>
					</TabsContent>

					<TabsContent value="reminder-rules" className="m-0 flex-1">
						<div className="px-10.5">
							<div className="border-b border-gray-200 py-4">
								<h1 className="text-2xl font-bold">
									{getTabTitle("reminder-rules")}
								</h1>
							</div>
							<ReminderRulesContent />
						</div>
					</TabsContent>

					<TabsContent
						value="cancellation-rules"
						className="m-0 flex-1"
					>
						<div className="px-10.5">
							<div className="pt-4 pb-1">
								<h1 className="text-2xl font-bold">
									{getTabTitle("cancellation-rules")}
								</h1>
							</div>
							<CancellationRules />
						</div>
					</TabsContent>

					<TabsContent value="banner" className="m-0 flex-1">
						<div className="px-10.5">
							<div className="border-b border-gray-200 py-4">
								<h1 className="text-2xl font-bold">
									{getTabTitle("banner")}
								</h1>
							</div>
							<div className="p-6">
								Banner content coming soon...
							</div>
						</div>
					</TabsContent>
				</div>
			</Tabs>
		</>
	);
};
