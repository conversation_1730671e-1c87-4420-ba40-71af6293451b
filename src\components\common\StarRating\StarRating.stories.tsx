import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { StarRating } from "./StarRating";

const meta: Meta<typeof StarRating> = {
	title: "Common/StarRating",
	component: StarRating,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		rating: {
			control: { type: "number", min: 0, max: 5, step: 0.5 },
		},
		maxRating: {
			control: { type: "number", min: 1, max: 10 },
		},
		size: {
			control: { type: "select" },
			options: ["small", "default", "large"],
		},
		color: {
			control: { type: "select" },
			options: ["yellow", "blue", "green", "red", "purple"],
		},
		interactive: {
			control: { type: "boolean" },
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		rating: 4,
		maxRating: 5,
	},
};

export const Small: Story = {
	args: {
		rating: 3.5,
		size: "small",
	},
};

export const Large: Story = {
	args: {
		rating: 4.5,
		size: "large",
	},
};

export const Interactive: Story = {
	args: {
		rating: 3,
		interactive: true,
		onRatingChange: (rating: number) => {
			console.log("Rating changed to:", rating);
		},
	},
};

export const DifferentColors: Story = {
	render: () => (
		<div className="space-y-4">
			<div className="flex items-center gap-4">
				<span className="w-16 text-sm">Yellow:</span>
				<StarRating rating={4} color="yellow" />
			</div>
			<div className="flex items-center gap-4">
				<span className="w-16 text-sm">Blue:</span>
				<StarRating rating={4} color="blue" />
			</div>
			<div className="flex items-center gap-4">
				<span className="w-16 text-sm">Green:</span>
				<StarRating rating={4} color="green" />
			</div>
			<div className="flex items-center gap-4">
				<span className="w-16 text-sm">Red:</span>
				<StarRating rating={4} color="red" />
			</div>
			<div className="flex items-center gap-4">
				<span className="w-16 text-sm">Purple:</span>
				<StarRating rating={4} color="purple" />
			</div>
		</div>
	),
};

export const DifferentSizes: Story = {
	render: () => (
		<div className="space-y-4">
			<div className="flex items-center gap-4">
				<span className="w-16 text-sm">Small:</span>
				<StarRating rating={4} size="small" />
			</div>
			<div className="flex items-center gap-4">
				<span className="w-16 text-sm">Default:</span>
				<StarRating rating={4} size="default" />
			</div>
			<div className="flex items-center gap-4">
				<span className="w-16 text-sm">Large:</span>
				<StarRating rating={4} size="large" />
			</div>
		</div>
	),
};
