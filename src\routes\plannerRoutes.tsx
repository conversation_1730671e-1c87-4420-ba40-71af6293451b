import { useNavigate, useParams } from 'react-router';

import { PlannerDashboard } from '@/pages/Planner/components/PlannerDashboard';
import { LocationSelection } from '@/pages/Planner/components/LocationSelection';
import { ProviderSelection } from '@/pages/Planner/components/ProviderSelection';
import { PreferencesManagement } from '@/pages/Planner/components/PreferenceManagement';
import { AddOrganizationPreference } from '@/pages/Planner/AddOrganizationPreference';
import { AddServicePreference } from '@/pages/Planner/components/AddServicePreference';
import { ServiceRulesList } from '@/pages/Planner/components/ServiceRulesList';
import { RuleConflictsScreen } from '@/pages/Planner/components/RuleConflictsScreen';
import { AppointmentConflictsScreen } from '@/pages/Planner/components/AppointmentConflictsScreen';
// import { SuccessScreen } from '@/pages/Planner/components/SuccessScreen';

// Wrapper components for each route
const PlannerDashboardWrapper = () => {
  const navigate = useNavigate();
  return (
    <PlannerDashboard 
      onSelectLevel={(level) => {
        switch (level) {
          case 'organization':
            navigate('/dashboard/schedule/planner/organization');
            break;
          case 'location':
            navigate('/dashboard/schedule/planner/locations');
            break;
          case 'provider':
            navigate('/dashboard/schedule/planner/providers');
            break;
        }
      }} 
    />
  );
};

const LocationSelectionWrapper = () => {
  const navigate = useNavigate();
  return (
    <LocationSelection 
      onBack={() => navigate('/dashboard/schedule/planner')}
      onSelectLocation={(location) => navigate(`/dashboard/schedule/planner/location/${location.id}`)}
    />
  );
};

const ProviderSelectionWrapper = () => {
  const navigate = useNavigate();
  return (
    <ProviderSelection 
      onBack={() => navigate('/dashboard/schedule/planner')}
      onSelectProvider={(provider) => navigate(`/dashboard/schedule/planner/provider/${provider.id}`)}
    />
  );
};

const OrganizationPreferencesWrapper = () => {
  const navigate = useNavigate();
  return (
    <PreferencesManagement
      level="organization"
      entityName="Organization Name"
      onAddOrganizationPreference={() => navigate('/dashboard/schedule/planner/organization/add-preference')}
      onAddServicePreference={() => navigate('/dashboard/schedule/planner/organization/services/add')}
      onAddCategoryPreference={() => navigate('/dashboard/schedule/planner/organization/categories/add')}
      onEditPreference={(id, type) => {
        switch (type) {
          case 'organization':
            navigate(`/dashboard/schedule/planner/organization/preferences/${id}/edit`);
            break;
          case 'service':
            navigate(`/dashboard/schedule/planner/organization/services/${id}/edit`);
            break;
          case 'category':
            navigate(`/dashboard/schedule/planner/organization/categories/${id}/edit`);
            break;
        }
      }}
      onViewServiceRules={(service) => navigate(`/dashboard/schedule/planner/organization/services/${service.id}/rules`)}
      onBack={() => navigate('/dashboard/schedule/planner')}
    />
  );
};

const LocationPreferencesWrapper = () => {
  const { locationId } = useParams();
  const navigate = useNavigate();
  return (
    <PreferencesManagement
      level="location"
      entityName={`Location ${locationId}`}
      onAddOrganizationPreference={() => navigate(`/dashboard/schedule/planner/location/${locationId}/add-preference`)}
      onAddServicePreference={() => navigate(`/dashboard/schedule/planner/location/${locationId}/services/add`)}
      onAddCategoryPreference={() => navigate(`/dashboard/schedule/planner/location/${locationId}/categories/add`)}
      onEditPreference={(id, type) => {
        switch (type) {
          case 'organization':
            navigate(`/dashboard/schedule/planner/location/${locationId}/preferences/${id}/edit`);
            break;
          case 'service':
            navigate(`/dashboard/schedule/planner/location/${locationId}/services/${id}/edit`);
            break;
          case 'category':
            navigate(`/dashboard/schedule/planner/location/${locationId}/categories/${id}/edit`);
            break;
        }
      }}
      onViewServiceRules={(service) => navigate(`/dashboard/schedule/planner/location/${locationId}/services/${service.id}/rules`)}
      onBack={() => navigate('/dashboard/schedule/planner/locations')}
    />
  );
};

const ProviderPreferencesWrapper = () => {
  const { providerId } = useParams();
  const navigate = useNavigate();
  return (
    <PreferencesManagement
      level="provider"
      entityName={`Provider ${providerId}`}
      onAddOrganizationPreference={() => navigate(`/dashboard/schedule/planner/provider/${providerId}/add-preference`)}
      onAddServicePreference={() => navigate(`/dashboard/schedule/planner/provider/${providerId}/services/add`)}
      onAddCategoryPreference={() => navigate(`/dashboard/schedule/planner/provider/${providerId}/categories/add`)}
      onEditPreference={(id, type) => {
        switch (type) {
          case 'organization':
            navigate(`/dashboard/schedule/planner/provider/${providerId}/preferences/${id}/edit`);
            break;
          case 'service':
            navigate(`/dashboard/schedule/planner/provider/${providerId}/services/${id}/edit`);
            break;
          case 'category':
            navigate(`/dashboard/schedule/planner/provider/${providerId}/categories/${id}/edit`);
            break;
        }
      }}
      onViewServiceRules={(service) => navigate(`/dashboard/schedule/planner/provider/${providerId}/services/${service.id}/rules`)}
      onBack={() => navigate('/dashboard/schedule/planner/providers')}
    />
  );
};

const AddOrganizationPreferenceWrapper = () => {
  const navigate = useNavigate();
  const { level, entityId } = useParams();
  
  const getBackPath = () => {
    switch (level) {
      case 'organization':
        return '/dashboard/schedule/planner/organization';
      case 'location':
        return `/dashboard/schedule/planner/location/${entityId}`;
      case 'provider':
        return `/dashboard/schedule/planner/provider/${entityId}`;
      default:
        return '/dashboard/schedule/planner/organization';
    }
  };
  
  return (
    <AddOrganizationPreference
      onBack={() => navigate(getBackPath())}
      onSave={(data) => {
        console.log('Saving organization preference:', data);
        navigate(getBackPath());
      }}
    />
  );
};

const AddServicePreferenceWrapper = () => {
  const navigate = useNavigate();
  const { level, entityId } = useParams();
  
  const getBackPath = () => {
    switch (level) {
      case 'organization':
        return '/dashboard/schedule/planner/organization';
      case 'location':
        return `/dashboard/schedule/planner/location/${entityId}`;
      case 'provider':
        return `/dashboard/schedule/planner/provider/${entityId}`;
      default:
        return '/dashboard/schedule/planner/organization';
    }
  };
  
  const handleSave = (data: any) => {
    console.log('Saving service preference:', data);
    
      console.log('Checking for conflicts...');
    // Simulate conflict detection
    const hasRuleConflicts = Math.random() > 0.5;
    const hasAppointmentConflicts = Math.random() > 0.5;
    
    const basePath = `/dashboard/schedule/planner/${level}/${entityId || 'organization'}`;
    
    if (hasRuleConflicts) {
      navigate(`${basePath}/services/conflicts`, { state: { preferenceData: data } });
    } else if (hasAppointmentConflicts) {
      navigate(`${basePath}/services/appointment-conflicts`, { state: { preferenceData: data } });
    } else {
      navigate(basePath, { 
        state: { 
          showSuccess: true, 
          preferenceData: data 
        }
      });
    }
  };
  
  return (
    <AddServicePreference
      onBack={() => navigate(getBackPath())}
      onSave={handleSave}
    />
  );
};

const ServiceRulesWrapper = () => {
  const navigate = useNavigate();
  const { level, entityId, serviceId } = useParams();
  
  const getBackPath = () => {
    switch (level) {
      case 'organization':
        return '/dashboard/schedule/planner/organization';
      case 'location':
        return `/dashboard/schedule/planner/location/${entityId}`;
      case 'provider':
        return `/dashboard/schedule/planner/provider/${entityId}`;
      default:
        return '/dashboard/schedule/planner/organization';
    }
  };
  
  // Sample rules data
  const sampleRules = [
    {
      id: '1',
      title: 'Rule Name, here the admin can give this rule a nick name',
      frequency: '10/day',
      occurrence: 'Daily',
      availability: '8:00 am - 8:00 pm',
      timePeriod: '03 Feb 2025 - 31 Dec 2025',
      createdDate: '07 June 2025',
      priority: 1
    },
    {
      id: '2',
      title: 'Another service rule',
      frequency: '15/day',
      occurrence: 'Weekly',
      availability: '9:00 am - 5:00 pm',
      timePeriod: '01 Jan 2025 - 31 Dec 2025',
      createdDate: '05 June 2025',
      priority: 2
    }
  ];
  
  return (
    <ServiceRulesList
      serviceName={`Service ${serviceId}`}
      rules={sampleRules}
      onBack={() => navigate(getBackPath())}
      onAddPreference={() => navigate(`/dashboard/schedule/planner/${level}/${entityId || 'organization'}/services/add`)}
      onEditRule={(ruleId) => console.log('Edit rule:', ruleId)}
      onDeleteRule={(ruleId) => console.log('Delete rule:', ruleId)}
      onReorderRules={(rules) => console.log('Reorder rules:', rules)}
      sortable={true}
    />
  );
};

const ConflictsWrapper = () => {
  const navigate = useNavigate();
  const { level, entityId } = useParams();
  
  const mockConflictData = {
    title: 'New Service Rule',
    frequency: '10/day',
    occurrence: 'Daily',
    availability: '8:00 am - 8:00 pm',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const basePath = `/dashboard/schedule/planner/${level}/${entityId || 'organization'}`;
  
  const handleResolve = () => {
    navigate(basePath, { 
      state: { 
        showSuccess: true, 
        preferenceData: {} 
      }
    });
  };
  
  return (
    <RuleConflictsScreen
      newRule={mockConflictData}
      conflictingRules={[]}
      conflictCount={2}
      onBack={() => navigate(`${basePath}/services/add`)}
      onEdit={() => navigate(`${basePath}/services/add`)}
      onReplace={handleResolve}
      onOverride={handleResolve}
    />
  );
};

const AppointmentConflictsWrapper = () => {
  const navigate = useNavigate();
  const { level, entityId } = useParams();
  
  const mockConflictData = {
    title: 'New Service Rule',
    frequency: '10/day',
    occurrence: 'Daily',
    availability: '8:00 am - 8:00 pm',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const sampleAppointments = [
    {
      id: '1',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    }
  ];
  
  const basePath = `/dashboard/schedule/planner/${level}/${entityId || 'organization'}`;
  
  const handleResolve = () => {
    navigate(basePath, { 
      state: { 
        showSuccess: true, 
        preferenceData: {} 
      }
    });
  };
  
  return (
    <AppointmentConflictsScreen
      newRule={mockConflictData}
      impactedAppointments={sampleAppointments}
      impactCount={sampleAppointments.length}
      onBack={() => navigate(`${basePath}/services/add`)}
      onLetThemBe={handleResolve}
      onCancelAll={handleResolve}
      onRescheduleAll={handleResolve}
      onViewAllAppointments={() => console.log('View all appointments')}
    />
  );
};

// const SuccessWrapper = () => {
//   const navigate = useNavigate();
//   const { level, entityId } = useParams();
  
//   const getBackPath = () => {
//     switch (level) {
//       case 'organization':
//         return '/dashboard/schedule/planner/organization';
//       case 'location':
//         return `/dashboard/schedule/planner/location/${entityId}`;
//       case 'provider':
//         return `/dashboard/schedule/planner/provider/${entityId}`;
//       default:
//         return '/dashboard/schedule/planner/organization';
//     }
//   };
  
//   return (
//     <SuccessScreen
//       serviceName="Service Name"
//       locationName="Location Name"
//       onViewAll={() => navigate(getBackPath())}
//       onAddAnother={() => navigate(`/dashboard/schedule/planner/${level}/${entityId || 'organization'}/services/add`)}
//     />
//   );
// };

const CategoryPlaceholderWrapper = () => {
  const navigate = useNavigate();
  const { level, entityId } = useParams();
  
  const getBackPath = () => {
    switch (level) {
      case 'organization':
        return '/dashboard/schedule/planner/organization';
      case 'location':
        return `/dashboard/schedule/planner/location/${entityId}`;
      case 'provider':
        return `/dashboard/schedule/planner/provider/${entityId}`;
      default:
        return '/dashboard/schedule/planner/organization';
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-semibold mb-4">Category Preferences</h1>
        <p className="text-gray-600 mb-4">Category preference functionality to be implemented</p>
        <button 
          onClick={() => navigate(getBackPath())}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Back
        </button>
      </div>
    </div>
  );
};

// Export the planner routes as an array that can be imported
export const plannerRoutes = [
  // Dashboard
  {
    index: true,
    Component: PlannerDashboardWrapper,
  },
  
  // Selection pages
  {
    path: "locations",
    Component: LocationSelectionWrapper,
  },
  {
    path: "providers",
    Component: ProviderSelectionWrapper,
  },
  
  // Organization routes
  {
    path: "organization",
    Component: OrganizationPreferencesWrapper,
  },
  {
    path: "organization/add-preference",
    Component: AddOrganizationPreferenceWrapper,
  },
  {
    path: "organization/preferences/:id/edit",
    Component: AddOrganizationPreferenceWrapper,
  },
  {
    path: "organization/services/add",
    Component: AddServicePreferenceWrapper,
  },
  {
    path: "organization/services/:id/edit",
    Component: AddServicePreferenceWrapper,
  },
  {
    path: "organization/services/:serviceId/rules",
    Component: ServiceRulesWrapper,
  },
  {
    path: "organization/services/conflicts",
    Component: ConflictsWrapper,
  },
  {
    path: "organization/services/appointment-conflicts",
    Component: AppointmentConflictsWrapper,
  },
  {
    path: "organization/categories/add",
    Component: CategoryPlaceholderWrapper,
  },
  {
    path: "organization/categories/:id/edit",
    Component: CategoryPlaceholderWrapper,
  },
  
  // Location routes
  {
    path: "location/:locationId",
    Component: LocationPreferencesWrapper,
  },
  {
    path: "location/:locationId/add-preference",
    Component: AddOrganizationPreferenceWrapper,
  },
  {
    path: "location/:locationId/preferences/:id/edit",
    Component: AddOrganizationPreferenceWrapper,
  },
  {
    path: "location/:locationId/services/add",
    Component: AddServicePreferenceWrapper,
  },
  {
    path: "location/:locationId/services/:id/edit",
    Component: AddServicePreferenceWrapper,
  },
  {
    path: "location/:locationId/services/:serviceId/rules",
    Component: ServiceRulesWrapper,
  },
  {
    path: "location/:locationId/services/conflicts",
    Component: ConflictsWrapper,
  },
  {
    path: "location/:locationId/services/appointment-conflicts",
    Component: AppointmentConflictsWrapper,
  },
  {
    path: "location/:locationId/categories/add",
    Component: CategoryPlaceholderWrapper,
  },
  {
    path: "location/:locationId/categories/:id/edit",
    Component: CategoryPlaceholderWrapper,
  },
  
  // Provider routes
  {
    path: "provider/:providerId",
    Component: ProviderPreferencesWrapper,
  },
  {
    path: "provider/:providerId/add-preference",
    Component: AddOrganizationPreferenceWrapper,
  },
  {
    path: "provider/:providerId/preferences/:id/edit",
    Component: AddOrganizationPreferenceWrapper,
  },
  {
    path: "provider/:providerId/services/add",
    Component: AddServicePreferenceWrapper,
  },
  {
    path: "provider/:providerId/services/:id/edit",
    Component: AddServicePreferenceWrapper,
  },
  {
    path: "provider/:providerId/services/:serviceId/rules",
    Component: ServiceRulesWrapper,
  },
  {
    path: "provider/:providerId/services/conflicts",
    Component: ConflictsWrapper,
  },
  {
    path: "provider/:providerId/services/appointment-conflicts",
    Component: AppointmentConflictsWrapper,
  },
  {
    path: "provider/:providerId/categories/add",
    Component: CategoryPlaceholderWrapper,
  },
  {
    path: "provider/:providerId/categories/:id/edit",
    Component: CategoryPlaceholderWrapper,
  },
];
