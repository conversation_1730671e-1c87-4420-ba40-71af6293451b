import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	// FormMessage,
} from "@/components/ui/form";

export const DateValidationOptions = ({
	sectionIndex,
	field,
	control,
	watch,
	form,
}: {
	sectionIndex: number;
	field: any;
	control: any;
	watch: any;
	form: any;
}) => {
	const getDateFields = (
		currentSectionIndex: number,
		currentFieldIndex: number
	) => {
		const sections = watch("sections") || [];
		// console.log("sections", sections);
		return sections.flatMap((section: any, sIndex: number) =>
			section.fields
				.map((field: any, fIndex: number) => ({
					id: field.id,
					title: field.title || "Untitled Field",
					sectionIndex: sIndex,
					fieldIndex: fIndex,
				}))
				.filter((field: any) => {
					const fieldData = watch(
						`sections.${field.sectionIndex}.fields.${field.fieldIndex}`
					);
					return (
						(fieldData.type === "date" ||
							fieldData.type === "date_range") &&
						(field.sectionIndex < currentSectionIndex ||
							(field.sectionIndex === currentSectionIndex &&
								field.fieldIndex < currentFieldIndex))
					);
				})
		);
	};

	if (field.value.type !== "date" && field.value.type !== "date_range")
		return null;

	const validationType =
		watch(
			`sections.${sectionIndex}.fields.${field.name}.dateValidation.type`
		) || "static";

	return (
		<div className="mt-4 space-y-4">
			<FormField
				control={control}
				name={`sections.${sectionIndex}.fields.${field.name}.dateValidation.type`}
				render={({ field: typeField }) => (
					<FormItem>
						<FormLabel>Date Validation Type</FormLabel>
						<Select
							onValueChange={(value) => {
								typeField.onChange(value);
								if (value === "relative") {
									form.setValue(
										`sections.${sectionIndex}.fields.${field.name}.dateValidation.minDateOffset`,
										{
											value: 0,
											unit: "days",
											direction: "past",
										}
									);
								}
							}}
							value={typeField.value}
						>
							<SelectTrigger>
								<SelectValue placeholder="Select validation type" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="static">
									Static Dates
								</SelectItem>
								<SelectItem value="relative">
									Relative Dates
								</SelectItem>
								<SelectItem value="dynamic">
									Dynamic (Field Reference)
								</SelectItem>
							</SelectContent>
						</Select>
					</FormItem>
				)}
			/>
			{validationType === "static" && (
				<>
					<FormField
						control={control}
						name={`sections.${sectionIndex}.fields.${field.name}.dateValidation.minDate`}
						render={({ field: dateField }) => (
							<FormItem>
								<FormLabel>Minimum Date</FormLabel>
								<FormControl>
									<Input
										type="date"
										{...dateField}
										value={dateField.value || ""}
									/>
								</FormControl>
							</FormItem>
						)}
					/>
					<FormField
						control={control}
						name={`sections.${sectionIndex}.fields.${field.name}.dateValidation.maxDate`}
						render={({ field: dateField }) => (
							<FormItem>
								<FormLabel>Maximum Date</FormLabel>
								<FormControl>
									<Input
										type="date"
										{...dateField}
										value={dateField.value || ""}
									/>
								</FormControl>
							</FormItem>
						)}
					/>
				</>
			)}
			{validationType === "relative" && (
				<>
					<FormField
						control={control}
						name={`sections.${sectionIndex}.fields.${field.name}.dateValidation.minDateOffset`}
						render={({ field: offsetField }) => (
							<FormItem>
								<FormLabel>Minimum Date Offset</FormLabel>
								<div className="flex gap-2">
									<Input
										type="number"
										placeholder="Value"
										value={offsetField.value?.value || ""}
										onChange={(e) => {
											const value = parseInt(
												e.target.value
											);
											offsetField.onChange({
												...offsetField.value,
												value: isNaN(value) ? 0 : value,
											});
										}}
									/>
									<Select
										value={
											offsetField.value?.unit || "days"
										}
										onValueChange={(unit) => {
											offsetField.onChange({
												...offsetField.value,
												unit,
											});
										}}
									>
										<SelectTrigger>
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="days">
												Days
											</SelectItem>
											<SelectItem value="months">
												Months
											</SelectItem>
											<SelectItem value="years">
												Years
											</SelectItem>
										</SelectContent>
									</Select>
									<Select
										value={
											offsetField.value?.direction ||
											"past"
										}
										onValueChange={(direction) => {
											offsetField.onChange({
												...offsetField.value,
												direction,
											});
										}}
									>
										<SelectTrigger>
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="past">
												Past
											</SelectItem>
											<SelectItem value="future">
												Future
											</SelectItem>
										</SelectContent>
									</Select>
								</div>
							</FormItem>
						)}
					/>
				</>
			)}
			{validationType === "dynamic" && (
				<>
					<FormField
						control={control}
						name={`sections.${sectionIndex}.fields.${field.name}.dateValidation.minDateField`}
						render={({ field: refField }) => (
							<FormItem>
								<FormLabel>
									Reference Field for Minimum Date
								</FormLabel>
								<Select
									onValueChange={refField.onChange}
									value={refField.value || ""}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select field" />
									</SelectTrigger>
									<SelectContent>
										{getDateFields(
											sectionIndex,
											field.name
										).map((dateField: any) => (
											<SelectItem
												key={dateField.id}
												value={dateField.id}
											>
												{`${dateField.title} (Section ${dateField.sectionIndex + 1})`}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormItem>
						)}
					/>
					<FormField
						control={control}
						name={`sections.${sectionIndex}.fields.${field.name}.dateValidation.maxDateField`}
						render={({ field: refField }) => (
							<FormItem>
								<FormLabel>
									Reference Field for Maximum Date
								</FormLabel>
								<Select
									onValueChange={refField.onChange}
									value={refField.value || ""}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select field" />
									</SelectTrigger>
									<SelectContent>
										{getDateFields(
											sectionIndex,
											field.name
										).map((dateField: any) => (
											<SelectItem
												key={dateField.id}
												value={dateField.id}
											>
												{`${dateField.title} (Section ${dateField.sectionIndex + 1})`}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormItem>
						)}
					/>
				</>
			)}
		</div>
	);
};
