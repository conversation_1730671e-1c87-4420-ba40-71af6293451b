import type {
	Control,
	UseFormWatch,
	UseFormSetValue,
	UseFormGetValues,
	UseFormClearErrors,
} from "react-hook-form";
import { z } from "zod";

const baseFieldSchema = z.object({
	id: z.string(),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean().optional(),
	options: z
		.array(
			z.object({
				id: z.string(),
				value: z.string().min(1, "Option value is required"),
				conditions: z
					.object({
						type: z.enum(["continue", "submit", "goto"]),
						destination: z.string(),
						logic: z.enum(["equals", "not_equals"]).optional(),
						selected: z.boolean().optional(),
					})
					.optional(),
			})
		)
		.optional(),
});

const fieldSchemas = {
	text: baseFieldSchema.extend({
		type: z.literal("text"),
	}),

	longtext: baseFieldSchema.extend({
		type: z.literal("longtext"),
	}),

	infoImage: baseFieldSchema.extend({
		type: z.literal("infoImage"),
		image: z.string().min(1, "Image is required"),
		required: z.boolean().optional().default(false),
	}),

	infoText: baseFieldSchema.extend({
		type: z.literal("infoText"),
		info_text_value: z.string().min(1, "Information text is required"),
		required: z.boolean().optional().default(false),
	}),

	attachment: baseFieldSchema.extend({
		type: z.literal("attachment"),
		approved_formats: z
			.array(z.enum(["Png", "Pdf", "Jpeg", "Csv"]))
			.min(1, "At least one file format must be selected"),
	}),

	checkbox: baseFieldSchema.extend({
		type: z.literal("checkbox"),
		options: z
			.array(
				z.object({
					id: z.string(),
					value: z.string().min(1, "Option value is required"),
					conditions: z.object({
						type: z.enum(["continue", "submit", "goto"]),
						destination: z.string(),
						logic: z.enum(["equals", "not_equals"]),
						selected: z.boolean(),
					}),
				})
			)
			.min(1, "At least one option is required"),
	}),

	radio: baseFieldSchema.extend({
		type: z.literal("radio"),
		options: z
			.array(
				z.object({
					id: z.string(),
					value: z.string().min(1, "Option value is required"),
					conditions: z.object({
						type: z.enum(["continue", "submit", "goto"]),
						destination: z.string(),
					}),
				})
			)
			.min(1, "At least one option is required"),
	}),
};

const createOption = (type: string) => ({
	id: crypto.randomUUID(),
	value: "",
	conditions: {
		type: "continue",
		destination: "next",
		...(type === "checkbox"
			? {
					logic: "equals",
					selected: true,
				}
			: {}),
		conditional_block_message:
			"This form submission cannot be processed at this time.",
	},
});

const ApprovedFormats = ["Png", "Pdf", "Jpeg", "Csv"] as const;

const generateUUID = (): UUID => {
	return crypto.randomUUID() as UUID;
};

type UUID = `${string}-${string}-${string}-${string}-${string}`;

type FlowSubmit = {
	action: "submit";
	targetSection?: undefined;
};

type FlowContinue = {
	action: "continue";
	targetSection: UUID;
};

const fieldOptionSchema = z.object({
	id: z.string(),
	value: z.string().min(1, "Option value is required"),
	conditions: z
		.object({
			type: z.enum(["continue", "submit", "goto", "block"]),
			destination: z.string(),
			logic: z.enum(["equals", "not_equals"]).optional(),
			selected: z.boolean().optional(),
			conditional_block_message: z.string().nullable().optional(),
		})
		.optional(),
});

const textFieldSchema = z.object({
	type: z.literal("text"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const numericFieldSchema = z.object({
	type: z.literal("numeric"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const dateFieldSchema = z.object({
	type: z.literal("date"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
	dateValidation: z
		.object({
			type: z.enum(["static", "relative", "dynamic"]),
			minDate: z.string().nullable(),
			maxDate: z.string().nullable(),
			minDateOffset: z
				.object({
					value: z.number(),
					unit: z.enum(["days", "months", "years"]),
					direction: z.enum(["past", "future"]),
				})
				.optional(),
			maxDateOffset: z
				.object({
					value: z.number(),
					unit: z.enum(["days", "months", "years"]),
					direction: z.enum(["past", "future"]),
				})
				.optional(),
			minDateField: z.string().optional(),
			maxDateField: z.string().optional(),
		})
		.optional(),
});

const dateRangeFieldSchema = z.object({
	type: z.literal("date_range"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
	dateValidation: z
		.object({
			type: z.enum(["static", "relative", "dynamic"]),
			minDate: z.string().nullable(),
			maxDate: z.string().nullable(),
			minDateOffset: z
				.object({
					value: z.number(),
					unit: z.enum(["days", "months", "years"]),
					direction: z.enum(["past", "future"]),
				})
				.optional(),
			maxDateOffset: z
				.object({
					value: z.number(),
					unit: z.enum(["days", "months", "years"]),
					direction: z.enum(["past", "future"]),
				})
				.optional(),
			minDateField: z.string().optional(),
			maxDateField: z.string().optional(),
		})
		.optional(),
});

const longtextFieldSchema = z.object({
	type: z.literal("longtext"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z.array(fieldOptionSchema).optional(),
});

const attachmentFieldSchema = z.object({
	type: z.literal("attachment"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	approved_formats: z
		.array(z.enum(ApprovedFormats))
		.min(1, "At least one file format must be selected"),
	options: z.array(fieldOptionSchema).optional(),
});

const infoImageFieldSchema = z.object({
	type: z.literal("infoImage"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	image: z
		.string()
		.nullable()
		.refine((val) => {
			if (!val) return true;
			try {
				new URL(val);
				return true;
			} catch {
				return false;
			}
		}, "Invalid image URL"),
	required: z.boolean().optional().default(false),
	options: z.array(fieldOptionSchema).optional(),
	id: z.string().uuid(),
});

const infoTextFieldSchema = z.object({
	type: z.literal("infoText"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	info_text_value: z
		.string()
		.min(1, "Information text is required")
		.nullable(),
	required: z.boolean().optional(),
	options: z.array(fieldOptionSchema).optional(),
});

const checkboxFieldSchema = z.object({
	type: z.literal("checkbox"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z
		.array(fieldOptionSchema)
		.min(1, "At least one option is required"),
});

const radioFieldSchema = z.object({
	type: z.literal("radio"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z
		.array(fieldOptionSchema)
		.min(1, "At least one option is required"),
});

const dropdownFieldSchema = z.object({
	type: z.literal("dropdown"),
	title: z.string().min(1, "Field title is required"),
	description: z.string().nullable(),
	required: z.boolean(),
	options: z
		.array(fieldOptionSchema)
		.min(1, "At least one option is required"),
});

const formFieldSchema = z
	.discriminatedUnion("type", [
		textFieldSchema,
		numericFieldSchema,
		dateFieldSchema,
		dateRangeFieldSchema,
		longtextFieldSchema,
		attachmentFieldSchema,
		infoImageFieldSchema,
		infoTextFieldSchema,
		checkboxFieldSchema,
		radioFieldSchema,
		dropdownFieldSchema,
	])
	.and(
		z.object({
			id: z.string().uuid(),
			image: z.string().nullable(),
			info_text_value: z.string().nullable(),
			approved_formats: z.array(z.enum(ApprovedFormats)).optional(),
			options: z.array(fieldOptionSchema).optional(),
		})
	);

const uuidSchema = z.custom<UUID>((val) => {
	return (
		typeof val === "string" &&
		/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
			val
		)
	);
}, "Invalid UUID format");

const sectionFlowSchema = z.discriminatedUnion("action", [
	z.object({
		action: z.literal("submit"),
		targetSection: z.undefined(),
	}) satisfies z.ZodType<FlowSubmit>,
	z.object({
		action: z.literal("continue"),
		targetSection: uuidSchema,
	}) satisfies z.ZodType<FlowContinue>,
]);

const formSectionSchema = z.object({
	id: z.string().uuid(),
	title: z.string().min(1, "Section title is required"),
	description: z.string().nullable(),
	flow: sectionFlowSchema,
	fields: z.array(formFieldSchema),
});

const formBuilderSchema = z.object({
	name: z.string().min(1, "Form name is required"),
	logo: z.string().nullable(),
	banner: z.string().nullable(),
	success_message: z.string().min(1, "Success message is required"),
	block_message: z.string().min(1, "Block message is required"),
	submit_button_title: z.string().min(1, "Button action is required"),
	service_id: z.string().nullable(),
	status: z.enum(["live", "draft", "inactive"]),
	sections: z.array(formSectionSchema),
	apply_to_option: z.object({
		apply_to_all: z.number(),
		locations: z.array(
			z
				.object({
					id: z.number().optional(),
					update_location: z.number().optional(),
					apply_to_all_stations: z.number().optional(),
					selected_stations: z.array(z.number()).optional(),
				})
				.optional()
		),
	}),
});

const defaultFieldValues = {
	text: {
		type: "text",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	},

	numeric: {
		type: "numeric",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	},

	date: {
		type: "date",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
		dateValidation: {
			type: "static",
			minDate: null,
			maxDate: null,
		},
	},

	date_range: {
		type: "date_range",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
		dateValidation: {
			type: "static",
			minDate: null,
			maxDate: null,
		},
	},

	longtext: {
		type: "longtext",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	},

	checkbox: () => ({
		type: "checkbox",
		required: false,
		options: [createOption("checkbox")],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	}),

	radio: () => ({
		type: "radio",
		required: false,
		options: [createOption("radio")],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	}),

	dropdown: () => ({
		type: "dropdown",
		required: false,
		options: [createOption("dropdown")],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	}),

	attachment: {
		type: "attachment",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	},

	infoImage: {
		type: "infoImage",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	},

	infoText: {
		type: "infoText",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
	},
};

const createField = (type: string, preserveValues = {}) => {
	const defaults =
		defaultFieldValues[type as keyof typeof defaultFieldValues] ||
		defaultFieldValues.text;
	const baseValues = {
		id: crypto.randomUUID(),
		title: "",
		...(typeof defaults === "function" ? defaults() : defaults),
	};

	// Preserve any existing values while ensuring type-specific defaults
	return {
		...baseValues,
		...preserveValues,
		type, // Ensure type is always set correctly
	};
};

const validateField = (field: any) => {
	const schema = fieldSchemas[field.type as keyof typeof fieldSchemas];
	if (!schema) return true;
	try {
		schema.parse(field);
		return true;
	} catch (error) {
		return false;
	}
};

export type FieldType =
	| "text"
	| "longtext"
	| "numeric"
	| "date"
	| "date_range"
	| "dropdown"
	| "radio"
	| "checkbox"
	| "attachment"
	| "infoImage"
	| "infoText";

export type FieldOption = {
	id: string;
	value: string;
	conditions?: {
		type: "continue" | "submit" | "goto";
		destination: string;
		logic?: "equals" | "not_equals";
		selected?: boolean;
	};
};

type DateValidationType = "static" | "relative" | "dynamic";

export interface DateValidation {
	type: DateValidationType;
	minDate?: string;
	maxDate?: string;
	minDateOffset?: {
		value: number;
		unit: "days" | "months" | "years";
		direction: "past" | "future";
	};
	maxDateOffset?: {
		value: number;
		unit: "days" | "months" | "years";
		direction: "past" | "future";
	};
	minDateField?: string;
	maxDateField?: string;
}

export type FormField = {
	id: string;
	title: string;
	type: FieldType;
	description: string | null;
	required: boolean;
	options?: FieldOption[];
	image?: string | null;
	info_text_value?: string | null;
	approved_formats?: Array<"Png" | "Pdf" | "Jpeg" | "Csv">;
	dateValidation?: DateValidation;
};

export type FormSection = {
	id: string;
	title: string;
	description: string | null;
	fields: FormField[];
	flow: {
		action: "submit" | "continue";
		targetSection?: string;
	};
};

export type FormDataType = {
	name: string;
	logo: string | null;
	banner: string | null;
	success_message: string;
	block_message: string;
	submit_button_title: string;
	service_id?: string | null;
	status: "live" | "draft" | "inactive";
	sections: FormSection[];
	apply_to_option: {
		apply_to_all: number;
		locations: Array<{
			id?: number;
			update_location?: number;
			apply_to_all_stations?: number;
			selected_stations?: number[];
		}>;
	};
};

export interface DraggableFieldProps {
	sectionIndex: number;
	fieldIndex: number;
	field: {
		value: FormField;
		name: number;
		onChange: (value: any) => void;
	};
	control: Control<FormDataType>;
	watch: UseFormWatch<FormDataType>;
	setValue: UseFormSetValue<FormDataType>;
	getValues: UseFormGetValues<FormDataType>;
	clearErrors: UseFormClearErrors<FormDataType>;
	moveField: (
		fromSectionIndex: number,
		fromFieldIndex: number,
		toSectionIndex: number,
		toFieldIndex: number
	) => void;
	hasMultipleFields: boolean;
	isLastField: boolean;
	activeSection: string | null;
	onAddField: (sectionIndex: number, type: FieldType) => void;
	onAddSection: () => void;
	sectionId: string;
	form: any;
}

export {
	fieldSchemas,
	defaultFieldValues,
	formBuilderSchema,
	createField,
	validateField,
	generateUUID,
};

export interface GetFormsResponse {
	forms: FormTypes[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

export interface FormTypes {
	id: string;
	name: string;
	description: string;
	createdAt: string;
	type: "service" | "general" | "intake" | "feedback";
	service: {
		id: string;
		name: string;
	};
	providers: string;
	status: "active" | "inactive" | "draft";
}
