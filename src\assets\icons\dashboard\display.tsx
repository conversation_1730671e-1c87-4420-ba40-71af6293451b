import React from "react";

const DisplayIcon: React.FC = () => {
	return (
		<svg
			width="32"
			height="33"
			viewBox="0 0 32 33"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M18.6665 3.16797H13.3332C8.96086 3.16797 6.77472 3.16797 5.22505 4.25306C4.65172 4.6545 4.15304 5.15318 3.7516 5.72652C2.6665 7.27618 2.6665 9.46233 2.6665 13.8346C2.6665 18.2069 2.6665 20.393 3.7516 21.9428C4.15304 22.5161 4.65172 23.0148 5.22505 23.4162C6.77472 24.5013 8.96086 24.5013 13.3332 24.5013H18.6665C23.0388 24.5013 25.2249 24.5013 26.7746 23.4162C27.348 23.0148 27.8466 22.5161 28.2481 21.9428C29.3332 20.393 29.3332 18.2069 29.3332 13.8346C29.3332 9.46233 29.3332 7.27618 28.2481 5.72652C27.8466 5.15318 27.348 4.6545 26.7746 4.25306C25.2249 3.16797 23.0388 3.16797 18.6665 3.16797Z"
				fill="#C4F6D9"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path
				d="M14.6665 20.5H17.3332"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
			<path
				d="M19.3332 29.8333L18.9125 29.2748C17.9642 28.0159 17.729 26.0925 18.3289 24.5M12.6665 29.8333L13.0872 29.2748C14.0354 28.0159 14.2706 26.0925 13.6708 24.5"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
			/>
			<path
				d="M9.3335 29.832H22.6668"
				stroke="#141B34"
				strokeWidth="2"
				strokeLinecap="round"
			/>
		</svg>
	);
};

export default DisplayIcon;
