import React, { useEffect } from "react";
import { useUIStore } from "@/stores/uiStore";

const DashboardTest: React.FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);

	useEffect(() => {
		const breadcrumbs = [
			{
				label: "Home",
				href: "/",
			},
			{
				label: "Dashboard",
				isCurrentPage: true,
			},
		];

		setBreadcrumbs(breadcrumbs);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	return <div>DashboardTest</div>;
};

export default DashboardTest;
