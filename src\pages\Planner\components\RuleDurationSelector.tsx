import React from 'react';
import { Calendar } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface RuleDurationSelectorProps {
  timePeriodType: 'rolling' | 'specific';
  onTimePeriodTypeChange: (type: 'rolling' | 'specific') => void;
  duration: string;
  onDurationChange: (duration: string) => void;
  durationUnit: string;
  onDurationUnitChange: (unit: string) => void;
  startDate: string;
  onStartDateChange: (date: string) => void;
  endDate: string;
  onEndDateChange: (date: string) => void;
}

export const RuleDurationSelector: React.FC<RuleDurationSelectorProps> = ({
  timePeriodType,
  onTimePeriodTypeChange,
  duration,
  onDurationChange,
  durationUnit,
  onDurationUnitChange,
  startDate,
  onStartDateChange,
  endDate,
  onEndDateChange
}) => {
  return (
    <div className="space-y-4">
      <h3 className="font-medium">Set Rule Duration</h3>
      
      {/* Select Type of Time Period */}
      <div>
        <label className="block text-sm font-medium mb-2">Select Type of Time Period</label>
        <Select 
          value={timePeriodType === 'rolling' ? 'Rolling Duration' : 'Set Specific Date Range'} 
          onValueChange={(value) => onTimePeriodTypeChange(value === 'Rolling Duration' ? 'rolling' : 'specific')}
        >
          <SelectTrigger className="max-w-md">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Rolling Duration">Rolling Duration</SelectItem>
            <SelectItem value="Set Specific Date Range">Set Specific Date Range</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Rolling Duration */}
      {timePeriodType === 'rolling' && (
        <div className="grid grid-cols-2 gap-4 max-w-md">
          <div>
            <label className="block text-sm font-medium mb-2">Select Duration</label>
            <Input 
              value={duration}
              onChange={(e) => onDurationChange(e.target.value)}
              placeholder="20"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">&nbsp;</label>
            <Select value={durationUnit} onValueChange={onDurationUnitChange as any}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Days">Days</SelectItem>
                <SelectItem value="Weeks">Weeks</SelectItem>
                <SelectItem value="Months">Months</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Specific Date Range */}
      {timePeriodType === 'specific' && (
        <div>
          <label className="block text-sm font-medium mb-2">Select Period Range</label>
          <div className="flex items-center space-x-4 max-w-md">
            <div className="flex-1">
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input 
                  type="date"
                  placeholder="Pick a date"
                  value={startDate}
                  onChange={(e) => onStartDateChange(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <span className="text-gray-500 font-medium">To</span>
            <div className="flex-1">
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input 
                  type="date"
                  placeholder="Pick a date"
                  value={endDate}
                  onChange={(e) => onEndDateChange(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};