import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import { microsoftLogin } from "@/lib/api/auth";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

export const useMicrosoftLogin = () => {
	const handleLoginSuccess = useHandleLoginSuccess();

	return useMutation<any, AxiosError, { token: string }>({
		mutationFn: microsoftLogin,
		onSuccess: (data) => {
			handleLoginSuccess(data);
		},
	});
};
