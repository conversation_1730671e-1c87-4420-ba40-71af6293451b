import React, { Suspense, lazy, memo, useMemo } from "react";
import { useUIStore } from "@/stores/uiStore";
import {
	Sheet,
	Sheet<PERSON>ontent,
	She<PERSON><PERSON>eader,
	SheetTitle,
	SheetDescription,
} from "@/components/ui/sheet";

// Define base sheet component props
interface BaseSheetProps {
	data: Record<string, any>;
	onClose: () => void;
	[key: string]: any;
}

// Lazy load sheet components for better performance
const UserProfileSheet = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseSheetProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">User Profile</h3>
				<p>User ID: {data.userId || "Unknown"}</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

const NotificationsSheet = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseSheetProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Notifications</h3>
				<p>Filter: {data.filter || "all"}</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

const SettingsSheet = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseSheetProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Settings</h3>
				<p>Settings sheet placeholder</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

const CustomerDetailsSheet = lazy(() =>
	Promise.resolve({
		default: ({ data, onClose }: BaseSheetProps) => (
			<div className="p-4">
				<h3 className="text-lg font-semibold">Customer Details</h3>
				<p>Customer details sheet placeholder</p>
				<button
					onClick={onClose}
					className="mt-4 rounded bg-blue-500 px-4 py-2 text-white"
				>
					Close
				</button>
			</div>
		),
	})
);

// Sheet Registry with lazy components
const SHEET_COMPONENTS = {
	"user-profile": UserProfileSheet,
	notifications: NotificationsSheet,
	settings: SettingsSheet,
	"customer-details": CustomerDetailsSheet,
} as const;

type SheetId = keyof typeof SHEET_COMPONENTS;

const SIZE_CLASSES = {
	sm: "sm:max-w-sm",
	md: "sm:max-w-md",
	lg: "sm:max-w-lg",
	xl: "sm:max-w-xl",
	full: "sm:max-w-full",
};

// Loading fallback component
const SheetLoadingFallback = memo(() => (
	<div className="flex items-center justify-center p-8">
		<div className="h-6 w-6 animate-spin rounded-full border-b-2 border-gray-900"></div>
		<span className="ml-2">Loading...</span>
	</div>
));
SheetLoadingFallback.displayName = "SheetLoadingFallback";

// Error boundary for sheet components
class SheetErrorBoundary extends React.Component<
	{ children: React.ReactNode; onError?: () => void },
	{ hasError: boolean }
> {
	constructor(props: { children: React.ReactNode; onError?: () => void }) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError() {
		return { hasError: true };
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		console.error("Sheet Error:", error, errorInfo);
		this.props.onError?.();
	}

	render() {
		if (this.state.hasError) {
			return (
				<div className="p-4 text-center">
					<p className="text-red-600">
						Something went wrong loading this sheet.
					</p>
					<button
						onClick={() => this.setState({ hasError: false })}
						className="mt-2 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
					>
						Try again
					</button>
				</div>
			);
		}

		return this.props.children;
	}
}

// Optimized sheet content component
const SheetContentInner = memo(
	({
		sheetId,
		sheet,
		sheetData,
		onClose,
	}: {
		sheetId: string;
		sheet: any;
		sheetData: Record<string, any>;
		onClose: () => void;
	}) => {
		const SheetComponent = SHEET_COMPONENTS[sheetId as SheetId];

		if (!SheetComponent) {
			console.warn(`Sheet component not found for id: ${sheetId}`);
			return (
				<div className="p-4 text-center">
					<p>Sheet component not found: {sheetId}</p>
				</div>
			);
		}

		return (
			<SheetErrorBoundary onError={onClose}>
				<Suspense fallback={<SheetLoadingFallback />}>
					<SheetComponent
						data={sheetData}
						onClose={onClose}
						{...sheet.data}
					/>
				</Suspense>
			</SheetErrorBoundary>
		);
	}
);
SheetContentInner.displayName = "SheetContentInner";

export const SheetManager = memo(() => {
	// Use drawer state and functions but with sheet components
	const {
		activeDrawer: activeSheet,
		drawerData: sheetData,
		drawers: sheets,
		closeDrawer: closeSheet,
	} = useUIStore();

	const currentSheet = useMemo(
		() => sheets.find((s) => s.id === activeSheet),
		[sheets, activeSheet]
	);

	const sizeClass = useMemo(
		() => SIZE_CLASSES[currentSheet?.size || "md"],
		[currentSheet?.size]
	);

	const handleOpenChange = useMemo(
		() => (open: boolean) => {
			if (!open && currentSheet?.dismissible) {
				closeSheet();
			}
		},
		[closeSheet, currentSheet?.dismissible]
	);

	// Map drawer direction to sheet side
	const sheetSide = useMemo(() => {
		switch (currentSheet?.direction) {
			case "left":
				return "left";
			case "right":
				return "right";
			case "top":
				return "top";
			case "bottom":
				return "bottom";
			default:
				return "right";
		}
	}, [currentSheet?.direction]);

	if (!activeSheet || !currentSheet) return null;

	return (
		<Sheet open={!!activeSheet} onOpenChange={handleOpenChange}>
			<SheetContent side={sheetSide} className={sizeClass}>
				<SheetHeader>
					<SheetTitle>
						{currentSheet.data?.title || "Sheet"}
					</SheetTitle>
					<SheetDescription>
						{currentSheet.data?.description || ""}
					</SheetDescription>
				</SheetHeader>
				<div className="flex-1 overflow-auto">
					<SheetContentInner
						sheetId={activeSheet}
						sheet={currentSheet}
						sheetData={sheetData}
						onClose={closeSheet}
					/>
				</div>
			</SheetContent>
		</Sheet>
	);
});

SheetManager.displayName = "SheetManager";
