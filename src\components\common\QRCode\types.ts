export type QRCodeSize = "sm" | "md" | "lg" | "xl";
export type QRCodeLevel = "L" | "M" | "Q" | "H";

export interface ImageSettings {
	src: string;
	height?: number;
	width?: number;
	excavate?: boolean;
	x?: number;
	y?: number;
}

export interface BaseQRCodeProps {
	value: string;
	size?: QRCodeSize | number;
	title?: string;
	bgColor?: string;
	fgColor?: string;
	level?: QRCodeLevel;
	className?: string;
	includeMargin?: boolean;
}

export interface QRCodeProps extends BaseQRCodeProps {
	// Additional custom props can be added here
}

export type QRCodeComponentProps = QRCodeProps;
