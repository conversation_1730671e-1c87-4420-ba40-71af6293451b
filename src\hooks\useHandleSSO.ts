import { useEffect, useState } from "react";
import type { TenantConfig, AuthMethod } from "@/types/tenant";
import type { SSOVerificationResponse } from "@/types/auth";
import { useVerifySSOTenant } from "@/stores/slices/authSlice";

const EXCLUDED_DOMAINS = [
	"localhost",
	"migranium.com",
	"dev.migranium.com",
	"staging.migranium.com",
];

export const useHandleSSO = (initialTenantConfig: TenantConfig) => {
	const [tenantConfig, setTenantConfig] =
		useState<TenantConfig>(initialTenantConfig);
	const [domain, setDomain] = useState<string | null>(null);
	const [shouldVerifySSO, setShouldVerifySSO] = useState(false);

	const {
		data: ssoData,
		isLoading,
		isError,
	} = useVerifySSOTenant(shouldVerifySSO ? domain : null);

	useEffect(() => {
		if (typeof window !== "undefined") {
			const hostname = window.location.hostname;
			const isExcludedDomain = EXCLUDED_DOMAINS.includes(hostname);

			if (!isExcludedDomain) {
				const subdomain = hostname.split(".")[0];
				setDomain(`${subdomain}`);
				setShouldVerifySSO(true);
			} else {
				setShouldVerifySSO(false);
			}
		}
	}, []);

	useEffect(() => {
		if (ssoData?.data) {
			const ssoResponse = ssoData as unknown as SSOVerificationResponse;

			const updatedConfig: TenantConfig = {
				...initialTenantConfig,
				id: String(ssoResponse.data.id),
				name: ssoResponse.data.name,
				subdomain: ssoResponse.data.domain,
				logo: ssoResponse.data.logo_url || null,
				authMethods: updateAuthMethods(
					initialTenantConfig.authMethods,
					ssoResponse.data.sso_enable
				),
				loginType: determineLoginType(
					ssoResponse.data,
					initialTenantConfig
				),
				showPasswordLogin: ssoResponse.data.sso_enable,
				ssoButtonText: ssoResponse.data.sso_enable
					? initialTenantConfig.ssoButtonText || "Sign in with SSO"
					: initialTenantConfig.ssoButtonText,
				showSSOLogin: ssoResponse.data.sso_enable,
				ssoLoginUrl: ssoResponse.data.sso_login_url,
			};

			setTenantConfig(updatedConfig);
		}
	}, [ssoData, initialTenantConfig]);

	const updateAuthMethods = (
		currentMethods: AuthMethod[],
		ssoEnabled: boolean
	): AuthMethod[] => {
		const updatedMethods = [...currentMethods];

		if (ssoEnabled) {
			const ssoAuthMethod = "sso" as unknown as AuthMethod;
			if (!updatedMethods.includes(ssoAuthMethod)) {
				updatedMethods.push(ssoAuthMethod);
			}
		} else {
			const ssoAuthMethod = "sso" as unknown as AuthMethod;
			return updatedMethods.filter((method) => method !== ssoAuthMethod);
		}

		return updatedMethods;
	};

	const determineLoginType = (
		ssoData: SSOVerificationResponse["data"],
		config: TenantConfig
	): "standard" | "custom" => {
		if (ssoData?.domain && ssoData?.name) {
			return "custom";
		}
		return config.loginType || "standard";
	};

	return {
		tenantConfig,
		isLoading,
		isError,
		shouldVerifySSO,
	};
};
