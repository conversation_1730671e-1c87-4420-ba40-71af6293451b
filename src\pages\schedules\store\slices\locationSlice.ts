import { useMutation, useQuery } from "@tanstack/react-query";
import * as HttpQuery from "../../http";
import * as AppointmentTypes from "../../types/Appointment";

/**
 * List appointments
 * @param params - Query parameters
 * @returns List of appointments
 */

export function useListAppointments(
    params: AppointmentTypes.ListAppointmentsQueryParams
) {
    return useQuery({
        queryKey: ['appointments', params],
        queryFn: () => HttpQuery.APIVersion3UseListAppointments(params),
    })
}

/**
 * Create appointment
 * @param data - Appointment data
 * @returns Appointment created response
 */

export function useCreateAppointment() {
    return useMutation({
        mutationFn: ({
            data
        }: {
            data: AppointmentTypes.ActionAppointmentParams;
        }) => HttpQuery.APIVersion3UseCreateAppointment(data),
    })
}

/**
 * Get appointment details
 * @param id - Appointment ID
 * @returns Appointment details
 */

export function useGetAppointmentDetails(
    id: string
) {
    return useQuery({
        queryKey: ['appointment', id],
        queryFn: () => HttpQuery.APIVersion3GetAppointmentDetails(id),
    })
}

/**
 * Update appointment
 * @param id - Appointment ID
 * @param data - Appointment data
 * @returns Appointment updated response
 */

export function useUpdateAppointment() {
    return useMutation({
        mutationFn: ({
            id,
            data
        }: {
            id: string;
            data: AppointmentTypes.ActionAppointmentParams;
        }) => HttpQuery.APIVersion3UpdateAppointment(id, data),
    })
}

/**
 * Delete appointment
 * @param id - Appointment ID
 * @returns Appointment deleted response
 */

export function useDeleteAppointment() {
    return useMutation({
        mutationFn: ({
            id
        }: {
            id: string;
        }) => HttpQuery.APIVersion3DeleteAppointment(id),
    })
}

/**
 * Cancel appointment
 * @param id - Appointment ID
 * @param reason - Reason for cancellation
 * @returns Appointment cancelled response
 */

export function useCancelAppointment() {
    return useMutation({
        mutationFn: ({
            id,
            reason
        }: {
            id: string;
            reason: string;
        }) => HttpQuery.APIVersion3CancelAppointment(id, reason),
    })
}

/**
 * Check in appointment
 * @param id - Appointment ID
 * @returns Appointment checked in response
 */

export function useCheckInAppointment() {
    return useMutation({
        mutationFn: ({
            id
        }: {
            id: string;
        }) => HttpQuery.APIVersion3CheckInAppointment(id),
    })
}

/**
 * Complete appointment
 * @param id - Appointment ID
 * @param notes - Notes for the appointment
 * @returns Appointment completed response
 */

export function useCompleteAppointment(
) {
    return useMutation({    
        mutationFn: ({
            id,
            notes
        }: {
            id: string;
            notes: string;
        }) => HttpQuery.APIVersion3CompleteAppointment(id, notes),
    })
}

/**
 * Reschedule appointment
 * @param id - Appointment ID
 * @param data - Reschedule appointment data
 * @returns Appointment rescheduled response
 */

export function useRescheduleAppointment() {
    return useMutation({
        mutationFn: ({
            id,
            data
        }: {
            id: string;
            data: AppointmentTypes.RescheduleAppointmentParams;
        }) => HttpQuery.APIVersion3RescheduleAppointment(id, data),
    })
}