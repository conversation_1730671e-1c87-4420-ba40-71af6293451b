import { type FC } from "react";
import { Trash2, Send, ChevronRight, Pencil } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import type { Location } from "@/features/locations/types";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export interface FormsCardProps {
	location: Location;
	onEdit?: (location: Location) => void;
	onView?: (location: Location) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
	onSendBookingLink?: () => void;
}

export const FormsCard: React.FC<FormsCardProps> = ({
	location,
	onEdit,
	onView,
	isSelected = false,
	onSelectionChange,
	onSendBookingLink,
}) => {
	return (
		<div
			className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
			onClick={() => onView?.(location)}
		>
			{/* Checkbox Section */}
			<div
				className="flex h-16 items-center px-4"
				onClick={(e) => e.stopPropagation()}
			>
				<Checkbox
					checked={isSelected}
					onCheckedChange={onSelectionChange}
					className="cursor-pointer"
				/>
			</div>

			{/* Form Name Section */}
			<div className="flex flex-2 items-center px-3">
				<h2 className="text-base leading-5 font-medium">
					{location.name}
				</h2>
			</div>

			{/* Status Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className={cn(
						"border-transparent",
						location.isActive
							? "bg-foreground-success text-[#0a2914]"
							: "bg-foreground-warning text-[#0a2914]"
					)}
				>
					{location.isActive ? "Active" : "Inactive"}
				</Badge>
			</div>

			{/* Service Name Section */}
			<div className="flex flex-1 items-center px-3">
				<p className="text-muted leading-5 font-normal">
					Services Name
				</p>
			</div>

			{/* Submissions Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className="bg-foreground-muted border-transparent text-[#0a2914]"
				>
					12
				</Badge>
			</div>

			{/* Type Section */}
			<div className="flex flex-1 items-center px-3">
				<p className="text-muted leading-5 font-normal">Services</p>
			</div>

			{/* Actions Section */}
			<div className="flex flex-1 items-center justify-end px-3">
				<div className="flex items-center gap-2.5">
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onSendBookingLink?.();
							console.log(
								"Send Booking Link action for:",
								location.name
							);
						}}
					>
						<Send className="text-muted h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onEdit?.(location);
						}}
					>
						<Pencil className="text-muted h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							console.log("QR Code action for:", location.name);
						}}
					>
						<Trash2 className="text-muted h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(location);
						}}
					>
						<ChevronRight className="text-muted h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
};
