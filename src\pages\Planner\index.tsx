import React, { useState } from 'react';
import { Routes, Route, useNavigate, useParams, useLocation } from 'react-router';

// Import your components
import { PlannerDashboard } from './components/PlannerDashboard';
import { LocationSelection } from './components/LocationSelection';
import { ProviderSelection } from './components/ProviderSelection';
import { PreferencesManagement } from './components/PreferenceManagement';
import { AddOrganizationPreference } from './AddOrganizationPreference';
import { AddServicePreference } from './components/AddServicePreference';
import { ServiceRulesList } from './components/ServiceRulesList';
import { RuleConflictsScreen } from './components/RuleConflictsScreen';
import { AppointmentConflictsScreen } from './components/AppointmentConflictsScreen';
import { SuccessModal } from './components/SuccessModal';
import { AddCategoryPreference } from './components/AddCategoryPreference';

// Helper hook to get current level and navigation paths
const useCurrentLevel = () => {
  const location = useLocation();
  const params = useParams<{ locationId?: string; providerId?: string }>();
  
  // Determine level based on route structure
  if (location.pathname.includes('/organization')) {
    return {
      level: 'organization' as const,
      entityId: undefined,
      entityName: 'Organization',
      basePath: '/dashboard/schedule/planner/organization'
    };
  } else if (location.pathname.includes('/location/') && params.locationId) {
    return {
      level: 'location' as const,
      entityId: params.locationId,
      entityName: `Location ${params.locationId}`,
      basePath: `/dashboard/schedule/planner/location/${params.locationId}`
    };
  } else if (location.pathname.includes('/provider/') && params.providerId) {
    return {
      level: 'provider' as const,
      entityId: params.providerId,
      entityName: `Provider ${params.providerId}`,
      basePath: `/dashboard/schedule/planner/provider/${params.providerId}`
    };
  }
  
  // Default fallback
  return {
    level: 'organization' as const,
    entityId: undefined,
    entityName: 'Organization',
    basePath: '/dashboard/schedule/planner/organization'
  };
};

// Dashboard Wrapper
const DashboardWrapper = () => {
  const navigate = useNavigate();
  
  return (
    <PlannerDashboard 
      onSelectLevel={(level) => {
        switch (level) {
          case 'organization':
            navigate('organization');
            break;
          case 'location':
            navigate('locations');
            break;
          case 'provider':
            navigate('providers');
            break;
        }
      }} 
    />
  );
};

// Selection Wrappers
const LocationSelectionWrapper = () => {
  const navigate = useNavigate();
  
  return (
    <LocationSelection 
      onBack={() => navigate('..')}
      onSelectLocation={(location) => navigate(`../location/${location.id}`)}
    />
  );
};

const ProviderSelectionWrapper = () => {
  const navigate = useNavigate();
  
  return (
    <ProviderSelection 
      onBack={() => navigate('..')}
      onSelectProvider={(provider) => navigate(`../provider/${provider.id}`)}
    />
  );
};

// Preferences Management Wrappers
const OrganizationPreferencesWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successData, setSuccessData] = useState<any>(null);

  // Check if we should show success modal from navigation state
  React.useEffect(() => {
    if (location.state?.showSuccess && location.state?.preferenceData) {
      setSuccessData(location.state.preferenceData);
      setShowSuccessModal(true);
      // Clear the state to prevent showing again on refresh
      navigate(location.pathname, { replace: true });
    }
  }, [location.state, navigate, location.pathname]);
  
  return (
    <>
      <PreferencesManagement
        level="organization"
        entityName="Organization"
        onAddOrganizationPreference={() => navigate('add-preference')}
        onAddServicePreference={() => navigate('services/add')}
        onAddCategoryPreference={() => navigate('categories/add')}
        onEditPreference={(id, type) => {
          switch (type) {
            case 'organization':
              navigate(`preferences/${id}/edit`);
              break;
            case 'service':
              navigate(`services/${id}/edit`);
              break;
            case 'category':
              navigate(`categories/${id}/edit`);
              break;
          }
        }}
        onViewServiceRules={(service) => navigate(`services/${service.id}/rules`)}
        onBack={() => navigate('..')}
      />
      
      {/* Success Modal */}
      {showSuccessModal && (
        <SuccessModal
          isOpen={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          preferenceName={successData?.title || "Preference"}
          locationName="Organization"
          onViewAll={() => setShowSuccessModal(false)}
          onAddAnother={() => {
            setShowSuccessModal(false);
            navigate('add-preference');
          }}
        />
      )}
    </>
  );
};

const LocationPreferencesWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { locationId } = useParams<{ locationId: string }>();
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successData, setSuccessData] = useState<any>(null);

  // Check if we should show success modal from navigation state
  React.useEffect(() => {
    if (location.state?.showSuccess && location.state?.preferenceData) {
      setSuccessData(location.state.preferenceData);
      setShowSuccessModal(true);
      // Clear the state to prevent showing again on refresh
      navigate(location.pathname, { replace: true });
    }
  }, [location.state, navigate, location.pathname]);
  
  return (
    <>
      <PreferencesManagement
        level="location"
        entityName={`Location ${locationId}`}
        onAddOrganizationPreference={() => navigate('add-preference')}
        onAddServicePreference={() => navigate('services/add')}
        onAddCategoryPreference={() => navigate('categories/add')}
        onEditPreference={(id, type) => {
          switch (type) {
            case 'organization':
              navigate(`preferences/${id}/edit`);
              break;
            case 'service':
              navigate(`services/${id}/edit`);
              break;
            case 'category':
              navigate(`categories/${id}/edit`);
              break;
          }
        }}
        onViewServiceRules={(service) => navigate(`services/${service.id}/rules`)}
        onBack={() => navigate('../../locations')}
      />
      
      {/* Success Modal */}
      {showSuccessModal && (
        <SuccessModal
          isOpen={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          preferenceName={successData?.title || "Preference"}
          locationName={`Location ${locationId}`}
          onViewAll={() => setShowSuccessModal(false)}
          onAddAnother={() => {
            setShowSuccessModal(false);
            navigate('add-preference');
          }}
        />
      )}
    </>
  );
};

const ProviderPreferencesWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { providerId } = useParams<{ providerId: string }>();
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successData, setSuccessData] = useState<any>(null);

  // Check if we should show success modal from navigation state
  React.useEffect(() => {
    if (location.state?.showSuccess && location.state?.preferenceData) {
      setSuccessData(location.state.preferenceData);
      setShowSuccessModal(true);
      // Clear the state to prevent showing again on refresh
      navigate(location.pathname, { replace: true });
    }
  }, [location.state, navigate, location.pathname]);
  
  return (
    <>
      <PreferencesManagement
        level="provider"
        entityName={`Provider ${providerId}`}
        onAddOrganizationPreference={() => navigate('add-preference')}
        onAddServicePreference={() => navigate('services/add')}
        onAddCategoryPreference={() => navigate('categories/add')}
        onEditPreference={(id, type) => {
          switch (type) {
            case 'organization':
              navigate(`preferences/${id}/edit`);
              break;
            case 'service':
              navigate(`services/${id}/edit`);
              break;
            case 'category':
              navigate(`categories/${id}/edit`);
              break;
          }
        }}
        onViewServiceRules={(service) => navigate(`services/${service.id}/rules`)}
        onBack={() => navigate('../../providers')}
      />
      
      {/* Success Modal */}
      {showSuccessModal && (
        <SuccessModal
          isOpen={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          preferenceName={successData?.title || "Preference"}
          locationName={`Provider ${providerId}`}
          onViewAll={() => setShowSuccessModal(false)}
          onAddAnother={() => {
            setShowSuccessModal(false);
            navigate('add-preference');
          }}
        />
      )}
    </>
  );
};

// Organization Preference Wrappers
const AddOrganizationPreferenceWrapper = () => {
  const navigate = useNavigate();
  const { basePath } = useCurrentLevel();
  
  const handleSave = (data: any) => {
    // Log the context-aware data
    console.log(`Saving ${data.contextLevel} preference:`, data);
    
    // Here you would typically make an API call with the context-aware data
    // Example API call structure:
    // await api.savePreference({
    //   ...data,
    //   level: data.contextLevel,
    //   entityId: data.entityId
    // });
    
    // Navigate back to the appropriate dashboard
    navigate(basePath);
  };
  
  return (
    <AddOrganizationPreference
      onBack={() => navigate(basePath)}
      onSave={handleSave}
    />
  );
};

const EditOrganizationPreferenceWrapper = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { basePath } = useCurrentLevel();
  
  return (
    <AddOrganizationPreference
      onBack={() => navigate(basePath)}
      onSave={(data) => {
        console.log('Updating organization preference:', data);
        navigate(basePath);
      }}
      editingId={id}
    />
  );
};

// Organization Conflicts Wrapper - FIXED
const OrganizationConflictsWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { basePath } = useCurrentLevel();
  
  // Get preference data from navigation state
  const preferenceData = location.state?.preferenceData;
  const hasAppointmentConflicts = location.state?.hasAppointmentConflicts;
  
  // Mock conflict data based on preference data
  const newRule = {
    title: preferenceData?.title || 'New Organization Rule',
    frequency: preferenceData?.totalMaxFrequency ? `${preferenceData.totalMaxFrequency}/${preferenceData.frequencyPeriod}` : '25/Monthly',
    occurrence: preferenceData?.occurrence || 'Daily',
    availability: 'Organization-wide',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const conflictingRules = [
    {
      id: '1',
      title: 'Location-Specific Rule: Downtown Clinic',
      frequency: '15/Monthly',
      occurrence: 'Daily',
      availability: '9:00 am - 6:00 pm',
      timePeriod: '01 Jan 2025 - 31 Dec 2025',
      hasTimeConflict: true
    },
    {
      id: '2',
      title: 'Provider Rule: Dr. Smith Availability',
      frequency: '10/Weekly',
      occurrence: 'Weekdays',
      availability: '8:00 am - 4:00 pm',
      timePeriod: '01 Feb 2025 - 30 Jun 2025'
    },
    {
      id: '3',
      title: 'Previous Organization Rule',
      frequency: '30/Monthly',
      occurrence: 'All days',
      availability: '24/7',
      timePeriod: '01 Jan 2025 - 31 Mar 2025',
      hasTimeConflict: true
    }
  ];
  
  const handleResolve = () => {
    if (hasAppointmentConflicts) {
      navigate(`${basePath}/appointment-conflicts`, { 
        state: { preferenceData }
      });
    } else {
      // Navigate back to preferences list and show success there
      navigate(basePath, { 
        state: { 
          showSuccess: true, 
          preferenceData 
        }
      });
    }
  };
  
  return (
    <RuleConflictsScreen
      newRule={newRule}
      conflictingRules={conflictingRules}
      conflictCount={conflictingRules.length}
      onBack={() => navigate(`${basePath}/add-preference`)}
      onEdit={() => navigate(`${basePath}/add-preference`, { 
        state: { editData: preferenceData }
      })}
      onReplace={handleResolve}
      onOverride={handleResolve}
    />
  );
};

// Organization Appointment Conflicts Wrapper - FIXED
const OrganizationAppointmentConflictsWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { basePath } = useCurrentLevel();
  
  // Get preference data from navigation state
  const preferenceData = location.state?.preferenceData;
  
  const newRule = {
    title: preferenceData?.title || 'New Organization Rule',
    frequency: preferenceData?.totalMaxFrequency ? `${preferenceData.totalMaxFrequency}/${preferenceData.frequencyPeriod}` : '25/Monthly',
    occurrence: preferenceData?.occurrence || 'Daily',
    availability: 'Organization-wide',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const impactedAppointments = [
    {
      id: '1',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'Organization Consultation',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '2',
      patientName: 'Sarah Williams',
      providerName: 'Dr. Michael Chen',
      appointmentType: 'Follow-up Appointment',
      date: '15 Apr 2025',
      time: '2:00 pm - 3:00 pm'
    },
    {
      id: '3',
      patientName: 'Robert Johnson',
      providerName: 'Dr. Lisa Brown',
      appointmentType: 'Initial Consultation',
      date: '18 Apr 2025',
      time: '10:00 am - 11:30 am'
    }
  ];
  
  const handleResolve = () => {
    // Navigate back to preferences list and show success there
    navigate(basePath, { 
      state: { 
        showSuccess: true, 
        preferenceData 
      }
    });
  };
  
  return (
    <AppointmentConflictsScreen
      newRule={newRule}
      impactedAppointments={impactedAppointments}
      impactCount={impactedAppointments.length}
      onBack={() => navigate(`${basePath}/conflicts`, { 
        state: { preferenceData }
      })}
      onLetThemBe={handleResolve}
      onCancelAll={handleResolve}
      onRescheduleAll={handleResolve}
      onViewAllAppointments={() => console.log('View all appointments')}
    />
  );
};



// Service Preference Wrappers
const AddServicePreferenceWrapper = () => {
  const navigate = useNavigate();
  const { basePath } = useCurrentLevel();
  
  const handleSave = (data: any) => {
    console.log('Saving service preference:', data);
    
    // Simulate conflict detection
    const hasRuleConflicts = Math.random() > 0.3;
    const hasAppointmentConflicts = Math.random() > 0.5;
    
    if (hasRuleConflicts) {
      navigate(`${basePath}/services/conflicts`, { 
        state: { preferenceData: data }
      });
    } else if (hasAppointmentConflicts) {
      navigate(`${basePath}/services/appointment-conflicts`, { 
        state: { preferenceData: data }
      });
    } else {
      navigate(basePath, { 
        state: { 
          showSuccess: true, 
          preferenceData: data 
        }
      });
    }
  };
  
  return (
    <AddServicePreference
      onBack={() => navigate(basePath)}
      onSave={handleSave}
    />
  );
};

const EditServicePreferenceWrapper = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { basePath } = useCurrentLevel();
  
  const handleSave = (data: any) => {
    console.log('Updating service preference:', data);
    navigate(basePath);
  };
  
  return (
    <AddServicePreference
      onBack={() => navigate(basePath)}
      onSave={handleSave}
    />
  );
};

// Service Rules Wrapper
const ServiceRulesWrapper = () => {
  const navigate = useNavigate();
  const { serviceId } = useParams<{ serviceId: string }>();
  const { basePath } = useCurrentLevel();
  
  const sampleRules = [
    {
      id: '1',
      title: 'Rule Name, here the admin can give this rule a nick name',
      frequency: '10/day',
      occurrence: 'Daily',
      availability: '8:00 am - 8:00 pm',
      timePeriod: '03 Feb 2025 - 31 Dec 2025',
      createdDate: '07 June 2025',
      priority: 1
    },
    {
      id: '2',
      title: 'Another service rule',
      frequency: '15/day',
      occurrence: 'Weekly',
      availability: '9:00 am - 5:00 pm',
      timePeriod: '01 Jan 2025 - 31 Dec 2025',
      createdDate: '05 June 2025',
      priority: 2
    }
  ];
  
  return (
    <ServiceRulesList
      serviceName={`Service ${serviceId}`}
      rules={sampleRules}
      onBack={() => navigate(basePath)}
      onAddPreference={() => navigate(`${basePath}/services/add`)}
      onEditRule={(ruleId) => navigate(`${basePath}/services/${serviceId}/rules/${ruleId}/edit`)}
      onDeleteRule={(ruleId) => console.log('Delete rule:', ruleId)}
      onReorderRules={(rules) => console.log('Reorder rules:', rules)}
      sortable={true}
    />
  );
};

// Conflicts Wrapper
const ConflictsWrapper = () => {
  const navigate = useNavigate();
  const { basePath } = useCurrentLevel();
  
  const mockConflictData = {
    title: 'New Service Rule',
    frequency: '10/day',
    occurrence: 'Daily',
    availability: '8:00 am - 8:00 pm',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const mockConflictingRules = [
    {
      id: '1',
      title: 'Existing Rule 1',
      frequency: '15/day',
      occurrence: 'Daily',
      availability: '9:00 am - 6:00 pm',
      timePeriod: '01 Jan 2025 - 31 Dec 2025',
      hasTimeConflict: true
    },
    {
      id: '2',
      title: 'Existing Rule 2',
      frequency: '20/day',
      occurrence: 'Daily',
      availability: '10:00 am - 7:00 pm',
      timePeriod: '01 Feb 2025 - 31 Dec 2025'
    }
  ];
  
  const handleResolve = () => {
    const hasAppointmentConflicts = Math.random() > 0.5;
    
    if (hasAppointmentConflicts) {
      navigate(`${basePath}/services/appointment-conflicts`);
    } else {
      navigate(basePath, { 
        state: { 
          showSuccess: true, 
          preferenceData: {} 
        }
      });
    }
  };
  
  return (
    <RuleConflictsScreen
      newRule={mockConflictData}
      conflictingRules={mockConflictingRules}
      conflictCount={mockConflictingRules.length}
      onBack={() => navigate(`${basePath}/services/add`)}
      onEdit={() => navigate(`${basePath}/services/add`)}
      onReplace={handleResolve}
      onOverride={handleResolve}
    />
  );
};

// Appointment Conflicts Wrapper
const AppointmentConflictsWrapper = () => {
  const navigate = useNavigate();
  const { basePath } = useCurrentLevel();
  
  const mockConflictData = {
    title: 'New Service Rule',
    frequency: '10/day',
    occurrence: 'Daily',
    availability: '8:00 am - 8:00 pm',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const sampleAppointments = [
    {
      id: '1',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'First CT Scan Appointment',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '2',
      patientName: 'Jane Smith',
      providerName: 'Dr. Sarah Wilson',
      appointmentType: 'Follow-up Consultation',
      date: '15 Apr 2025',
      time: '2:00 pm - 3:00 pm'
    }
  ];
  
  const handleResolve = () => {
    navigate(basePath, { 
      state: { 
        showSuccess: true, 
        preferenceData: {} 
      }
    });
  };
  
  return (
    <AppointmentConflictsScreen
      newRule={mockConflictData}
      impactedAppointments={sampleAppointments}
      impactCount={sampleAppointments.length}
      onBack={() => navigate(`${basePath}/services/add`)}
      onLetThemBe={handleResolve}
      onCancelAll={handleResolve}
      onRescheduleAll={handleResolve}
      onViewAllAppointments={() => console.log('View all appointments')}
    />
  );
};



// Category Placeholder
const CategoryPlaceholder = () => {
  const navigate = useNavigate();
  const { basePath } = useCurrentLevel();
  
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-semibold mb-4">Category Preferences</h1>
        <p className="text-gray-600 mb-4">Category preference functionality to be implemented</p>
        <button 
          onClick={() => navigate(basePath)}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Back
        </button>
      </div>
    </div>
  );
};

// Location Conflicts Wrapper
const LocationConflictsWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { basePath } = useCurrentLevel();
  
  // Get preference data from navigation state
  const preferenceData = location.state?.preferenceData;
  const hasAppointmentConflicts = location.state?.hasAppointmentConflicts;
  
  // Mock conflict data based on preference data - location-scoped
  const newRule = {
    title: preferenceData?.title || 'New Location Rule',
    frequency: preferenceData?.totalMaxFrequency ? `${preferenceData.totalMaxFrequency}/${preferenceData.frequencyPeriod}` : '25/Monthly',
    occurrence: preferenceData?.occurrence || 'Daily',
    availability: 'Location-wide',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const conflictingRules = [
    {
      id: '1',
      title: 'Provider Rule: Dr. Smith Availability',
      frequency: '15/Monthly',
      occurrence: 'Daily',
      availability: '9:00 am - 6:00 pm',
      timePeriod: '01 Jan 2025 - 31 Dec 2025',
      hasTimeConflict: true
    },
    {
      id: '2',
      title: 'Service Rule: Blood Work Schedule',
      frequency: '10/Weekly',
      occurrence: 'Weekdays',
      availability: '8:00 am - 4:00 pm',
      timePeriod: '01 Feb 2025 - 30 Jun 2025'
    },
    {
      id: '3',
      title: 'Previous Location Rule',
      frequency: '30/Monthly',
      occurrence: 'All days',
      availability: '24/7',
      timePeriod: '01 Jan 2025 - 31 Mar 2025',
      hasTimeConflict: true
    }
  ];
  
  const handleResolve = () => {
    if (hasAppointmentConflicts) {
      navigate(`${basePath}/appointment-conflicts`, { 
        state: { preferenceData }
      });
    } else {
      // Navigate back to preferences list and show success there
      navigate(basePath, { 
        state: { 
          showSuccess: true, 
          preferenceData 
        }
      });
    }
  };
  
  return (
    <RuleConflictsScreen
      newRule={newRule}
      conflictingRules={conflictingRules}
      conflictCount={conflictingRules.length}
      onBack={() => navigate(`${basePath}/add-preference`)}
      onEdit={() => navigate(`${basePath}/add-preference`, { 
        state: { editData: preferenceData }
      })}
      onReplace={handleResolve}
      onOverride={handleResolve}
    />
  );
};

// Location Appointment Conflicts Wrapper
const LocationAppointmentConflictsWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { basePath } = useCurrentLevel();
  
  // Get preference data from navigation state
  const preferenceData = location.state?.preferenceData;
  
  const newRule = {
    title: preferenceData?.title || 'New Location Rule',
    frequency: preferenceData?.totalMaxFrequency ? `${preferenceData.totalMaxFrequency}/${preferenceData.frequencyPeriod}` : '25/Monthly',
    occurrence: preferenceData?.occurrence || 'Daily',
    availability: 'Location-wide',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const impactedAppointments = [
    {
      id: '1',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'Location Consultation',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '2',
      patientName: 'Sarah Williams',
      providerName: 'Dr. Michael Chen',
      appointmentType: 'Follow-up Appointment',
      date: '15 Apr 2025',
      time: '2:00 pm - 3:00 pm'
    },
    {
      id: '3',
      patientName: 'Robert Johnson',
      providerName: 'Dr. Lisa Brown',
      appointmentType: 'Initial Consultation',
      date: '18 Apr 2025',
      time: '10:00 am - 11:30 am'
    }
  ];
  
  const handleResolve = () => {
    // Navigate back to preferences list and show success there
    navigate(basePath, { 
      state: { 
        showSuccess: true, 
        preferenceData 
      }
    });
  };
  
  return (
    <AppointmentConflictsScreen
      newRule={newRule}
      impactedAppointments={impactedAppointments}
      impactCount={impactedAppointments.length}
      onBack={() => navigate(`${basePath}/conflicts`, { 
        state: { preferenceData }
      })}
      onLetThemBe={handleResolve}
      onCancelAll={handleResolve}
      onRescheduleAll={handleResolve}
      onViewAllAppointments={() => console.log('View all appointments')}
    />
  );
};

// Add Category Preference Wrapper
const AddCategoryPreferenceWrapper = () => {
  const navigate = useNavigate();
  const { basePath } = useCurrentLevel();

  const handleSave = (data: any) => {
    console.log('Saving category preference:', data);
    
    // Simulate conflict detection
    const hasRuleConflicts = Math.random() > 0.3;
    const hasAppointmentConflicts = Math.random() > 0.5;
    
    if (hasRuleConflicts) {
      navigate(`${basePath}/categories/conflicts`, { 
        state: { preferenceData: data }
      });
    } else if (hasAppointmentConflicts) {
      navigate(`${basePath}/categories/appointment-conflicts`, { 
        state: { preferenceData: data }
      });
    } else {
      // Navigate back to preferences list and show success there
      navigate(basePath, { 
        state: { 
          showSuccess: true, 
          preferenceData: data 
        }
      });
    }
  };

  return (
    <AddCategoryPreference
      onBack={() => navigate(basePath)}
      onSave={handleSave}
    />
  );
};

// Category Conflicts Wrapper
const CategoryConflictsWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { basePath } = useCurrentLevel();
  
  // Get preference data from navigation state
  const preferenceData = location.state?.preferenceData;
  const hasAppointmentConflicts = location.state?.hasAppointmentConflicts;
  
  // Mock conflict data based on preference data - category-scoped
  const newRule = {
    title: preferenceData?.title || 'New Category Rule',
    frequency: preferenceData?.totalMaxFrequency ? `${preferenceData.totalMaxFrequency}/${preferenceData.frequencyPeriod}` : '25/Monthly',
    occurrence: preferenceData?.occurrence || 'Daily',
    availability: 'Category-wide',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const conflictingRules = [
    {
      id: '1',
      title: 'Service-Specific Rule: Blood Test',
      frequency: '15/Monthly',
      occurrence: 'Daily',
      availability: '9:00 am - 6:00 pm',
      timePeriod: '01 Jan 2025 - 31 Dec 2025',
      hasTimeConflict: true
    },
    {
      id: '2',
      title: 'Provider Rule: Dr. Smith Availability',
      frequency: '10/Weekly',
      occurrence: 'Weekdays',
      availability: '8:00 am - 4:00 pm',
      timePeriod: '01 Feb 2025 - 30 Jun 2025'
    },
    {
      id: '3',
      title: 'Previous Category Rule',
      frequency: '30/Monthly',
      occurrence: 'All days',
      availability: '24/7',
      timePeriod: '01 Jan 2025 - 31 Mar 2025',
      hasTimeConflict: true
    }
  ];
  
  const handleResolve = () => {
    if (hasAppointmentConflicts) {
      navigate(`${basePath}/categories/appointment-conflicts`, { 
        state: { preferenceData }
      });
    } else {
      // Navigate back to preferences list and show success there
      navigate(basePath, { 
        state: { 
          showSuccess: true, 
          preferenceData 
        }
      });
    }
  };
  
  return (
    <RuleConflictsScreen
      newRule={newRule}
      conflictingRules={conflictingRules}
      conflictCount={conflictingRules.length}
      onBack={() => navigate(`${basePath}/categories/add`)}
      onEdit={() => navigate(`${basePath}/categories/add`, { 
        state: { editData: preferenceData }
      })}
      onReplace={handleResolve}
      onOverride={handleResolve}
    />
  );
};

// Category Appointment Conflicts Wrapper
const CategoryAppointmentConflictsWrapper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { basePath } = useCurrentLevel();
  
  // Get preference data from navigation state
  const preferenceData = location.state?.preferenceData;
  
  const newRule = {
    title: preferenceData?.title || 'New Category Rule',
    frequency: preferenceData?.totalMaxFrequency ? `${preferenceData.totalMaxFrequency}/${preferenceData.frequencyPeriod}` : '25/Monthly',
    occurrence: preferenceData?.occurrence || 'Daily',
    availability: 'Category-wide',
    timePeriod: '03 Feb 2025 - 31 Dec 2025'
  };
  
  const impactedAppointments = [
    {
      id: '1',
      patientName: 'John Heatherway',
      providerName: 'Dr. Abraham Johnson',
      appointmentType: 'Laboratory Test',
      date: '12 Apr 2025',
      time: '12:30 pm - 4:00 pm'
    },
    {
      id: '2',
      patientName: 'Sarah Williams',
      providerName: 'Dr. Michael Chen',
      appointmentType: 'Blood Test',
      date: '15 Apr 2025',
      time: '2:00 pm - 3:00 pm'
    },
    {
      id: '3',
      patientName: 'Robert Johnson',
      providerName: 'Dr. Lisa Brown',
      appointmentType: 'Urine Test',
      date: '18 Apr 2025',
      time: '10:00 am - 11:30 am'
    }
  ];
  
  const handleResolve = () => {
    // Navigate back to preferences list and show success there
    navigate(basePath, { 
      state: { 
        showSuccess: true, 
        preferenceData 
      }
    });
  };
  
  return (
    <AppointmentConflictsScreen
      newRule={newRule}
      impactedAppointments={impactedAppointments}
      impactCount={impactedAppointments.length}
      onBack={() => navigate(`${basePath}/categories/conflicts`, { 
        state: { preferenceData }
      })}
      onLetThemBe={handleResolve}
      onCancelAll={handleResolve}
      onRescheduleAll={handleResolve}
      onViewAllAppointments={() => console.log('View all appointments')}
    />
  );
};

// Main App Component
export const PlannerApp: React.FC = () => {
  return (
    <Routes>
      {/* Dashboard */}
      <Route index element={<DashboardWrapper />} />
      
      {/* Selection pages */}
      <Route path="locations" element={<LocationSelectionWrapper />} />
      <Route path="providers" element={<ProviderSelectionWrapper />} />
      
      {/* Organization level */}
      <Route path="organization" element={<OrganizationPreferencesWrapper />} />
      <Route path="organization/add-preference" element={<AddOrganizationPreferenceWrapper />} />
      <Route path="organization/preferences/:id/edit" element={<EditOrganizationPreferenceWrapper />} />
      
      {/* Organization Conflicts */}
      <Route path="organization/conflicts" element={<OrganizationConflictsWrapper />} />
      <Route path="organization/appointment-conflicts" element={<OrganizationAppointmentConflictsWrapper />} />
      
      {/* Organization Services */}
      <Route path="organization/services/add" element={<AddServicePreferenceWrapper />} />
      <Route path="organization/services/:id/edit" element={<EditServicePreferenceWrapper />} />
      <Route path="organization/services/:serviceId/rules" element={<ServiceRulesWrapper />} />
      <Route path="organization/services/conflicts" element={<ConflictsWrapper />} />
      <Route path="organization/services/appointment-conflicts" element={<AppointmentConflictsWrapper />} />
      
      {/* Organization Category routes */}
      <Route path="organization/categories/add" element={<AddCategoryPreferenceWrapper />} />
      <Route path="organization/categories/:id/edit" element={<AddCategoryPreferenceWrapper />} />
      <Route path="organization/categories/conflicts" element={<CategoryConflictsWrapper />} />
      <Route path="organization/categories/appointment-conflicts" element={<CategoryAppointmentConflictsWrapper />} />
      
      {/* Location level */}
      <Route path="location/:locationId" element={<LocationPreferencesWrapper />} />
      <Route path="location/:locationId/add-preference" element={<AddOrganizationPreferenceWrapper />} />
      <Route path="location/:locationId/preferences/:id/edit" element={<EditOrganizationPreferenceWrapper />} />
      
      {/* Location Conflicts */}
      <Route path="location/:locationId/conflicts" element={<LocationConflictsWrapper />} />
      <Route path="location/:locationId/appointment-conflicts" element={<LocationAppointmentConflictsWrapper />} />
      
      {/* Location Services */}
      <Route path="location/:locationId/services/add" element={<AddServicePreferenceWrapper />} />
      <Route path="location/:locationId/services/:id/edit" element={<EditServicePreferenceWrapper />} />
      <Route path="location/:locationId/services/:serviceId/rules" element={<ServiceRulesWrapper />} />
      <Route path="location/:locationId/services/conflicts" element={<ConflictsWrapper />} />
      <Route path="location/:locationId/services/appointment-conflicts" element={<AppointmentConflictsWrapper />} />
      
      {/* Location Category routes */}
      <Route path="location/:locationId/categories/add" element={<AddCategoryPreferenceWrapper />} />
      <Route path="location/:locationId/categories/:id/edit" element={<AddCategoryPreferenceWrapper />} />
      <Route path="location/:locationId/categories/conflicts" element={<CategoryConflictsWrapper />} />
      <Route path="location/:locationId/categories/appointment-conflicts" element={<CategoryAppointmentConflictsWrapper />} />
      
      {/* Provider level */}
      <Route path="provider/:providerId" element={<ProviderPreferencesWrapper />} />
      <Route path="provider/:providerId/add-preference" element={<AddOrganizationPreferenceWrapper />} />
      <Route path="provider/:providerId/preferences/:id/edit" element={<EditOrganizationPreferenceWrapper />} />
      
      {/* Provider Conflicts */}
      <Route path="provider/:providerId/conflicts" element={<OrganizationConflictsWrapper />} />
      <Route path="provider/:providerId/appointment-conflicts" element={<OrganizationAppointmentConflictsWrapper />} />
      
      {/* Provider Services */}
      <Route path="provider/:providerId/services/add" element={<AddServicePreferenceWrapper />} />
      <Route path="provider/:providerId/services/:id/edit" element={<EditServicePreferenceWrapper />} />
      <Route path="provider/:providerId/services/:serviceId/rules" element={<ServiceRulesWrapper />} />
      <Route path="provider/:providerId/services/conflicts" element={<ConflictsWrapper />} />
      <Route path="provider/:providerId/services/appointment-conflicts" element={<AppointmentConflictsWrapper />} />
      
      {/* Provider Category routes */}
      <Route path="provider/:providerId/categories/add" element={<AddCategoryPreferenceWrapper />} />
      <Route path="provider/:providerId/categories/:id/edit" element={<AddCategoryPreferenceWrapper />} />
      <Route path="provider/:providerId/categories/conflicts" element={<CategoryConflictsWrapper />} />
      <Route path="provider/:providerId/categories/appointment-conflicts" element={<CategoryAppointmentConflictsWrapper />} />
    </Routes>
  );
};