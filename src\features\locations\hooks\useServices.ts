import { useQuery } from "@tanstack/react-query";
import { servicesApi } from "../api/servicesApi";
import type { GetServicesResponse } from "../api/servicesApi";
import { queryKeys } from "@/lib/query/keys";

interface UseServicesParams {
	organizationId?: number;
	enabled?: boolean;
	filters?: Record<string, any>;
}

export function useServices({
	organizationId,
	enabled = true,
	filters = {},
}: UseServicesParams) {
	return useQuery<GetServicesResponse, Error>({
		queryKey: queryKeys.services?.list
			? queryKeys.services.list(filters)
			: ["services", organizationId, filters],
		queryFn: () => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return servicesApi.getServices(organizationId, filters);
		},
		enabled: enabled && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}
