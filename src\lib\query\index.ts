// Main exports for React Query configuration
export { queryClient } from "./client";
export { queryKeys } from "./keys";
export {
	shortLivedQueryOptions,
	mediumLivedQueryOptions,
	longLivedQueryOptions,
	backgroundRefreshOptions,
	criticalDataOptions,
	defaultMutationOptions,
	criticalMutationOptions,
} from "./options";

// Re-export commonly used React Query hooks and utilities
export {
	useQuery,
	useMutation,
	useQueryClient,
	useInfiniteQuery,
	QueryClientProvider,
	QueryErrorResetBoundary,
} from "@tanstack/react-query";
