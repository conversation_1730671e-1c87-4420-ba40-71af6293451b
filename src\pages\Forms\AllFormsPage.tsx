import { useEffect, useState, type FC } from "react";
import { useUIS<PERSON> } from "@/stores/uiStore";
import { MapPin, Plus, Search, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import type { FormTypes, GetFormsResponse } from "./types";
import { SendFormLinkSheet } from "./sheets";
import { Checkbox } from "@/components/common/Checkbox";
import { AllFormsCard } from "./components";
import { useNavigate } from "react-router";

const formResponse: GetFormsResponse = {
	forms: [
		{
			id: "1",
			name: "Form 1",
			description: "Form 1 description",
			createdAt: "04 Aug 2025",
			type: "service",
			service: {
				id: "1",
				name: "Vaccination",
			},
			providers: "All",
			status: "active",
		},
		{
			id: "2",
			name: "Form 2",
			description: "Form 2 description",
			createdAt: "04 Aug 2025",
			type: "general",
			service: {
				id: "1",
				name: "Vaccination",
			},
			providers: "1",
			status: "draft",
		},
		{
			id: "3",
			name: "Form 3",
			description: "Form 3 description",
			createdAt: "04 Aug 2025",
			type: "intake",
			service: {
				id: "1",
				name: "Vaccination",
			},
			providers: "93",
			status: "active",
		},
		{
			id: "4",
			name: "Form 4",
			description: "Form 4 description",
			createdAt: "04 Aug 2025",
			type: "feedback",
			service: {
				id: "1",
				name: "Vaccination",
			},
			providers: "22",
			status: "inactive",
		},
	],
	pagination: {
		page: 1,
		limit: 10,
		total: 0,
		totalPages: 0,
	},
};

export const AllFormsPage: FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);
	const navigate = useNavigate();
	const [searchTerm, setSearchTerm] = useState("");
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showAddServiceForm, setShowAddServiceForm] = useState(false);
	const [showSendFormLinkSheet, setShowSendFormLinkSheet] = useState(false);
	const [selectedForms, setSelectedForms] = useState<string[]>([]);
	const [currentPage, setCurrentPage] = useState(1);

	const handleSelectAll = (checked: boolean) => {
		if (checked && formResponse.forms) {
			setSelectedForms(formResponse.forms.map((form) => form.id));
		} else {
			setSelectedForms([]);
		}
	};

	const handleFormSelection = (formId: string, selected: boolean) => {
		if (selected) {
			setSelectedForms((prev) => [...prev, formId]);
		} else {
			setSelectedForms((prev) => prev.filter((id) => id !== formId));
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleViewForm = (form: FormTypes) => {
		console.log("View form:", form.id);
		setShowSendFormLinkSheet(true);
	};

	const handleAddForm = (form: FormTypes) => {
		console.log("Add form:", form.id);
		setShowAddServiceForm(true);
	};

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Forms",
				href: "/forms",
			},
		]);

		setCurrentPageTitle("Form Manager");

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, setCurrentPageTitle]);

	return (
		<div className="flex flex-col gap-4 py-6">
			{/* Header */}
			<div className="flex items-center justify-between pl-4">
				<h1 className="text-xl font-semibold">All Forms</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="h-10 cursor-pointer"
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						className="bg-primary hover:bg-primary/90 h-10 cursor-pointer text-white hover:text-white"
						onClick={() => navigate("/dashboard/forms/create")}
					>
						<Plus className="mr-2 h-4 w-4" />
						Create a Form
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="flex w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
				<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4 text-xs">
					<div className="flex items-center pr-4">
						<Checkbox
							label=""
							checked={
								selectedForms.length ===
									formResponse.forms.length &&
								formResponse.forms.length > 0
							}
							className="cursor-pointer"
							onCheckedChange={handleSelectAll}
						/>
					</div>
					<div className="flex flex-2 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Form Name</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Type</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Service</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Providers</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Status</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p>Created At</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p></p>
						</div>
					</div>
				</div>

				{/* Locations Grid */}
				{formResponse.forms && (
					<>
						{formResponse.forms.length === 0 ? (
							<div className="py-12 text-center">
								<MapPin className="mx-auto h-12 w-12 text-gray-400" />
								<h3 className="mt-2 text-sm font-medium text-gray-900">
									No service found
								</h3>
								<p className="mt-1 text-sm text-gray-500">
									Get started by creating your first service.
								</p>
								<Button
									className="mt-4"
									onClick={() => setShowAddServiceForm(true)}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add a Service
								</Button>
							</div>
						) : (
							<div className="flex flex-col gap-0.5">
								{formResponse.forms.map((form: any) => (
									<AllFormsCard
										key={form.id}
										form={form}
										isSelected={selectedForms.includes(
											form.id
										)}
										onSelectionChange={(selected) =>
											handleFormSelection(
												form.id,
												selected
											)
										}
										onEdit={() =>
											console.log(
												"Edit location:",
												form.id
											)
										}
										onView={() => handleViewForm(form)}
									/>
								))}
							</div>
						)}

						{/* Pagination */}
						{formResponse.pagination?.totalPages > 1 && (
							<div className="mt-8 flex items-center justify-center gap-2">
								<Button
									variant="outline"
									disabled={
										formResponse.pagination?.page === 1
									}
									onClick={() =>
										handlePageChange(
											formResponse.pagination?.page - 1
										)
									}
								>
									Previous
								</Button>

								<span className="text-sm text-gray-600">
									Page {formResponse.pagination.page} of{" "}
									{formResponse.pagination?.totalPages}
								</span>

								<Button
									variant="outline"
									disabled={
										formResponse.pagination?.page ===
										formResponse.pagination?.totalPages
									}
									onClick={() =>
										handlePageChange(
											formResponse.pagination?.page + 1
										)
									}
								>
									Next
								</Button>
							</div>
						)}
					</>
				)}
			</div>

			{/* Send Form Link Sheet */}
			<SendFormLinkSheet
				open={showSendFormLinkSheet}
				onOpenChange={setShowSendFormLinkSheet}
			/>
		</div>
	);
};
