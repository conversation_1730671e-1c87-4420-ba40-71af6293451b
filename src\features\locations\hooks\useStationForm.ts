import { useForm } from "react-hook-form";
import type { StationFormData } from "../schemas/stationSchema";
import type { CreateStationRequest } from "../types";

interface StationFormConfig {
	initialData?: Partial<StationFormData>;
	onSubmit?: (data: CreateStationRequest) => Promise<void>;
}

interface StationFormReturn {
	// Custom form methods
	handleMethodToggle: (methodId: string) => void;
	handlePreferenceChange: (field: string, value: boolean) => void;
	validateCurrentStep: (step: number) => Promise<boolean>;
	onFormSubmit: (data: StationFormData) => Promise<void>;

	// Computed properties
	isSubmitting: boolean;
	errors: Record<string, any>;
	formData: StationFormData;
}

const defaultFormData: Partial<StationFormData> = {
	stationName: "",
	description: "",
	autoApprove: false,
	serviceVisibility: true,
	serviceAvailability: true,
	availableMethods: ["in-person"],
	stationDuration: 30,
	durationUnit: "minutes",
	applyStationTo: "all-locations",
	selectedStations: [],
};

// Create form methods
export function createStationForm({
	initialData = {},
	onSubmit,
}: StationFormConfig = {}) {
	const form = useForm<StationFormData>({
		defaultValues: {
			...defaultFormData,
			...initialData,
		},
		mode: "onChange",
	});

	return form;
}

// Hook to access form and custom methods
export function useStationForm({
	initialData = {},
	onSubmit,
}: StationFormConfig = {}): StationFormReturn {
	const form = useForm<StationFormData>({
		defaultValues: {
			...defaultFormData,
			...initialData,
		},
		mode: "onChange",
	});

	const { setValue, watch, trigger, getValues, formState } = form;

	const { errors, isSubmitting } = formState;
	const formData = watch();

	// Handle method toggle
	const handleMethodToggle = (methodId: string) => {
		const currentMethods = getValues("availableMethods");
		const typedMethodId = methodId as "in-person" | "video" | "audio";
		const newMethods = currentMethods.includes(typedMethodId)
			? currentMethods.filter((id) => id !== typedMethodId)
			: [...currentMethods, typedMethodId];

		setValue("availableMethods", newMethods, { shouldValidate: true });
	};

	// Handle preference change
	const handlePreferenceChange = (field: string, value: boolean) => {
		setValue(field as keyof StationFormData, value, {
			shouldValidate: true,
		});
	};

	// Validate current step
	const validateCurrentStep = async (step: number): Promise<boolean> => {
		switch (step) {
			case 1:
				return true; // Location selection is optional
			case 2:
				return await trigger([
					"stationName",
					"description",
					"autoApprove",
					"serviceVisibility",
					"serviceAvailability",
					"availableMethods",
					"stationDuration",
					"durationUnit",
				]);
			case 3:
				return await trigger(["applyStationTo", "selectedStations"]);
			default:
				return false;
		}
	};

	// Form submission handler
	const onFormSubmit = async (data: StationFormData): Promise<void> => {
		try {
			// Transform form data to match CreateStationRequest type
			const requestData: CreateStationRequest = {
				stationName: data.stationName,
				description: data.description,
				autoApprove: data.autoApprove,
				serviceVisibility: data.serviceVisibility,
				serviceAvailability: data.serviceAvailability,
				availableMethods: data.availableMethods,
				stationDuration: data.stationDuration,
				durationUnit: data.durationUnit,
				applyStationTo: data.applyStationTo,
				selectedStations: data.selectedStations,
			};

			await onSubmit?.(requestData);
		} catch (error) {
			console.error("Error submitting station form:", error);
			throw error;
		}
	};

	return {
		// Custom methods
		handleMethodToggle,
		handlePreferenceChange,
		validateCurrentStep,
		onFormSubmit,

		// Computed properties
		isSubmitting,
		errors,
		formData,
	};
}
