import { useState } from "react";
import { X } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Uploader } from "@/components/common/Uploader";
import { getAllCountries } from "@/components/common/InputPhone/utils";
import type { CreateLocationRequest } from "../../types";
import type { UploadedFile } from "@/components/common/Uploader/types";
import { uploadImage } from "@/lib/api/upload";

interface AddLocationSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: CreateLocationRequest & { images?: File[] }) => Promise<void>;
}

export function AddLocationSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddLocationSheetProps) {
	const [formData, setFormData] = useState({
		name: "",
		address: "",
		country: "",
		state: "",
		city: "",
		phone_number: "",
		image: "",
		description: "",
	});
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [isUploading, setIsUploading] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
	const [actualFiles, setActualFiles] = useState<File[]>([]);

	const countries = getAllCountries();

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleImageChange = (files: File[]) => {
		if (files.length > 0) {
			setSelectedFile(files[0]);
			setFormData((prev) => ({ ...prev, image: "" })); // Clear image URL until upload
		} else {
			setSelectedFile(null);
			setFormData((prev) => ({ ...prev, image: "" }));
		}
	};

	const handleFileRemove = (fileId: string) => {
		const fileIndex = uploadedFiles.findIndex((file) => file.id === fileId);
		setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
		setActualFiles((prev) =>
			prev.filter((_, index) => index !== fileIndex)
		);
	};

	const handleFileEdit = (fileId: string, newName: string) => {
		setUploadedFiles((prev) =>
			prev.map((file) =>
				file.id === fileId ? { ...file, name: newName } : file
			)
		);
	};

	const handleSubmit = async () => {
		if (!formData.name || !formData.address) {
			// Basic validation
			return;
		}
		setIsSubmitting(true);
		try {
			let imageUrl = formData.image;
			if (selectedFile) {
				setIsUploading(true);
				console.log(selectedFile)
				imageUrl = await uploadImage(selectedFile);
				setIsUploading(false);
			}
			await onSubmit?.({ ...formData, image: imageUrl });
			// Reset form
			setFormData({
				name: "",
				address: "",
				country: "",
				state: "",
				city: "",
				phone_number: "",
				image: "",
				description: "",
			});
			setSelectedFile(null);
			onOpenChange(false);
		} catch (error) {
			setIsUploading(false);
			console.error("Error submitting location:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleCancel = () => {
		setFormData({
			name: "",
			address: "",
			country: "",
			state: "",
			city: "",
			phone_number: "",
			image: "",
			description: "",
		});
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-8 py-9 sm:max-w-[832px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-lg font-semibold">
							Add New Location
						</SheetTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6 rounded-sm"
						>
							<X className="h-4 w-4" />
							<span className="sr-only">Close</span>
						</Button>
					</div>
				</SheetHeader>

				<div className="flex-1 space-y-3.5 pb-6">
					{/* Location Name */}
					<div className="space-y-2">
						<label className="text-foreground text-sm font-medium">
							Location name{" "}
							<span className="text-red-500">*</span>
						</label>
						<InputText
							placeholder="Enter location name"
							value={formData.name}
							onChange={(e) =>
								handleInputChange("name", e.target.value)
							}
							className="w-full"
							id="location-name"
							variant="default"
						/>
					</div>
					{/* Address */}
					<div className="space-y-2">
						<label className="text-foreground text-sm font-medium">
							Address <span className="text-red-500">*</span>
						</label>
						<InputText
							placeholder="Enter full address"
							value={formData.address}
							onChange={(e) =>
								handleInputChange("address", e.target.value)
							}
							className="w-full"
							id="address"
							variant="default"
						/>
					</div>
					{/* Country, State, City */}
					<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								Country
							</label>
							<InputText
								placeholder="Enter country"
								value={formData.country}
								onChange={(e) =>
									handleInputChange("country", e.target.value)
								}
								className="w-full"
								id="country"
								variant="default"
							/>
						</div>
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								State
							</label>
							<InputText
								placeholder="Enter state"
								value={formData.state}
								onChange={(e) =>
									handleInputChange("state", e.target.value)
								}
								className="w-full"
								id="state"
								variant="default"
							/>
						</div>
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								City
							</label>
							<InputText
								placeholder="Enter city"
								value={formData.city}
								onChange={(e) =>
									handleInputChange("city", e.target.value)
								}
								className="w-full"
								id="city"
								variant="default"
							/>
						</div>
					</div>
					{/* Phone Number */}
					<div className="space-y-2">
						<label className="text-foreground text-sm font-medium">
							Phone Number
						</label>
						<InputText
							type="tel"
							placeholder="Enter phone number"
							value={formData.phone_number}
							onChange={(e) =>
								handleInputChange(
									"phone_number",
									e.target.value
								)
							}
							className="w-full"
							id="phone_number"
							variant="default"
						/>
					</div>
					{/* Image Upload */}
					<div className="space-y-2">
						<Uploader
							files={
								selectedFile
									? [
											{
												id: "1",
												name: selectedFile.name,
												size: selectedFile.size,
												type: selectedFile.type,
											},
										]
									: []
							}
							onFilesChange={handleImageChange}
							onFileRemove={() => handleImageChange([])}
							onFileEdit={() => {}}
							accept=".svg,.png,.jpg,.jpeg"
							maxFileSize={8 * 1024 * 1024}
							uploadText={
								isUploading
									? "Uploading..."
									: "Click or drag file here to upload image"
							}
							descriptionText="Recommended file type: .svg, .png, .jpg (Max of 8 mb)"
							multiple={false}
							maxFiles={1}
							size="default"
							disabled={isUploading || isSubmitting}
						/>
					</div>
					{/* Description */}
					<div className="space-y-2">
						<label className="text-foreground text-sm font-medium">
							Location description
						</label>
						<Textarea
							placeholder="Enter location description"
							value={formData.description}
							onChange={(e) =>
								handleInputChange("description", e.target.value)
							}
							className="min-h-[120px] w-full resize-none"
						/>
					</div>
				</div>

				{/* Footer */}
				<SheetFooter className="mt-6 flex-row justify-end gap-3 p-0">
					<Button
						variant="secondary"
						onClick={handleCancel}
						className="cursor-pointer"
					>
						Close
					</Button>
					<Button
						className="bg-primary hover:bg-primary/90 cursor-pointer"
						onClick={handleSubmit}
						disabled={
							isSubmitting ||
							isUploading ||
							!formData.name ||
							!formData.address
						}
					>
						{isSubmitting || isUploading ? "Saving..." : "Save"}
					</Button>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
