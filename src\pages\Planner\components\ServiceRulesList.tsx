import React, { useState } from 'react';
import { ChevronLeft, Search, SlidersHorizontal, Plus, Edit, Trash2, GripVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'

interface ServiceRule {
  id: string;
  title: string;
  frequency: string;
  occurrence: string;
  availability: string;
  timePeriod: string;
  createdDate: string;
  priority: number;
}

interface ServiceRulesListProps {
  serviceName: string;
  rules: ServiceRule[];
  onBack: () => void;
  onAddPreference: () => void;
  onEditRule: (ruleId: string) => void;
  onDeleteRule: (ruleId: string) => void;
  onReorderRules: (rules: ServiceRule[]) => void;
  sortable?: boolean;
}

export const ServiceRulesList: React.FC<ServiceRulesListProps> = ({
  serviceName,
  rules: initialRules,
  onBack,
  onAddPreference,
  onEditRule,
  onDeleteRule,
  onReorderRules,
  sortable = true
}) => {
  const [rules, setRules] = useState(initialRules);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRules, setSelectedRules] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedRules(rules.map(rule => rule.id));
    } else {
      setSelectedRules([]);
    }
  };

  const handleSelectRule = (ruleId: string, checked: boolean) => {
    if (checked) {
      setSelectedRules(prev => [...prev, ruleId]);
    } else {
      setSelectedRules(prev => prev.filter(id => id !== ruleId));
      setSelectAll(false);
    }
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination || !sortable) return;

    const items = Array.from(rules);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update priority based on new order
    const updatedRules = items.map((rule, index) => ({
      ...rule,
      priority: index + 1
    }));

    setRules(updatedRules);
    onReorderRules(updatedRules);
  };

  const filteredRules = rules.filter(rule =>
    rule.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderRule = (rule: ServiceRule, index: number, isDragging?: boolean) => (
    <div className={`bg-white border rounded-lg p-4 ${isDragging ? 'shadow-lg' : ''}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <Checkbox 
            checked={selectedRules.includes(rule.id)}
            onCheckedChange={(checked) => handleSelectRule(rule.id, checked as boolean)}
          />
          <div className="text-blue-600 font-medium">{String(rule.priority).padStart(2, '0')}</div>
          <h3 className="font-medium">{rule.title}</h3>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Created: {rule.createdDate}</span>
          <div className="w-6 h-6 rounded-full border border-gray-300 flex items-center justify-center">
            <span className="text-xs">?</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Badge variant="outline" className="bg-gray-100">Frequency</Badge>
            <span className="text-sm">{rule.frequency}</span>
          </div>
          <div className="flex space-x-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800">Availability</Badge>
            <span className="text-sm">{rule.availability}</span>
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Badge variant="outline" className="bg-gray-100">Occurrence</Badge>
            <span className="text-sm">{rule.occurrence}</span>
          </div>
          <div className="flex space-x-2">
            <Badge variant="outline" className="bg-gray-100">Time Period</Badge>
            <span className="text-sm">{rule.timePeriod}</span>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        {sortable && (
          <div className="flex items-center space-x-2 text-gray-400">
            <GripVertical className="h-4 w-4" />
            <span className="text-xs">Drag to reorder</span>
          </div>
        )}
        <div className="flex items-center space-x-2 ml-auto">
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 w-8 p-0"
            onClick={() => onEditRule(rule.id)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
            onClick={() => onDeleteRule(rule.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-semibold">{serviceName}</h1>
              <p className="text-sm text-gray-600">Set Priority for [Service Name]</p>
            </div>
          </div>
          <Button variant="outline" className="flex items-center space-x-2">
            <span>View Schedule</span>
          </Button>
        </div>
      </div>

      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {/* Select All */}
            <div className="flex items-center space-x-3">
              <Checkbox 
                checked={selectAll}
                onCheckedChange={handleSelectAll}
              />
              <span className="font-medium">Select All</span>
            </div>
            
            {/* Delete Selected */}
            {selectedRules.length > 0 && (
              <Button 
                variant="destructive" 
                size="sm"
                onClick={() => {
                  selectedRules.forEach(ruleId => onDeleteRule(ruleId));
                  setSelectedRules([]);
                  setSelectAll(false);
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected ({selectedRules.length})
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input 
                placeholder="Search" 
                className="pl-10 w-64"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            {/* Set Priority Toggle */}
            <Button variant="outline" size="sm">
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Set Priority
            </Button>

            {/* Add Preference */}
            <Button 
              onClick={onAddPreference}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Preference
            </Button>
          </div>
        </div>

        {/* Rules List */}
        {filteredRules.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">
              {searchTerm ? 'No rules found matching your search.' : 'No rules added for this service yet.'}
            </div>
            {!searchTerm && (
              <Button 
                onClick={onAddPreference}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add First Preference
              </Button>
            )}
          </div>
        ) : sortable ? (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="rules">
              {(provided: any) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-4"
                >
                  {filteredRules.map((rule, index) => (
                    <Draggable key={rule.id} draggableId={rule.id} index={index}>
                      {(provided: any, snapshot: any) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                        >
                          {renderRule(rule, index, snapshot.isDragging)}
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        ) : (
          <div className="space-y-4">
            {filteredRules.map((rule, index) => (
              <div key={rule.id}>
                {renderRule(rule, index)}
              </div>
            ))}
          </div>
        )}
      </div>

    </div>
  );
};