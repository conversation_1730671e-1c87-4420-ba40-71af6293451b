import { z } from "zod";

export const timeSlotSchema = z.object({
	id: z.string(),
	startTime: z.string().min(1, "Start time is required"),
	endTime: z.string().min(1, "End time is required"),
});

export const dayScheduleSchema = z.object({
	enabled: z.boolean(),
	slots: z.array(timeSlotSchema),
});

export const weeklyScheduleSchema = z.object({
	monday: dayScheduleSchema,
	tuesday: dayScheduleSchema,
	wednesday: dayScheduleSchema,
	thursday: dayScheduleSchema,
	friday: dayScheduleSchema,
	saturday: dayScheduleSchema,
	sunday: dayScheduleSchema,
});

export type TimeSlot = z.infer<typeof timeSlotSchema>;
export type DaySchedule = z.infer<typeof dayScheduleSchema>;
export type WeeklyScheduleData = z.infer<typeof weeklyScheduleSchema>;

// Helper function to convert 12-hour time to minutes for comparison
export function timeToMinutes(timeString: string): number {
	const [time, period] = timeString.split(" ");
	const [hours, minutes] = time.split(":").map(Number);

	let totalMinutes = minutes;
	if (period === "AM") {
		totalMinutes += hours === 12 ? 0 : hours * 60;
	} else {
		totalMinutes += hours === 12 ? 12 * 60 : (hours + 12) * 60;
	}

	return totalMinutes;
}

// Validation function for time slots
export function validateTimeSlots(slots: TimeSlot[]): string[] {
	const errors: string[] = [];

	for (let i = 0; i < slots.length; i++) {
		const slot = slots[i];
		const startMinutes = timeToMinutes(slot.startTime);
		const endMinutes = timeToMinutes(slot.endTime);

		// Check if end time is after start time
		if (endMinutes <= startMinutes) {
			errors.push(`Slot ${i + 1}: End time must be after start time`);
		}

		// Check for overlaps with other slots
		for (let j = i + 1; j < slots.length; j++) {
			const otherSlot = slots[j];
			const otherStartMinutes = timeToMinutes(otherSlot.startTime);
			const otherEndMinutes = timeToMinutes(otherSlot.endTime);

			// Check for overlap
			if (
				(startMinutes < otherEndMinutes &&
					endMinutes > otherStartMinutes) ||
				(otherStartMinutes < endMinutes &&
					otherEndMinutes > startMinutes)
			) {
				errors.push(`Slots ${i + 1} and ${j + 1} overlap`);
			}

			// Check for duplicates
			if (
				slot.startTime === otherSlot.startTime &&
				slot.endTime === otherSlot.endTime
			) {
				errors.push(`Slots ${i + 1} and ${j + 1} are duplicates`);
			}
		}
	}

	return errors;
}
